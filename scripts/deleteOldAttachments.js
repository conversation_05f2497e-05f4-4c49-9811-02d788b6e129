const { Sequelize } = require('sequelize');
const getConnection = require('../src/infra/database/config/dbConnection');
const fs = require('fs');
const path = require('path');

async function deleteOldAttachments(yearMonth) {
  let sequelize;
  try {
    sequelize = await getConnection();
    console.log('Connection has been established successfully.');

    const result = await sequelize.query(
      `SELECT id, path FROM attachments WHERE EXTRACT(YEAR FROM created_at) * 100 + EXTRACT(MONTH FROM created_at) <= :yearMonth;`,
      {
        replacements: { yearMonth },
        type: sequelize.QueryTypes.SELECT,
      },
    );
    const transaction = await sequelize.transaction();

    const promises = result.map(async (attachment) => {
      const filePathToDelete = path.resolve('upload', `.${attachment.path}`);
      const isExistingFile = fs.existsSync(filePathToDelete);
      if (isExistingFile) {
        fs.unlinkSync(filePathToDelete);
      } else {
        console.log('File does not exist:', filePathToDelete);
      }

      await sequelize.query(`DELETE FROM attachments WHERE id = :id`, {
        replacements: { id: attachment.id },
        transaction,
      });
    });

    try {
      await Promise.all(promises);
      await transaction.commit();
      sequelize.close();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
    console.log('Successfully cleaned up old attachments.');
  } catch (error) {
    console.error('Unable to execute script:', error);
    sequelize?.close();
  }
}

// Example usage:
const [, , yearMonth] = process.argv;
deleteOldAttachments(parseInt(yearMonth, 10));
