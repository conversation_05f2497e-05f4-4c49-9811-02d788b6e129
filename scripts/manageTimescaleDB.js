#!/usr/bin/env node

/**
 * TimescaleDB Management Script for PRS Backend
 *
 * This script provides easy management of TimescaleDB features from the backend.
 * It's designed for production use with zero data loss guarantees.
 *
 * Usage:
 *   node scripts/manageTimescaleDB.js status
 *   node scripts/manageTimescaleDB.js check-readiness
 *   node scripts/manageTimescaleDB.js create-hypertable <table_name>
 *   node scripts/manageTimescaleDB.js optimize
 *   node scripts/manageTimescaleDB.js backup
 *
 * <AUTHOR> Development Team
 * @date 2025-06-28
 */

const { Sequelize } = require('sequelize');
const path = require('path');
const fs = require('fs');

// Load database configuration
const config = require('../src/infra/database/config/dbConfig.js');
const env = process.env.NODE_ENV || 'development';
const dbConfig = config[env];

// Initialize Sequelize
const sequelize = new Sequelize(
  dbConfig.database,
  dbConfig.username,
  dbConfig.password,
  {
    host: dbConfig.host,
    port: dbConfig.port,
    dialect: dbConfig.dialect,
    logging: false, // Set to console.log to see SQL queries
    pool: {
      max: 5,
      min: 0,
      acquire: 30000,
      idle: 10000,
    },
  },
);

class TimescaleDBManager {
  constructor() {
    this.sequelize = sequelize;
  }

  async checkConnection() {
    try {
      await this.sequelize.authenticate();
      console.log('✅ Database connection established successfully');
      return true;
    } catch (error) {
      console.error('❌ Unable to connect to database:', error.message);
      return false;
    }
  }

  async getTimescaleDBStatus() {
    try {
      console.log('🔍 Checking TimescaleDB status...\n');

      // Check if TimescaleDB extension is installed
      const [extensionResult] = await this.sequelize.query(`
        SELECT extname, extversion
        FROM pg_extension
        WHERE extname = 'timescaledb';
      `);

      if (extensionResult.length === 0) {
        console.log('❌ TimescaleDB extension not installed');
        return;
      }

      console.log(
        `✅ TimescaleDB ${extensionResult[0].extversion} is installed\n`,
      );

      // Check migration status
      const [migrationStatus] = await this.sequelize.query(`
        SELECT * FROM prs_timescaledb_status ORDER BY table_name;
      `);

      if (migrationStatus.length === 0) {
        console.log(
          '⚠️  No TimescaleDB migration status found. Run the migration first.',
        );
        return;
      }

      console.log('📊 Table Status:');
      console.log('================');

      migrationStatus.forEach((table) => {
        const status = table.is_hypertable_ready
          ? '🚀 Hypertable'
          : '📋 Regular Table';
        const compression = table.compression_enabled
          ? '✅ Compressed'
          : '⏳ No Compression';
        const readiness = table.constraint_migration_needed
          ? '⚠️  Needs Migration'
          : '✅ Ready';

        console.log(`${table.table_name}:`);
        console.log(`  Type: ${status}`);
        console.log(`  Size: ${table.table_size}`);
        console.log(`  Compression: ${compression}`);
        console.log(`  Readiness: ${readiness}`);
        console.log(`  Notes: ${table.notes}`);
        console.log('');
      });

      // Show database size
      const [dbSize] = await this.sequelize.query(`
        SELECT pg_size_pretty(pg_database_size(current_database())) as database_size;
      `);

      console.log(`💾 Total Database Size: ${dbSize[0].database_size}\n`);

      // Show hypertables if any exist
      const [hypertables] = await this.sequelize.query(`
        SELECT hypertable_name, num_chunks, compression_enabled
        FROM timescaledb_information.hypertables;
      `);

      if (hypertables.length > 0) {
        console.log('🚀 Active Hypertables:');
        console.log('=====================');
        hypertables.forEach((ht) => {
          console.log(
            `${ht.hypertable_name}: ${ht.num_chunks} chunks, compression: ${ht.compression_enabled ? 'enabled' : 'disabled'}`,
          );
        });
        console.log('');
      }
    } catch (error) {
      console.error('❌ Error checking TimescaleDB status:', error.message);
    }
  }

  async checkHypertableReadiness(tableName = null) {
    try {
      console.log('🔍 Checking hypertable readiness...\n');

      let query = `
        SELECT table_name, constraint_migration_needed, notes
        FROM timescaledb_migration_status
      `;

      if (tableName) {
        query += ` WHERE table_name = '${tableName}'`;
      }

      query += ` ORDER BY table_name;`;

      const [results] = await this.sequelize.query(query);

      if (results.length === 0) {
        console.log(
          tableName
            ? `❌ Table '${tableName}' not found in migration tracking`
            : '❌ No tables found in migration tracking',
        );
        return;
      }

      console.log('📋 Hypertable Readiness Status:');
      console.log('===============================');

      results.forEach((table) => {
        const status = table.constraint_migration_needed
          ? '⚠️  Requires constraint migration'
          : '✅ Ready for hypertable conversion';

        console.log(`${table.table_name}: ${status}`);
        if (table.constraint_migration_needed) {
          console.log(`  Action needed: ${table.notes}`);
        }
        console.log('');
      });
    } catch (error) {
      console.error('❌ Error checking hypertable readiness:', error.message);
    }
  }

  async createHypertable(tableName) {
    try {
      console.log(`🚀 Attempting to create hypertable for '${tableName}'...\n`);

      const [result] = await this.sequelize.query(`
        SELECT prs_create_hypertable_when_ready('${tableName}') as result;
      `);

      console.log(`📋 Result: ${result[0].result}\n`);

      // Show updated status
      await this.getTimescaleDBStatus();
    } catch (error) {
      console.error(
        `❌ Error creating hypertable for '${tableName}':`,
        error.message,
      );
    }
  }

  async optimizePerformance() {
    try {
      console.log('⚡ Running TimescaleDB performance optimization...\n');

      // Update table statistics
      console.log('📊 Updating table statistics...');
      const tables = [
        'audit_logs',
        'notifications',
        'requisitions',
        'purchase_orders',
        'delivery_receipts',
        'notes',
        'force_close_logs',
      ];

      for (const table of tables) {
        await this.sequelize.query(`ANALYZE ${table};`);
        console.log(`  ✅ Analyzed ${table}`);
      }

      // Refresh continuous aggregates if any exist
      const [aggregates] = await this.sequelize.query(`
        SELECT view_name FROM timescaledb_information.continuous_aggregates;
      `);

      if (aggregates.length > 0) {
        console.log('\n🔄 Refreshing continuous aggregates...');
        for (const agg of aggregates) {
          await this.sequelize.query(
            `CALL refresh_continuous_aggregate('${agg.view_name}', NULL, NULL);`,
          );
          console.log(`  ✅ Refreshed ${agg.view_name}`);
        }
      }

      // Run compression jobs if any exist
      const [compressionJobs] = await this.sequelize.query(`
        SELECT hypertable_name FROM timescaledb_information.compression_settings;
      `);

      if (compressionJobs.length > 0) {
        console.log('\n🗜️  Running compression jobs...');
        for (const job of compressionJobs) {
          await this.sequelize.query(
            `SELECT compress_chunk(chunk) FROM show_chunks('${job.hypertable_name}') AS chunk;`,
          );
          console.log(`  ✅ Compressed chunks for ${job.hypertable_name}`);
        }
      }

      console.log('\n✅ Performance optimization completed!');
    } catch (error) {
      console.error('❌ Error during performance optimization:', error.message);
    }
  }

  async createBackup() {
    try {
      console.log('💾 Creating TimescaleDB backup...\n');

      const timestamp = new Date()
        .toISOString()
        .replace(/[:.]/g, '-')
        .slice(0, 19);
      const backupDir = path.join(__dirname, '..', 'backups');

      // Ensure backup directory exists
      if (!fs.existsSync(backupDir)) {
        fs.mkdirSync(backupDir, { recursive: true });
      }

      const backupFile = path.join(
        backupDir,
        `timescaledb_backup_${timestamp}.sql`,
      );

      // Get database info for backup command
      const dbInfo = {
        host: dbConfig.host || 'localhost',
        port: dbConfig.port || 5432,
        database: dbConfig.database,
        username: dbConfig.username,
        password: dbConfig.password,
      };

      console.log('📋 Backup Information:');
      console.log(`  Database: ${dbInfo.database}`);
      console.log(`  Host: ${dbInfo.host}:${dbInfo.port}`);
      console.log(`  File: ${backupFile}`);
      console.log('');
      console.log('💡 To create the backup, run this command:');
      console.log('');
      console.log(`PGPASSWORD="${dbInfo.password}" pg_dump \\`);
      console.log(`  -h ${dbInfo.host} \\`);
      console.log(`  -p ${dbInfo.port} \\`);
      console.log(`  -U ${dbInfo.username} \\`);
      console.log(`  -d ${dbInfo.database} \\`);
      console.log(`  --verbose \\`);
      console.log(`  --format=plain \\`);
      console.log(`  --inserts \\`);
      console.log(`  > ${backupFile}`);
      console.log('');
      console.log(
        '🔒 This backup will preserve all TimescaleDB metadata and data.',
      );
    } catch (error) {
      console.error('❌ Error preparing backup:', error.message);
    }
  }

  async close() {
    await this.sequelize.close();
  }
}

// CLI Interface
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  const param = args[1];

  const manager = new TimescaleDBManager();

  if (!(await manager.checkConnection())) {
    process.exit(1);
  }

  try {
    switch (command) {
      case 'status':
        await manager.getTimescaleDBStatus();
        break;

      case 'check-readiness':
        await manager.checkHypertableReadiness(param);
        break;

      case 'create-hypertable':
        if (!param) {
          console.error(
            '❌ Please specify a table name: node scripts/manageTimescaleDB.js create-hypertable <table_name>',
          );
          process.exit(1);
        }
        await manager.createHypertable(param);
        break;

      case 'optimize':
        await manager.optimizePerformance();
        break;

      case 'backup':
        await manager.createBackup();
        break;

      default:
        console.log('🚀 TimescaleDB Management Script for PRS Backend\n');
        console.log('Available commands:');
        console.log(
          '  status              - Show TimescaleDB status and table information',
        );
        console.log(
          '  check-readiness     - Check which tables are ready for hypertable conversion',
        );
        console.log(
          '  create-hypertable   - Convert a table to hypertable (if ready)',
        );
        console.log(
          '  optimize            - Run performance optimization tasks',
        );
        console.log(
          '  backup              - Show backup command for TimescaleDB',
        );
        console.log('');
        console.log('Examples:');
        console.log('  node scripts/manageTimescaleDB.js status');
        console.log(
          '  node scripts/manageTimescaleDB.js check-readiness audit_logs',
        );
        console.log(
          '  node scripts/manageTimescaleDB.js create-hypertable audit_logs',
        );
        console.log('  node scripts/manageTimescaleDB.js optimize');
        console.log('  node scripts/manageTimescaleDB.js backup');
    }
  } catch (error) {
    console.error('❌ Command failed:', error.message);
    process.exit(1);
  } finally {
    await manager.close();
  }
}

// Run if called directly
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ Script failed:', error.message);
    process.exit(1);
  });
}

module.exports = TimescaleDBManager;
