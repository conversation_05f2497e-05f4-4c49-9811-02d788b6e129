'use strict';

const { Sequelize } = require('sequelize');
const getConnection = require('../src/infra/database/config/dbConnection');
const fs = require('fs');
const path = require('path');

async function cleanInvoiceReports(requisitionId) {
  if (!requisitionId) {
    console.error('Error: requisition_id parameter is required');
    process.exit(1);
  }

  try {
    const sequelize = await getConnection();
    console.log('Database connection established successfully.');

    console.log(
      `Starting cleanup process for requisition_id: ${requisitionId}`,
    );

    // Begin transaction
    const transaction = await sequelize.transaction();

    try {
      // Get invoice IDs for the requisition
      const invoiceIds = await sequelize.query(
        'SELECT id FROM invoice_reports WHERE requisition_id = :requisitionId',
        {
          replacements: { requisitionId },
          type: Sequelize.QueryTypes.SELECT,
          transaction,
        },
      );

      const ids = invoiceIds.map((invoice) => invoice.id);

      if (ids.length > 0) {
        // Delete invoice history
        await sequelize.query(
          'DELETE FROM invoice_report_histories WHERE invoice_report_id IN (:ids)',
          {
            replacements: { ids },
            transaction,
          },
        );
        console.log('Deleted invoice reports history');

        // Clear invoice_id from delivery receipts
        await sequelize.query(
          'UPDATE delivery_receipts SET invoice_id = NULL WHERE invoice_id IN (:ids)',
          {
            replacements: { ids },
            transaction,
          },
        );
        console.log('Cleared invoice_id from delivery receipts');

        // Get attachment paths before deleting
        const attachments = await sequelize.query(
          'SELECT path FROM attachments WHERE model = :model AND model_id IN (:ids)',
          {
            replacements: { model: 'invoice_report', ids },
            type: Sequelize.QueryTypes.SELECT,
            transaction,
          },
        );

        // Delete attachments records
        await sequelize.query(
          'DELETE FROM attachments WHERE model = :model AND model_id IN (:ids)',
          {
            replacements: { model: 'invoice', ids },
            transaction,
          },
        );
        console.log('Deleted attachment records');

        // Delete physical attachment files
        attachments.forEach((attachment) => {
          const filePath = path.join(process.cwd(), attachment.path);
          if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
          }
        });
        console.log('Deleted physical attachment files');

        // Delete invoice reports
        await sequelize.query(
          'DELETE FROM invoice_reports WHERE id IN (:ids)',
          {
            replacements: { ids },
            transaction,
          },
        );
        console.log('Deleted invoice reports');
      }

      // Commit transaction
      await transaction.commit();
      console.log(
        `Cleanup process completed successfully for requisition_id: ${requisitionId}`,
      );
    } catch (error) {
      // Rollback transaction on error
      await transaction.rollback();
      console.error('Error during cleanup process:', error);
      throw error;
    } finally {
      await sequelize.close();
      console.log('Database connection closed.');
    }
  } catch (error) {
    console.error('Unable to connect to the database:', error);
  }
}

// Check if running directly
if (require.main === module) {
  const requisitionId = process.argv[2];
  if (!requisitionId) {
    console.error('Usage: node cleanInvoiceReports.js <requisition_id>');
    process.exit(1);
  }

  cleanInvoiceReports(requisitionId);
}

module.exports = cleanInvoiceReports;
