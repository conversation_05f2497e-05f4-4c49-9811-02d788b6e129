'use strict';

const { Sequelize } = require('sequelize');
const getConnection = require('../src/infra/database/config/dbConnection');
const path = require('path');
const fs = require('fs');
async function resetPaymentRequestApproval(paymentRequestId) {
  if (!paymentRequestId) {
    console.error('Error: payment_request_id parameter is required');
    process.exit(1);
  }

  try {
    const sequelize = await getConnection();
    console.log('Database connection established successfully.');

    console.log(
      `Starting reset process for payment_request_id: ${paymentRequestId}`,
    );

    // Begin transaction
    const transaction = await sequelize.transaction();

    try {
      // 1. Update payment request status to "For PR Approval"
      await sequelize.query(
        `UPDATE rs_payment_requests SET status = 'For PR Approval' WHERE id = :paymentRequestId`,
        {
          replacements: { paymentRequestId },
          type: Sequelize.QueryTypes.UPDATE,
          transaction,
        },
      );

      // 2. Update associated purchase order status to "For Delivery"
      await sequelize.query(
        `UPDATE purchase_orders SET status = 'for_delivery' WHERE id = (SELECT purchase_order_id FROM rs_payment_requests WHERE id = :paymentRequestId)`,
        {
          replacements: { paymentRequestId },
          type: Sequelize.QueryTypes.UPDATE,
          transaction,
        },
      );

      // 3. Reset approval status of level 2 to pending
      const approvers = await sequelize.query(
        `SELECT * FROM rs_payment_request_approvers WHERE payment_request_id = :paymentRequestId ORDER BY level DESC`,
        {
          replacements: { paymentRequestId },
          type: Sequelize.QueryTypes.SELECT,
          transaction,
        },
      );

      for (const [index, approver] of approvers.entries()) {
        const status = index === 0 ? 'pending' : 'approved';
        await sequelize.query(
          `UPDATE rs_payment_request_approvers SET status = :status WHERE id = :id`,
          {
            replacements: { status, id: approver.id },
            type: Sequelize.QueryTypes.UPDATE,
            transaction,
          },
        );
      }

      const paymentRequest = await sequelize.query(
        `SELECT requisition_id FROM rs_payment_requests WHERE id = :paymentRequestId`,
        {
          replacements: { paymentRequestId },
          type: Sequelize.QueryTypes.SELECT,
          transaction,
        },
      );

      const requisitionId = paymentRequest[0].requisition_id;

      // 4. Remove gate pass attachments
      const attachments = await sequelize.query(
        `SELECT id, path FROM attachments WHERE model = 'requisition' AND model_id = :requisitionId AND file_name LIKE '%Gate Pass%'`,
        {
          replacements: { requisitionId },
          type: Sequelize.QueryTypes.SELECT,
          transaction,
        },
      );

      for (const attachment of attachments) {
        const filePath = path.join(__dirname, '..', '..', attachment.path);
        if (await fs.promises.stat(filePath).catch(() => null)) {
          await fs.promises.unlink(filePath);
        }
        await sequelize.query(`DELETE FROM attachments WHERE id = :id`, {
          replacements: { id: attachment.id },
          type: Sequelize.QueryTypes.DELETE,
          transaction,
        });
      }

      // 5. Remove gate pass
      await sequelize.query(
        `DELETE FROM gate_passes WHERE requisition_id = :requisitionId`,
        {
          replacements: { requisitionId },
          type: Sequelize.QueryTypes.DELETE,
          transaction,
        },
      );

      await transaction.commit();
      console.log('Reset process completed successfully.');
    } catch (error) {
      await transaction.rollback();
      console.error('Error resetting payment request approval:', error);
      process.exit(1);
    }
  } catch (error) {
    console.error('Error resetting payment request approval:', error);
    process.exit(1);
  }
}

if (process.env.NODE_ENV === 'local') {
  const paymentRequestId = process.argv[2];
  resetPaymentRequestApproval(paymentRequestId);
} else {
  console.log('This script is only for local environment');
}
