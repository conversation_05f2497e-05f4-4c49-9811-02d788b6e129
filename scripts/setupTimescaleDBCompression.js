#!/usr/bin/env node

/**
 * TimescaleDB Compression Setup Script
 *
 * This script sets up compression policies for TimescaleDB hypertables
 * to optimize storage and improve query performance for older data.
 *
 * Usage:
 *   node scripts/setupTimescaleDBCompression.js
 *   node scripts/setupTimescaleDBCompression.js --remove
 *   node scripts/setupTimescaleDBCompression.js --status
 *
 * <AUTHOR> Development Team
 * @date 2025-07-14
 */

const { Sequelize } = require('sequelize');
const dbConfig = require('../src/infra/database/config/dbConfig');

// Compression policies configuration
const COMPRESSION_POLICIES = [
  // High-volume tables - compress after 7 days
  {
    table: 'notifications',
    compressAfter: "INTERVAL '7 days'",
    priority: 'HIGH_VOLUME',
  },
  {
    table: 'audit_logs',
    compressAfter: "INTERVAL '7 days'",
    priority: 'HIGH_VOLUME',
  },
  {
    table: 'histories',
    compressAfter: "INTERVAL '7 days'",
    priority: 'HIGH_VOLUME',
  },
  {
    table: 'comments',
    compressAfter: "INTERVAL '7 days'",
    priority: 'HIGH_VOLUME',
  },

  // History tables - compress after 14 days
  {
    table: 'requisition_canvass_histories',
    compressAfter: "INTERVAL '14 days'",
    priority: 'HISTORY',
  },
  {
    table: 'requisition_item_histories',
    compressAfter: "INTERVAL '14 days'",
    priority: 'HISTORY',
  },
  {
    table: 'requisition_order_histories',
    compressAfter: "INTERVAL '14 days'",
    priority: 'HISTORY',
  },
  {
    table: 'requisition_delivery_histories',
    compressAfter: "INTERVAL '14 days'",
    priority: 'HISTORY',
  },
  {
    table: 'requisition_payment_histories',
    compressAfter: "INTERVAL '14 days'",
    priority: 'HISTORY',
  },
  {
    table: 'requisition_return_histories',
    compressAfter: "INTERVAL '14 days'",
    priority: 'HISTORY',
  },
  {
    table: 'delivery_receipt_items_history',
    compressAfter: "INTERVAL '14 days'",
    priority: 'HISTORY',
  },
  {
    table: 'invoice_report_histories',
    compressAfter: "INTERVAL '14 days'",
    priority: 'HISTORY',
  },
  {
    table: 'non_requisition_histories',
    compressAfter: "INTERVAL '14 days'",
    priority: 'HISTORY',
  },

  // Business tables - compress after 30 days
  {
    table: 'requisitions',
    compressAfter: "INTERVAL '30 days'",
    priority: 'BUSINESS',
  },
  {
    table: 'purchase_orders',
    compressAfter: "INTERVAL '30 days'",
    priority: 'BUSINESS',
  },
  {
    table: 'delivery_receipts',
    compressAfter: "INTERVAL '30 days'",
    priority: 'BUSINESS',
  },
  {
    table: 'delivery_receipt_items',
    compressAfter: "INTERVAL '30 days'",
    priority: 'BUSINESS',
  },
  {
    table: 'attachments',
    compressAfter: "INTERVAL '30 days'",
    priority: 'BUSINESS',
  },
  { table: 'notes', compressAfter: "INTERVAL '30 days'", priority: 'BUSINESS' },

  // Workflow tables - compress after 60 days
  {
    table: 'requisition_approvers',
    compressAfter: "INTERVAL '60 days'",
    priority: 'WORKFLOW',
  },
  {
    table: 'requisition_badges',
    compressAfter: "INTERVAL '60 days'",
    priority: 'WORKFLOW',
  },
  {
    table: 'canvass_approvers',
    compressAfter: "INTERVAL '60 days'",
    priority: 'WORKFLOW',
  },
  {
    table: 'canvass_items',
    compressAfter: "INTERVAL '60 days'",
    priority: 'WORKFLOW',
  },
  {
    table: 'canvass_item_suppliers',
    compressAfter: "INTERVAL '60 days'",
    priority: 'WORKFLOW',
  },
  {
    table: 'purchase_order_items',
    compressAfter: "INTERVAL '60 days'",
    priority: 'WORKFLOW',
  },
  {
    table: 'purchase_order_approvers',
    compressAfter: "INTERVAL '60 days'",
    priority: 'WORKFLOW',
  },
];

async function getSequelizeConnection() {
  const config =
    dbConfig[
      process.env.NODE_ENV === 'production' ? 'production' : 'development'
    ];
  const sequelize = new Sequelize(config);

  try {
    await sequelize.authenticate();
    console.log('✅ Database connection established');
    return sequelize;
  } catch (error) {
    console.error('❌ Unable to connect to database:', error.message);
    process.exit(1);
  }
}

async function checkTimescaleDBStatus(sequelize) {
  try {
    const [result] = await sequelize.query(`
      SELECT EXISTS(SELECT 1 FROM pg_extension WHERE extname = 'timescaledb') as enabled;
    `);

    if (!result[0].enabled) {
      console.error('❌ TimescaleDB extension is not enabled');
      process.exit(1);
    }

    console.log('✅ TimescaleDB extension is enabled');
    return true;
  } catch (error) {
    console.error('❌ Error checking TimescaleDB status:', error.message);
    process.exit(1);
  }
}

async function setupCompressionPolicies(sequelize) {
  console.log('🗜️  Setting up compression policies...');
  console.log('');

  let successCount = 0;
  let errorCount = 0;

  for (const policy of COMPRESSION_POLICIES) {
    try {
      // Check if table exists and is a hypertable
      const [isHypertable] = await sequelize.query(`
        SELECT EXISTS (
          SELECT FROM timescaledb_information.hypertables
          WHERE hypertable_name = '${policy.table}' AND hypertable_schema = 'public'
        );
      `);

      if (!isHypertable[0].exists) {
        console.log(`   ⚠️  Skipping ${policy.table} - not a hypertable`);
        continue;
      }

      // Check if compression is already enabled
      const [compressionExists] = await sequelize.query(`
        SELECT EXISTS (
          SELECT FROM timescaledb_information.compression_settings
          WHERE hypertable_name = '${policy.table}' AND hypertable_schema = 'public'
        );
      `);

      if (!compressionExists[0].exists) {
        // Enable compression on the hypertable
        await sequelize.query(`
          ALTER TABLE ${policy.table} SET (
            timescaledb.compress,
            timescaledb.compress_segmentby = 'id'
          );
        `);
        console.log(`   🔧 Enabled compression for ${policy.table}`);
      }

      // Check if compression policy already exists
      const [policyExists] = await sequelize.query(`
        SELECT job_id FROM timescaledb_information.jobs 
        WHERE hypertable_name = '${policy.table}' 
        AND proc_name = 'policy_compression';
      `);

      if (policyExists.length === 0) {
        // Add compression policy
        await sequelize.query(`
          SELECT add_compression_policy('${policy.table}', ${policy.compressAfter});
        `);
        console.log(
          `   ✅ Added compression policy for ${policy.table} (${policy.priority}) - compress after ${policy.compressAfter}`,
        );
      } else {
        console.log(
          `   ℹ️  Compression policy already exists for ${policy.table}`,
        );
      }

      successCount++;
    } catch (error) {
      console.log(
        `   ❌ Failed to setup compression for ${policy.table}: ${error.message}`,
      );
      errorCount++;
    }
  }

  console.log('');
  console.log('🗜️  COMPRESSION SETUP SUMMARY:');
  console.log(`   ✅ Successfully configured: ${successCount} tables`);
  console.log(`   ❌ Errors encountered: ${errorCount} tables`);
  console.log(
    '   💡 Compression will automatically compress old data to save storage',
  );
  console.log('   📊 Estimated storage savings: 60-80% for compressed chunks');
}

async function removeCompressionPolicies(sequelize) {
  console.log('🗑️  Removing compression policies...');
  console.log('');

  let successCount = 0;
  let errorCount = 0;

  for (const policy of COMPRESSION_POLICIES) {
    try {
      // Check if compression policy exists
      const [policyExists] = await sequelize.query(`
        SELECT job_id FROM timescaledb_information.jobs 
        WHERE hypertable_name = '${policy.table}' 
        AND proc_name = 'policy_compression';
      `);

      if (policyExists.length > 0) {
        // Remove compression policy
        await sequelize.query(`
          SELECT remove_compression_policy('${policy.table}');
        `);
        console.log(`   ✅ Removed compression policy for ${policy.table}`);
        successCount++;
      } else {
        console.log(`   ℹ️  No compression policy found for ${policy.table}`);
      }
    } catch (error) {
      console.log(
        `   ❌ Failed to remove compression policy for ${policy.table}: ${error.message}`,
      );
      errorCount++;
    }
  }

  console.log('');
  console.log('🗑️  COMPRESSION REMOVAL SUMMARY:');
  console.log(`   ✅ Successfully removed: ${successCount} policies`);
  console.log(`   ❌ Errors encountered: ${errorCount} policies`);
}

async function showCompressionStatus(sequelize) {
  console.log('📊 TimescaleDB Compression Status');
  console.log('');

  try {
    // Show compression settings
    const [compressionSettings] = await sequelize.query(`
      SELECT 
        cs.hypertable_name,
        cs.compression_enabled,
        cs.segmentby,
        cs.orderby
      FROM timescaledb_information.compression_settings cs
      WHERE cs.hypertable_schema = 'public'
      ORDER BY cs.hypertable_name;
    `);

    if (compressionSettings.length > 0) {
      console.log('🗜️  Tables with compression enabled:');
      compressionSettings.forEach((setting) => {
        console.log(
          `   ✅ ${setting.hypertable_name} - Segment by: ${setting.segmentby || 'none'}`,
        );
      });
    } else {
      console.log('   ℹ️  No tables have compression enabled');
    }

    console.log('');

    // Show compression policies
    const [compressionPolicies] = await sequelize.query(`
      SELECT 
        j.hypertable_name,
        j.schedule_interval,
        j.config->>'compress_after' as compress_after
      FROM timescaledb_information.jobs j
      WHERE j.proc_name = 'policy_compression'
      AND j.hypertable_schema = 'public'
      ORDER BY j.hypertable_name;
    `);

    if (compressionPolicies.length > 0) {
      console.log('⏰ Active compression policies:');
      compressionPolicies.forEach((policy) => {
        console.log(
          `   📅 ${policy.hypertable_name} - Compress after: ${policy.compress_after}`,
        );
      });
    } else {
      console.log('   ℹ️  No compression policies are active');
    }

    console.log('');

    // Show chunk compression status
    const [chunkStatus] = await sequelize.query(`
      SELECT 
        hypertable_name,
        COUNT(*) as total_chunks,
        COUNT(*) FILTER (WHERE is_compressed = true) as compressed_chunks,
        pg_size_pretty(SUM(total_bytes)) as total_size,
        pg_size_pretty(SUM(total_bytes) FILTER (WHERE is_compressed = true)) as compressed_size
      FROM timescaledb_information.chunks
      WHERE hypertable_schema = 'public'
      GROUP BY hypertable_name
      ORDER BY total_chunks DESC;
    `);

    if (chunkStatus.length > 0) {
      console.log('📦 Chunk compression status:');
      chunkStatus.forEach((status) => {
        const compressionRatio =
          status.total_chunks > 0
            ? Math.round((status.compressed_chunks / status.total_chunks) * 100)
            : 0;
        console.log(
          `   📊 ${status.hypertable_name}: ${status.compressed_chunks}/${status.total_chunks} chunks compressed (${compressionRatio}%) - Size: ${status.total_size}`,
        );
      });
    } else {
      console.log('   ℹ️  No chunk information available');
    }
  } catch (error) {
    console.error('❌ Error getting compression status:', error.message);
  }
}

async function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  console.log('🚀 TimescaleDB Compression Management Script');
  console.log('');

  const sequelize = await getSequelizeConnection();
  await checkTimescaleDBStatus(sequelize);

  try {
    switch (command) {
      case '--remove':
        await removeCompressionPolicies(sequelize);
        break;
      case '--status':
        await showCompressionStatus(sequelize);
        break;
      default:
        await setupCompressionPolicies(sequelize);
        break;
    }
  } catch (error) {
    console.error('❌ Script execution failed:', error.message);
    process.exit(1);
  } finally {
    await sequelize.close();
    console.log('');
    console.log('✅ Script completed successfully');
  }
}

// Run the script
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ Unhandled error:', error);
    process.exit(1);
  });
}

module.exports = {
  setupCompressionPolicies,
  removeCompressionPolicies,
  showCompressionStatus,
  COMPRESSION_POLICIES,
};
