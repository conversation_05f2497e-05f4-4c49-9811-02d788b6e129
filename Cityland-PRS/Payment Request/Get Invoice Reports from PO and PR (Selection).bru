meta {
  name: Get Invoice Reports from PO and PR (Selection)
  type: http
  seq: 4
}

get {
  url: {{base_url}}/v1/purchase-orders/:poId/invoice-reports?paymentRequestId=60
  body: none
  auth: inherit
}

params:query {
  paymentRequestId: 60
}

params:path {
  poId: 245
}

body:multipart-form {
  purchaseOrderId: 245
  terms: CIA
  isDraft: false
  requisitionId: 850
  comment: comment 1
  payableDate: 2025-06-27
  discountIn: Fixed Amount
  discountAmount: 0.00
  invoiceIds[0]: 6
}
