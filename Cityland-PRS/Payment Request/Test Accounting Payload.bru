meta {
  name: Test Accounting Payload
  type: http
  seq: 9
}

get {
  url: {{base_url}}/v1/rs-payment-request/:id/test-accounting-payload
  body: none
  auth: inherit
}

params:path {
  id: 152
}

body:multipart-form {
  purchaseOrderId: 245
  terms: CIA
  isDraft: false
  requisitionId: 850
  comment: comment 1
  payableDate: 2025-06-27
  discountIn: Fixed Amount
  discountAmount: 0.00
  invoiceIds[0]: 6
}
