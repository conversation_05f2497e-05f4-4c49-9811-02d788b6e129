meta {
  name: Get PR Purchase Order Details
  type: http
  seq: 8
}

get {
  url: {{base_url}}/v1/rs-payment-request/po-details/:poId
  body: none
  auth: inherit
}

params:query {
  ~paymentRequestId: 60
}

params:path {
  poId: 245
}

body:multipart-form {
  purchaseOrderId: 245
  terms: CIA
  isDraft: false
  requisitionId: 850
  comment: comment 1
  payableDate: 2025-06-27
  discountIn: Fixed Amount
  discountAmount: 0.00
  invoiceIds[0]: 6
}
