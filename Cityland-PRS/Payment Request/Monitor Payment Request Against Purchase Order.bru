meta {
  name: Monitor Payment Request Against Purchase Order
  type: http
  seq: 7
}

get {
  url: {{base_url}}/v1/rs-payment-request/monitoring/:purchaseOrderId
  body: json
  auth: inherit
}

params:path {
  purchaseOrderId: 191
}

body:json {
  {}
}

body:multipart-form {
  purchaseOrderId: 245
  terms: CIA
  isDraft: false
  requisitionId: 850
  comment: comment 1
  payableDate: 2025-06-27
  discountIn: Fixed Amount
  discountAmount: 0.00
  invoiceIds[0]: 6
}
