meta {
  name: Download OFM Items
  type: http
  seq: 3
}

get {
  url: {{base_url}}/v1/download/ofm-items
  body: none
  auth: inherit
}

params:query {
  ~filterBy: copper
  ~sortBy: {"itmDes": "ASC", "itemCd": "DESC"}
}

docs {
  # Download OFM Items Excel
  
  This endpoint generates and downloads an Excel file containing OFM Items data.
  
  ## Query Parameters
  
  - `filterBy` (optional): Search term for item code or description
  - `sortBy` (optional): JSON object for sorting. Available columns: itemCd, itmDes, unit, acctCd, remainingGfq, isSteelbars
  
  ## Response
  
  Returns an object with:
  - `fileName`: Generated filename with format OFMITEM_YYYYMMDD_HHMMSS
  - `data`: CSV data for the Excel file
  
  ## Excel Columns
  
  1. Item Code - Item Code of the Item
  2. Item Description - Item Description
  3. Unit - Unit saved for the Item
  4. Account Code - Item Code of the Item
  5. GFQ - Remaining GFQ of the Item
  6. Steelbars - Yes or No
  7. Weight - Weight of the Steelbars (only if steelbars = Yes)
  8. Company - Company that the Item is related to
  9. Project - Project that the Item is related to
  10. Trade - Trade that the Item is related to
  
  ## Example Response
  
  ```json
  {
    "fileName": "OFMITEM_20240728_102013",
    "data": "Extract as of 2024-07-28\r\n\r\nItem Code,Item Description,Unit,Account Code,GFQ,Steelbars,Weight,Company,Project,Trade\nBW5.5DU5,5.5MM2 BARE COPPER WIRE,bottle,2018C650013131,1000.111,Yes,234.222,CITY & LAND DEVELOPERS INCORPORATED,ONE TAFT RESIDENCES,Civil and Architecture Works"
  }
  ```
}
