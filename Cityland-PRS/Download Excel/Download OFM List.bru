meta {
  name: Download OFM List
  type: http
  seq: 4
}

get {
  url: {{base_url}}/v1/download/ofm-list?searchBy=xavierville
  body: none
  auth: inherit
}

params:query {
  ~filterBy: xavierville
  ~sortBy: {"listName": "ASC", "company": "DESC"}
}

docs {
  # Download OFM List Excel
  
  This endpoint generates and downloads an Excel file containing OFM List data.
  
  ## Query Parameters
  
  - `filterBy` (optional): Search term for list name or item description
  - `sortBy` (optional): JSON object for sorting. Available columns: listName, company, project, trade
  
  ## Response
  
  Returns an object with:
  - `fileName`: Generated filename with format OFMLIST_YYYYMMDD_HHMMSS
  - `data`: CSV data for the Excel file
  
  ## Excel Columns
  
  1. List Name - List Name based on Project and Trade
  2. Company - Company that the List is related to
  3. Project - Project that the List is related to
  4. Trade - Trade that has created the List
  5. Item - Item Description
  6. Account Code - Item Code of the Item
  7. GFQ - Remaining GFQ of the Item
  
  Note: List Name, Company, Project, and Trade are repeated for every Item in the OFM List
  
  ## Example Response
  
  ```json
  {
    "fileName": "OFMLIST_20240728_102013",
    "data": "Extract as of 2024-07-28\r\n\r\nList Name,Company,Project,Trade,Item,Account Code,GFQ\n101 XAVIERVILLE-Civil and Architecture Works,CITYLAND DEVELOPMENT CORPORATION,101 XAVIERVILLE,Civil and Architecture Works,EDC-101X-C&A WRK TXH (NO TAX),2058C715011000,123.456"
  }
  ```
}
