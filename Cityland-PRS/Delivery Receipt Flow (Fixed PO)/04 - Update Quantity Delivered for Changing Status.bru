meta {
  name: 04 - Update Quantity Delivered for Changing Status
  type: http
  seq: 7
}

put {
  url: http://localhost:4000/v1/delivery-receipts/{{drId}}
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "requisitionId": "{{requisitionId}}",
    "poId": "{{poId}}",
    "supplier": "{{supplier}}",
    "isDraft": "true",
    "note": "",
    "items": {{itemsArray}},
    "attachmentIds": {{attachmentIds}}
  }
}

script:pre-request {
  // // Retrieve itemsArray from environment
  // let itemsArray = JSON.parse(bru.getEnvVar("itemsArray") || "[]");
  
  // // Ensure first item's qtyDelivered is equal to qtyOrdered
  // if (itemsArray.length > 0) {
  //     itemsArray[0].qtyDelivered = itemsArray[0].qtyOrdered;
  // }
  
  // // Save updated itemsArray back to environment
  // bru.setEnvVar("itemsArray", JSON.stringify(itemsArray));
  
  // console.log("Updated First Item:", itemsArray[0]);
}

tests {
  if (res.getStatus() === 200) {
      const response = res.getBody();
      const firstItemId = response.items[0].id;
      bru.setEnvVar("itemId", firstItemId.toString());
  } else {
      console.log("Response was not 200");
  }
}
