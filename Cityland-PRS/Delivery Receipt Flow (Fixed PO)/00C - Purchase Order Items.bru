meta {
  name: 00C - Purchase Order Items
  type: http
  seq: 3
}

get {
  url: {{base_url}}/v1/purchase-orders/{{poId}}/items?type=receiving
  body: none
  auth: bearer
}

params:query {
  type: receiving
}

auth:bearer {
  token: {{access_token}}
}

tests {
  // Parse response1 JSON
  let response1 = res.getBody();
  
  // Function to generate a random number between min and max (inclusive)
  function getRandomQty(min, max) {
    return parseFloat(((Math.random() * (max - min + 1.000)) + min).toFixed(3));
  }
  
  // Transform items array
  let transformedItems = response1.data.map(item => {
      let remainingQtyForDelivery = item.remainingQtyForDelivery;
      let qtyDelivered = getRandomQty(0.000, remainingQtyForDelivery);
  
      return {
          "poItemId": item.id,
          "itemId": item.itemId,
          "poId": `${bru.getEnvVar("poId")}`,
          "itemDes": item.itemName,
          "qtyOrdered": item.quantityPurchased,
          "qtyDelivered": qtyDelivered,
          "qtyReturned": 0,  // Placeholder
          "dateDelivered": "Apr 20, 2025", // Placeholder
          "unit": item.unit,
          "notes": ""          // Placeholder
      };
  });
  
  // Store transformed items array
  bru.setEnvVar("itemsArray", JSON.stringify(transformedItems));
  
  console.log("Transformed Items:", transformedItems);
}
