meta {
  name: 00B - Purchase Order Details
  type: http
  seq: 2
}

get {
  url: http://localhost:4000/v1/purchase-orders/{{poId}}/for-delivery
  body: none
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

tests {
  // Parse response1 JSON
  let response1 = res.getBody();
  
  // Extract relevant fields
  bru.setEnvVar("poId", response1.id);
  bru.setEnvVar("supplier", response1.supplier.name);
  
  // Function to generate a random number between min and max (inclusive)
  function getRandomQty(min, max) {
      return Math.floor(Math.random() * (max - min + 1)) + min;
  }
  
  console.log("Stored PO ID:", response1.id);
  console.log("Stored Supplier:", response1.supplier.name);
}
