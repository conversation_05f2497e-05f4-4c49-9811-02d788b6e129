meta {
  name: 10 - Add Attachment to Submitted DR
  type: http
  seq: 10
}

post {
  url: http://localhost:4000/v1/attachments
  body: multipartForm
  auth: bearer
}

auth:bearer {
  token: {{accessToken}}
}

body:multipart-form {
  model: delivery_receipt
  attachments: @file(/Users/<USER>/Documents/Screenshot 2024-02-22 at 11.33.10 AM.png)
  modelId: {{drId}}
}

tests {
  const response = res.getBody();
  
  if (res.getStatus() === 200 && response.length > 0) {
      const ids = response.map(item => item.id.toString());
      bru.setEnvVar("attachmentIds", JSON.stringify(ids));
  } else {
      console.log("No IDs to extract");
  }
}
