meta {
  name: [X] Get Invoice DR Items
  type: http
  seq: 6
}

get {
  url: {{base_url}}/v1/invoice-reports/:id/delivery-report-items?search=3254&sortBy={"drNumber":"ASC","qtyOrdered":"DESC"}&page=1&limit=2
  body: none
  auth: bearer
}

params:query {
  search: 3254
  sortBy: {"drNumber":"ASC","qtyOrdered":"DESC"}
  page: 1
  limit: 2
}

params:path {
  id: 17
}

auth:bearer {
  token: {{access_token}}
}
