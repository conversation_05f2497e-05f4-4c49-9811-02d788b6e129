meta {
  name: Submit Purchase Order with Updates
  type: http
  seq: 4
}

post {
  url: {{base_url}}/v1/purchase-orders/:purchaseOrderId/submit-with-updates
  body: json
  auth: inherit
}

params:path {
  purchaseOrderId: 572
}

body:json {
  {
    "action": {
      "mainFields": true,
      "items": true,
      "additionalFees": false
    },
    "warrantyId": "151",
    "isNewDeliveryAddress": false,
    "newDeliveryAddress": null,
    "isAddedDiscountFixedAmount": true,
    "isAddedDiscountPercentage": false,
    "addedDiscount": 0,
    "items": [
      {
        "purchaseOrderItemId": "1101",
        "quantityPurchased": 8
      }
    ]
  }
}
