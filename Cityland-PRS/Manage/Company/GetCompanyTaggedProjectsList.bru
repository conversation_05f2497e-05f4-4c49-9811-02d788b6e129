meta {
  name: Get Company Tagged Projects List
  type: http
  seq: 6
}

get {
  url: {{base_url}}/v1/companies/:companyId/tagged-projects?limit=10&page=1&page=1&sortBy={"name": "asc"}&paginate=true
  body: json
  auth: inherit
}

params:query {
  limit: 10
  page: 1
  page: 1
  sortBy: {"name": "asc"}
  paginate: true
  ~search: "two"
}

params:path {
  companyId: 399
}

auth:bearer {
  token: {{access_token}}
}
