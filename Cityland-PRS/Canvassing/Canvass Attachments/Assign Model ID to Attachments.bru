meta {
  name: Assign Model ID to Attachments
  type: http
  seq: 3
}

delete {
  url: http://localhost:4000/v1/attachments/:canvassId
  body: json
  auth: bearer
}

params:path {
  canvassId: 
}

auth:bearer {
  token: {{accessToken}}
}

tests {
  // Parse the response JSON
  let jsonData = res.getBody();
  
  // Check if accessToken exists in the response
  if (jsonData.accessToken) {
      // Set the accessToken as an environment variable
      bru.setEnvVar("accessToken", jsonData.accessToken);
      console.log("Access Token saved to environment variable");
  } else {
      console.warn("accessToken not found in the response");
  }
      
}
