meta {
  name: Create canvass V2
  type: http
  seq: 2
}

post {
  url: {{baseUrl}}/v1/canvass
  body: multipartForm
  auth: none
}

body:multipart-form {
  notes: new comment
  requisitionId: 1
  isDraft: false
  addItems[0][requisitionItemListId]: 8
  addItems[0][suppliers][0][supplierId]: 1
  addItems[0][suppliers][0][term]: 1
  addItems[0][suppliers][0][quantity]: 1
  addItems[0][suppliers][0][order]: 1
  addItems[0][suppliers][0][unitPrice]: 1
  addItems[0][suppliers][0][discountValue]: 1
  addItems[0][suppliers][0][discountType]: percent
  addItems[0][suppliers][0][notes]: addItems
  addItems[0][suppliers][0][attachmentIds][0]: 1
  updateItems[0][id]: 272
  updateItems[0][requisitionItemListId]: 9
  updateItems[0][suppliers][0][id]: 170
  updateItems[0][suppliers][0][supplierId]: 2
  updateItems[0][suppliers][0][term]: 2
  updateItems[0][suppliers][0][quantity]: 2
  updateItems[0][suppliers][0][order]: 2
  updateItems[0][suppliers][0][unitPrice]: 2
  updateItems[0][suppliers][0][discountValue]: 0
  updateItems[0][suppliers][0][discountType]: percent
  updateItems[0][suppliers][0][notes]: updateItems
  ~updateItems[0][suppliers][0][attachmentIds][0]: 2
  ~deleteItems[0][id]: 8
}
