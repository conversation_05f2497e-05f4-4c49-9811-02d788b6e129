meta {
  name: Update User
  type: http
  seq: 3
}

put {
  url: {{baseUrl}}/v1/users/:id
  body: json
  auth: bearer
}

params:path {
  id: 4
}

auth:bearer {
  token: {{accessToken}}
}

body:json {
  {
      "firstName": "assistantmanager edited",
      "lastName": "assistantmanager editedng",
      "email": "<EMAIL>",
      "status": "active",
      "supervisorId": 3,
      "departmentId": 6,
      "roleId": 13
  }
}
