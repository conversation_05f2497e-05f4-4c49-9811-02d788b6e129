meta {
  name: 07 - Test Unauthorized Access
  type: http
  seq: 7
}

get {
  url: {{base_url}}/v1/requisitions/{{requisitionId}}/validate-force-close
  auth: bearer
}

auth:bearer {
  token: invalid_token_for_testing
}

tests {
  const response = res.getBody();

  // Test that unauthorized access returns 401
  test("Unauthorized access returns 401", function() {
    expect(res.status).to.equal(401);
  });

  test("Error response has message", function() {
    expect(response).to.have.property('message');
  });

  // Log the response for debugging
  console.log("Unauthorized Access Response:", JSON.stringify(response, null, 2));
}
