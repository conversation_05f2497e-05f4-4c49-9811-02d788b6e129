meta {
  name: 06 - Test Missing Notes
  type: http
  seq: 6
}

post {
  url: {{base_url}}/v1/requisitions/{{requisitionId}}/force-close
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "confirmedScenario": "ACTIVE_PO_PARTIAL_DELIVERY",
    "acknowledgedImpacts": [
      "PO amounts will be updated to reflect delivered quantities only"
    ]
  }
}

tests {
  const response = res.getBody();

  // Test that missing notes returns validation error
  test("Missing notes returns validation error", function() {
    expect(res.status).to.equal(422);
  });

  test("Error response has message", function() {
    expect(response).to.have.property('message');
  });

  // Log the response for debugging
  console.log("Missing Notes Response:", JSON.stringify(response, null, 2));
}
