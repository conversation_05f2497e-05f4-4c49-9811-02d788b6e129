meta {
  name: 02 - Execute Force Close (Scenario 1)
  type: http
  seq: 2
}

post {
  url: {{base_url}}/v1/requisitions/{{requisitionId}}/force-close
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "notes": "Testing Scenario 1: Active PO with partial deliveries - Force closing due to supplier inability to fulfill remaining quantities",
    "confirmedScenario": "ACTIVE_PO_PARTIAL_DELIVERY",
    "acknowledgedImpacts": [
      "PO amounts will be updated to reflect delivered quantities only",
      "PO quantities will be adjusted to match actual deliveries",
      "Unfulfilled quantities will be returned to GFQ for OFM items",
      "All draft and pending documents will be cancelled",
      "Purchase orders will be closed"
    ]
  }
}

tests {
  const response = res.getBody();

  // Test force close execution (should fail for this test requisition)
  test("Force close execution returns error for ineligible requisition", function() {
    expect(res.status).to.equal(400);
  });

  test("Response has error message", function() {
    expect(response).to.have.property('message');
  });

  test("Response has error code", function() {
    expect(response).to.have.property('errorCode');
    expect(response.errorCode).to.equal('BAD_REQUEST');
  });

  // Log the response for debugging
  console.log("Force Close Execution Response:", JSON.stringify(response, null, 2));

  // Store result for history check
  if (response.details) {
    bru.setVar('forceCloseCompleted', 'true');
  }
}
