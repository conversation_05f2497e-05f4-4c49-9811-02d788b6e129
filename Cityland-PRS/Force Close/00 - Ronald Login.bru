meta {
  name: 00 - <PERSON>
  type: http
  seq: 0
}

post {
  url: {{baseUrl}}/v1/auth/login
  body: json
  auth: none
}

body:json {
  {
      "username": "{{username}}",
      "password": "{{password}}"
  }
}

tests {
  const response = res.getBody();
  const { accessToken, otpSecret } = response
  
  if (accessToken) {
      bru.setVar('accessToken', accessToken)
      bru.setVar('access_token', accessToken)
  }
  
  if (otpSecret) {
      bru.setVar('otpSecret', otpSecret)
  }
  
  test("Login successful", function() {
    expect(res.status).to.equal(200);
  });
  
  test("Access token received", function() {
    expect(response.accessToken).to.be.a('string');
  });
  
  console.log("Ronald authenticated successfully");
}
