meta {
  name: 01 - Validate Force Close Eligibility
  type: http
  seq: 1
}

get {
  url: {{baseUrl}}/v1/requisitions/{{requisitionId}}/validate-force-close
  auth: bearer
}

auth:bearer {
  token: {{accessToken}}
}

tests {
  const response = res.getBody();

  // Test that response has required fields
  test("Response has requisitionId", function() {
    expect(response).to.have.property('requisitionId');
  });

  test("Response has isEligible field", function() {
    expect(response).to.have.property('isEligible');
  });

  test("Response has scenario field", function() {
    expect(response).to.have.property('scenario');
  });

  test("Response has buttonVisible field", function() {
    expect(response).to.have.property('buttonVisible');
  });

  // Log the response for debugging
  console.log("Force Close Eligibility Response:", JSON.stringify(response, null, 2));

  // Store scenario for next test if eligible
  if (response.isEligible) {
    bru.setVar('detectedScenario', response.scenario);
    bru.setVar('isEligible', 'true');
  } else {
    bru.setVar('isEligible', 'false');
    bru.setVar('eligibilityReason', response.reason);
  }
}
