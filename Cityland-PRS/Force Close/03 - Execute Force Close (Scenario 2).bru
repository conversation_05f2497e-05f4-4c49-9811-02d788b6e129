meta {
  name: 03 - Execute Force Close (Scenario 2)
  type: http
  seq: 3
}

post {
  url: {{base_url}}/v1/requisitions/{{requisitionId}}/force-close
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "notes": "Testing Scenario 2: All POs closed with remaining quantities - Force closing due to no available suppliers for remaining items",
    "confirmedScenario": "CLOSED_PO_WITH_REMAINING_CANVASS_QTY",
    "acknowledgedImpacts": [
      "All remaining quantities for canvassing will be zeroed out",
      "Unfulfilled quantities will be returned to GFQ for OFM items",
      "Requisition status will be updated to CLOSED",
      "Force close activity will be logged with user notes"
    ]
  }
}

tests {
  const response = res.getBody();

  // Test force close execution (should fail for this test requisition)
  test("Force close execution returns error for ineligible requisition", function() {
    expect(res.status).to.equal(400);
  });

  test("Response has error message", function() {
    expect(response).to.have.property('message');
  });

  test("Response has error code", function() {
    expect(response).to.have.property('errorCode');
    expect(response.errorCode).to.equal('BAD_REQUEST');
  });

  // Log the response for debugging
  console.log("Force Close Scenario 2 Response:", JSON.stringify(response, null, 2));
}
