meta {
  name: 05 - Test Invalid Requisition ID
  type: http
  seq: 5
}

get {
  url: {{base_url}}/v1/requisitions/99999/validate-force-close
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

tests {
  const response = res.getBody();

  // Test that invalid ID returns proper business logic response
  test("Invalid requisition ID returns proper response", function() {
    expect(res.status).to.equal(200);
  });

  test("Response indicates not eligible", function() {
    expect(response).to.have.property('isEligible');
    expect(response.isEligible).to.equal(false);
  });

  test("Response has authorization error scenario", function() {
    expect(response).to.have.property('scenario');
    expect(response.scenario).to.equal('authorization_error');
  });

  test("Response has reason", function() {
    expect(response).to.have.property('reason');
  });

  // Log the response for debugging
  console.log("Invalid ID Response:", JSON.stringify(response, null, 2));
}
