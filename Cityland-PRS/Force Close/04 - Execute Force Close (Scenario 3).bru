meta {
  name: 04 - Execute Force Close (Scenario 3)
  type: http
  seq: 4
}

post {
  url: {{base_url}}/v1/requisitions/{{requisitionId}}/force-close
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "notes": "Testing Scenario 3: Closed POs with pending canvass approvals - Force closing due to requester no longer needing the items",
    "confirmedScenario": "CLOSED_PO_PENDING_CS",
    "acknowledgedImpacts": [
      "All remaining quantities for canvassing will be zeroed out",
      "Pending canvass sheets will be cancelled (status changed to CANCELLED)",
      "Unfulfilled quantities will be returned to GFQ for OFM items",
      "Requisition status will be updated to CLOSED",
      "Force close activity will be logged"
    ]
  }
}

tests {
  const response = res.getBody();

  // Test force close execution (should fail for this test requisition)
  test("Force close execution returns error for ineligible requisition", function() {
    expect(res.status).to.equal(400);
  });

  test("Response has error message", function() {
    expect(response).to.have.property('message');
  });

  test("Response has error code", function() {
    expect(response).to.have.property('errorCode');
    expect(response.errorCode).to.equal('BAD_REQUEST');
  });

  // Log the response for debugging
  console.log("Force Close Scenario 3 Response:", JSON.stringify(response, null, 2));
}
