[{"iterationIndex": 0, "summary": {"totalRequests": 1, "passedRequests": 0, "failedRequests": 0, "skippedRequests": 0, "errorRequests": 1, "totalAssertions": 0, "passedAssertions": 0, "failedAssertions": 0, "totalTests": 0, "passedTests": 0, "failedTests": 0, "totalPreRequestTests": 0, "passedPreRequestTests": 0, "failedPreRequestTests": 0, "totalPostResponseTests": 0, "passedPostResponseTests": 0, "failedPostResponseTests": 0}, "results": [{"test": {"filename": "Force Close/07 - Test Unauthorized Access.bru"}, "request": {"method": "POST", "url": "https://localhost:8444/api/v1/requisitions/604/validate-force-close", "headers": {"Authorization": "Bearer "}}, "response": {"status": "error", "statusText": null, "headers": null, "data": null, "responseTime": 0}, "error": "self-signed certificate", "status": "error", "assertionResults": [], "testResults": [], "preRequestTestResults": [], "postResponseTestResults": [], "shouldStopRunnerExecution": false, "runDuration": 1.1518395830000001, "name": "07 - Test Unauthorized Access", "path": "Force Close/07 - Test Unauthorized Access", "iterationIndex": 0}]}]