meta {
  name: Ad-hoc Approver
  type: http
  seq: 2
}

post {
  url: http://localhost:4000/v1/non-requisitions/31/add-adhoc-approver
  body: json
  auth: inherit
}

headers {
  Content-Type: application/json
  Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6NiwiaWF0IjoxNzQ1MzgwMTk5LCJleHAiOjE3NDUzODM3OTl9.jLpsBfWc48eoj5z46EDBFyimAYtNzTLN8OSYt4q2wE0
}

body:json {
  {"approverId":4}
}
