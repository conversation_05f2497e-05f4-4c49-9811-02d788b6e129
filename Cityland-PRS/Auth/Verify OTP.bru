meta {
  name: Verify OTP
  type: http
  seq: 6
}

post {
  url: {{baseUrl}}/v1/auth/verify-otp
  body: json
  auth: bearer
}

auth:bearer {
  token: {{accessToken}}
}

body:json {
  {
      "otp": "382463"
  }
}

tests {
  const response = res.getBody();
  const { accessToken, refreshToken } = response;
  
  if (accessToken) {
      bru.setVar('accessToken', accessToken)
  }
  
  if (refreshToken) {
      bru.setVar('refreshToken', refreshToken)
  }
  
  
  
}
