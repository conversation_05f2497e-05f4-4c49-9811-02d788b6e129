meta {
  name: Update Temp Pass
  type: http
  seq: 7
}

post {
  url: {{baseUrl}}/v1/auth/update-temp-password
  body: json
  auth: bearer
}

auth:bearer {
  token: {{accessToken}}
}

body:json {
  {
      "password": "Cityl@nd123"
  }
}

tests {
  const response = res.getBody();
  const {accessToken, otpSecret} = response;
  
  // For OTP Setup
  if (accessToken) {
      bru.setVar('accessToken', accessToken)
      bru.setVar('otpSecret', otpSecret)
  }
  
  
  
  
}
