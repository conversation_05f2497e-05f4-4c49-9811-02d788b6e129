services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: fastify-app
    command: npm start
    restart: always
    ports:
      - 4000:4000
    volumes:
      - ./cityland_be_logs:/usr/app/logs
    env_file:
      - .env.example

  postgres:
    container_name: postgres
    hostname: postgres
    image: 'postgres:15'
    environment:
      POSTGRES_DB: prs
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: admin
    ports:
      - '5432:5432'
    restart: always
    volumes:
      - ./cityland_db:/var/lib/postgresql/data
