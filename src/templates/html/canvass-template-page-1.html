<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="preconnect" href="https://rsms.me/" />
    <link rel="stylesheet" href="https://rsms.me/inter/inter.css" />
    <title>Canvass Sheet</title>
    <style>
      @media print {
        .page,
        .page-break {
          break-after: page;
        }

        .section-break {
          break-inside: avoid;
          page-break-inside: avoid;
        }
      }

      @page {
        size: legal;
      }

      body {
        font-family: 'Inter', sans-serif;
        font-size: 12px;
        line-height: 1.2;
        color: #000;
        margin: 0;
        padding: 0;
        width: 8.5in;
        height: 11in;
        box-sizing: border-box;
      }

      @supports (font-variation-settings: normal) {
        body {
          font-family: 'Inter var', sans-serif;
        }
      }

      .wrapper {
        max-width: 7.5in;
        margin: 0 auto;
        padding: 0;
        padding-bottom: 40px;
      }

      .header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 10px;
      }

      .title {
        color: #8b4513;
        font-size: 20px;
        font-weight: bold;
      }

      .page-info {
        text-align: right;
        font-size: 12px;
        margin-top: 10px;
        margin-bottom: 10px;
        white-space: nowrap;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 2px;
      }

      .project-name {
        font-size: 22px;
        font-weight: bold;
        margin-top: 5px;
      }

      .divider {
        border-bottom: 1px solid #333;
        margin: 10px 0;
      }

      .cs-number {
        display: flex;
        justify-content: space-between;
        margin: 20px 0;
      }

      .cs-number-left {
        font-size: 18px;
        font-weight: bold;
      }

      .cs-number-id {
        font-weight: normal;
      }

      .date-section {
        display: flex;
        justify-content: space-between;
      }

      .main-content {
        display: flex;
        gap: 20px;
        margin-top: 20px;
      }

      .left-panel {
        flex: 2;
        border: 1px solid #ccc;
        border-radius: 5px;
        padding: 15px;
      }

      .right-panel {
        flex: 1;
        border: 1px solid #ccc;
        border-radius: 5px;
        padding: 15px;
      }

      .field-group {
        margin-bottom: 15px;
      }

      .field-label {
        font-size: 14px;
      }

      .field-value {
        font-weight: bold;
        margin-top: 3px;
      }

      .field-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 15px;
      }

      .field-col {
        flex: 1;
      }

      .route-line {
        display: flex;
        margin-bottom: 10px;
      }

      .route-number {
        width: 20px;
        font-weight: bold;
      }

      .route-input {
        flex: 1;
        border-bottom: 1px solid #333;
        margin-left: 5px;
      }

      .table-container {
        border-radius: 8px;
        font-size: 7px;
        overflow: hidden;
        border: 1px solid #ccc;
        margin-top: 5px;
        margin-bottom: 15px;
        padding: 1px;
      }

      table {
        width: 100%;
        border-collapse: collapse;
        margin: 0;
        border: none;
      }

      th,
      td {
        border: 1px solid #ccc;
        padding: 4px;
        text-align: left;
        font-size: 10px;
      }

      tr:first-child th {
        border-top: none;
      }

      tr:last-child td {
        border-bottom: none;
      }

      th:first-child,
      td:first-child {
        border-left: none;
      }

      th:last-child,
      td:last-child {
        border-right: none;
      }

      th,
      td {
        padding: 4px;
        text-align: left;
        font-size: 10px;
      }

      th {
        color: #4f575e;
        vertical-align: top;
        text-align: left;
        font-weight: bold;
        font-size: 8px;
      }

      td {
        height: 30px;
      }

      .header-row {
        display: flex;
        justify-content: space-between;
        margin-top: 20px;
        margin-bottom: 5px;
      }

      .pagination {
        text-align: right;
      }

      .qty {
        text-align: center;
      }

      .unit {
        text-align: center;
      }

      .unit-price-wrapper,
      .discount-wrapper {
        margin-top: 5px;
      }

      .unit-price-label,
      .discount-label {
        font-size: 11px;
      }

      .supplier-col {
        width: 50%;
      }

      .price {
        color: #333;
      }

      .discounted-price {
        color: #333;
        font-weight: bold;
      }

      .text-bold {
        font-weight: bold;
      }
    </style>
  </head>

  <body>
    <div class="wrapper">
      <div class="header">
        <div>
          <div class="title">CANVASS SHEET</div>
          <div class="project-name">[PROJECT NAME]</div>
        </div>
        <div class="page-info">
          <span>Page</span>
          <span
            type="text"
            name="currentPage"
            class="page-number-input text-bold"
            >1</span
          >
          <span>of</span>
          <span type="text" name="totalPage" class="page-number-input">10</span>
        </div>
      </div>

      <div class="divider"></div>

      <div class="cs-number">
        <div class="cs-number-left">
          C.S. # <span class="cs-number-id text-bold">[CS-12AA0000000001]</span>
        </div>
        <div class="date-section">
          <div style="margin-right: 75px">Date Prepared: XX Mmm YYYY</div>
          <div style="margin-right: 75px">Date Received:</div>
        </div>
      </div>

      <div class="main-content">
        <div class="left-panel">
          <div class="field-group">
            <div class="field-label">Purpose:</div>
            <div class="field-value">
              PPT1 Replenishment of Janitorial Supplies
            </div>
          </div>

          <div class="field-row">
            <div class="field-col">
              <div class="field-label">Department:</div>
              <div class="field-value">Admin</div>
            </div>
            <div class="field-col">
              <div class="field-label">Date Needed:</div>
              <div class="field-value">ASAP</div>
            </div>
            <div class="field-col">
              <div class="field-label">Charge To:</div>
              <div class="field-value">PPT1</div>
            </div>
          </div>

          <div class="field-row">
            <div class="field-col">
              <div class="field-label">Request By:</div>
              <div class="field-value">[Requestor Name]</div>
            </div>
            <div class="field-col">
              <div class="field-label">Noted By:</div>
              <div class="field-value"></div>
            </div>
          </div>
        </div>

        <div class="right-panel">
          <div class="field-label">Please Route To:</div>
          <div style="margin-top: 10px">
            <div class="route-line">
              <div class="route-number">1.</div>
              <div class="route-input"></div>
            </div>
          </div>
        </div>
      </div>

      <div class="header-row">
        <div class="text-bold">Items</div>
        <div>
          <span class="text-bold">NOTE:</span> Please be specific in your
          request.
        </div>
        <div class="pagination">
          <span class="text-bold">1 - 10</span> of 12
        </div>
      </div>

      <div class="table-container">
        <table>
          <thead>
            <tr>
              <th>#</th>
              <th>Qty</th>
              <th>Unit</th>
              <th>Description</th>
              <th colspan="4" class="supplier-col">Suppliers</th>
              <th>Notes</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>1</td>
              <td class="qty">888,888,888</td>
              <td class="unit">pcs</td>
              <td>
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Duis
                imperdiet magna cursus enim mollis, non vulputate.
              </td>
              <td>
                <div>[Very Long Supplier Name Here]</div>
                <div class="unit-price-wrapper">
                  <div class="unit-price-label">Unit Price:</div>
                  <div class="price">₱100,000.00</div>
                </div>
                <div class="discount-wrapper">
                  <div class="discount-label">Discounted Unit Price:</div>
                  <div class="discounted-price">₱100,000.00</div>
                </div>
              </td>
              <td>
                <div>[Very Long Supplier Name Here]</div>
                <div class="unit-price-wrapper">
                  <div class="unit-price-label">Unit Price:</div>
                  <div class="price">₱100,000.00</div>
                </div>
                <div class="discount-wrapper">
                  <div class="discount-label">Discounted Unit Price:</div>
                  <div class="discounted-price">₱100,000.00</div>
                </div>
              </td>
              <td>
                <div>[Very Long Supplier Name Here]</div>
                <div class="unit-price-wrapper">
                  <div class="unit-price-label">Unit Price:</div>
                  <div class="price">₱100,000.00</div>
                </div>
                <div class="discount-wrapper">
                  <div class="discount-label">Discounted Unit Price:</div>
                  <div class="discounted-price">₱100,000.00</div>
                </div>
              </td>
              <td>
                <div>[Very Long Supplier Name Here]</div>
                <div class="unit-price-wrapper">
                  <div class="unit-price-label">Unit Price:</div>
                  <div class="price">₱100,000.00</div>
                </div>
                <div class="discount-wrapper">
                  <div class="discount-label">Discounted Unit Price:</div>
                  <div class="discounted-price">₱100,000.00</div>
                </div>
              </td>
              <td>
                Etiam magna tempor, magna tempor magna, non auctor vel augue.
                Etiam nulla vel diam.
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="table-container section-break">
        <table>
          <thead>
            <tr>
              <th rowspan="2" class="row-number-header">#</th>
              <th colspan="6" class="section-header">Approved</th>
              <th colspan="5" class="section-header">FMSD Notation</th>
            </tr>
            <tr>
              <th>Supplier</th>
              <th>Unit Cost</th>
              <th>GFQ</th>
              <th>Ordered</th>
              <th>This Request</th>
              <th>Percent</th>
              <th>Qty</th>
              <th>Date</th>
              <th>CV No.</th>
              <th>Amount</th>
              <th>By</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="row-number">1</td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </body>
</html>
