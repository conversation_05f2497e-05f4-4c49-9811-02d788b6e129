<!doctype html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Purchase Request System</title>
  <style>
    @media print {

      .page,
      .page-break {
        break-after: page;
      }
    }

    @page {
      size: legal;
      margin: 0.3in;
    }

    body {
      font-family: 'Inter', sans-serif;
      font-size: 12px;
      line-height: 1.2;
      color: #000;
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    @supports (font-variation-settings: normal) {
      body {
        font-family: 'Inter var', sans-serif;
      }
    }

    .wrapper {
      max-width: 7.5in;
      margin: 0 auto;
      padding: 0;
      padding-bottom: 40px;
    }

    .header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
    }

    .logo-title {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .logo {
      font-size: 20px;
      line-height: 1;
    }

    .title {
      font-weight: bold;
      font-size: 12px;
      color: #450a0a;
    }

    .page-info {
      text-align: right;
      font-size: 12px;
      /* Remove margin-top: 10px; */
      display: flex;
      align-items: center;
      justify-content: flex-end;
      gap: 2px;
    }

    .page-number-input {
      width: 18px;
      text-align: center;
      border-radius: 4px;
      padding: 1px;
      font-weight: bold;
      margin: 0;
    }

    .rs-section {
      margin-top: 10px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .rs-details {
      display: flex;
    }

    .rs-label {
      font-weight: bold;
      font-size: 17px;
      margin-right: 10px;
    }

    .rs-value {
      font-weight: bold;
      border: none;
      background: transparent;
      font-family: inherit;
      font-size: 17px;
      align-items: left;
    }


    .status {
      font-weight: bold;
      text-align: right;
      font-size: 12px;
      border: none;
      background: transparent;
      font-family: inherit;
    }


    .section {
      margin: 15px 0;
    }

    .section-title {
      font-weight: bold;
      margin-bottom: 5px;
      font-size: 12px;
    }

    .details-grid-outer {
      border: 1px solid #ccc;
      border-radius: 10px;
      padding: 10px;
    }

    .details-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 10px;
      padding: 5px;
      font-size: 10px;
    }

    .details-row {
      display: flex;
      flex-direction: column;
    }

    .details-label {
      color: #323232;
      font-weight: normal;
      font-size: 8px;
      margin-bottom: 4px;
      text-align: left;
    }

    input,
    select {
      border: none;
      background: transparent;
      width: 100%;
      font-family: inherit;
      font-size: inherit;
      outline: none;
    }

    input[name="company"],
    input[name="department"],
    input[name="project"],
    input[name="purpose"] {
      font-weight: 500;
    }

    .items-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 15px;
      margin-bottom: 5px;
    }

    .items-title {
      font-weight: bold;
      color: #495057;
      font-size: 14px;
    }

    .items-counter {
      display: flex;
      align-items: center;
      gap: 3px;
    }

    .item-count-input {
      width: 20px;
      text-align: center;
      background-color: transparent;
      font-weight: bold;
      margin: 0;
      padding: 1px;
    }

    .total-count {
      width: 25px;
    }

    .items-counter span {
      color: #6c757d;
    }

    /* Table styles for better pagination */
    table {
      width: 100%;
      border-collapse: collapse;
      margin: 0;
      border: none;
    }

    th,
    td {
      border: 1px solid #ccc;
      padding: 4px;
      text-align: left;
      font-size: 10px;
    }

    /* Remove border from outer edges of table cells */
    tr:first-child th {
      border-top: none;
    }

    tbody tr:last-child td {
      border-bottom: none;
    }

    th:first-child,
    td:first-child {
      border-left: none;
    }

    th:last-child,
    td:last-child {
      border-right: none;
    }

    th {
      color: #4f575e;
      vertical-align: top;
      text-align: left;
      font-weight: bold;
      font-size: 8px;
      background-color: #f8f9fa;
    }

    td {
      height: 30px;
    }

    /* Remove items-section container border */
    .items-section {
      border: none;
      margin-top: 10px;
    }

    .num-col {
      vertical-align: middle;
      width: 20px;
      text-align: center;
    }

    .unit-col {
      width: 40px;
      vertical-align: middle;
      text-align: center;
    }

    .remaining-col,
    .qty-col {
      width: 90px;
      text-align: center;
      vertical-align: middle;
    }

    .item-name-cell {
      vertical-align: middle;
      word-break: break-all;
      word-wrap: break-word;
      white-space: normal;
      padding: 4px;
      text-align: left;
    }

    .item-name-input {
      color: #4f575e;
      width: 100%;
      min-height: 50px;
      outline: none;
      border: none;
      background: transparent;
      font-family: inherit;
      font-size: inherit;
      text-decoration: underline;
      word-break: break-all;
      word-wrap: break-word;
      white-space: normal;
      text-align: left;
      resize: none;
      overflow: hidden;
    }

    tbody tr {
      height: 80px;
    }

    input,
    textarea {
      text-align: center;
      vertical-align: middle;
    }

    .text-left {
      text-align: center;
    }

    .footer {
      margin-top: 20px;
      text-align: right;
      font-size: 10px;
      color: #666;
    }

    /* Style for the items section */
    .items-section {
      border: 1px solid #ccc;
      border-radius: 8px;
      overflow: hidden;
      margin-top: 10px;
    }
  </style>
</head>

<body>
  <div class="wrapper">
    {{#each pages}}
    <div class="page" {{#if @index}}style="page-break-before: always;" {{/if}}>
      <div class="header">
        <div class="logo-title">
          <div class="logo">
<svg
                    width='22'
                    height='24'
                    viewBox='0 0 22 24'
                    fill='none'
                    xmlns='http://www.w3.org/2000/svg'
                  >
                    <path
                      d='M4.86392 9.24902H4.53815V14.3166H4.86392V9.24902Z'
                      fill='#653332'
                    />
                    <path
                      fill-rule='evenodd'
                      clip-rule='evenodd'
                      d='M13.3701 9.24902H13.7321L14.8904 14.3166H14.5465L14.2027 12.7239H12.8815L12.5376 14.3166H12.2119L13.3701 9.24902ZM13.5692 9.79197H13.533L12.9848 12.2895H14.1022L13.5692 9.79197Z'
                      fill='#653332'
                    />
                    <path
                      d='M8.48357 14.3166H8.73694V12.4162L9.71425 9.24902H9.38848L8.61025 11.8733L7.83203 9.24902H7.52435L8.48357 12.4162V14.3166Z'
                      fill='#653332'
                    />
                    <path
                      d='M10.6916 9.24902H10.3658V14.3166H11.7413V13.846H10.6916V9.24902Z'
                      fill='#653332'
                    />
                    <path
                      fill-rule='evenodd'
                      clip-rule='evenodd'
                      d='M18.8721 14.3166V9.24902C18.8721 9.24902 19.4874 9.24902 19.8675 9.24902C20.0847 9.24902 20.3018 9.32924 20.6922 9.71958C21.2832 10.3106 21.2611 11.7828 21.2611 11.7828C21.2611 11.7828 21.3153 13.0135 20.7724 13.7917C20.4256 14.2887 19.7951 14.3166 19.7951 14.3166H18.8721ZM19.1254 13.846V9.75578C19.1254 9.75578 19.6865 9.75578 20.0123 9.75578C20.0666 9.75578 20.3474 9.79197 20.6922 10.3892C20.9801 10.8879 20.9715 11.8009 20.9715 11.8009C20.9715 11.8009 20.9801 12.7501 20.6922 13.2488C20.3474 13.846 19.9399 13.846 19.9399 13.846H19.1254Z'
                      fill='#653332'
                    />
                    <path
                      d='M15.8315 9.24902H15.5781V14.3166H15.8315V9.90056L17.4604 14.3166H17.8223V9.24902H17.5327V13.6469L15.9039 9.24902H15.8315Z'
                      fill='#653332'
                    />
                    <path
                      d='M6.49275 9.24902H6.20317H5.58783V9.75578H6.20317V14.3166H6.49275V9.75578H7.10809V9.24902H6.49275Z'
                      fill='#653332'
                    />
                    <path
                      d='M2.29395 14.4252C1.38903 14.4252 0.737488 13.2421 0.737488 11.7828C0.737488 10.3235 1.43434 9.17664 2.29395 9.17664C2.89179 9.17664 3.41937 9.71267 3.66942 10.5521H3.30373C3.08351 10.0047 2.72062 9.611 2.29395 9.611C1.59615 9.611 1.06326 10.613 1.06326 11.7925C1.06326 12.972 1.55937 13.9546 2.29395 13.9546C2.70634 13.9546 3.05893 13.5844 3.28114 13.0678H3.65132C3.39608 13.8775 2.87881 14.4252 2.29395 14.4252Z'
                      fill='#653332'
                    />
                    <path
                      fill-rule='evenodd'
                      clip-rule='evenodd'
                      d='M11.0355 0.959961C13.781 0.959961 15.4696 3.45753 16.7799 7.43917H12.7005C12.3916 4.16172 11.7953 1.6839 11.144 1.6839C10.4027 1.6839 9.33458 4.26379 8.94273 8.3079H5.16648C6.00121 3.95388 8.2899 0.959961 11.0355 0.959961ZM5.12189 15.2939C5.94324 19.7823 8.37603 23.04 11.0355 23.04C13.3241 23.04 15.6223 20.3991 16.6934 16.5969H12.667C12.2898 19.798 11.5832 21.9722 10.9812 21.9722C10.4258 21.9721 9.47657 19.1606 9.04464 15.2939H5.12189Z'
                      fill='#653332'
                    />
                  </svg>
          </div>
          <div class="title">Purchase Request System</div>
        </div>
        <div class="page-info">
          <span>Page</span>
          <input type="text" name="currentPage" class="page-number-input" value="{{currentPage}}" />
          <span>of</span>
          <input type="text" name="totalPage" class="page-number-input" value="{{../totalPage}}" />
        </div>
      </div>

      <div class="rs-section">
        <div class="rs-details">
          <span class="rs-label">R.S. #</span>
          <span class="rs-value" name="rsNumber"> {{../rsNumber}} </span>
        </div>
        <span name="status" class="status"> {{../status}} </span>
      </div>



      {{#if @first}}
      <div class="section">
        <div class="details-grid-outer">
          <div class="section-title">Request Details</div>
          <div class="details-grid">
            <div class="details-row">
              <div class="details-label">Company:</div>
              <div><input type="text" name="company" value="{{../company}}" style="text-align: left" /></div>
            </div>
            <div class="details-row">
              <div class="details-label">Department:</div>
              <div><input type="text" name="department" value="{{../department}}" style="text-align: left;" /></div>
            </div>
            <div class="details-row">
              <div class="details-label">Project:</div>
              <div><input type="text" name="project" value="{{../project}}" style="text-align: left;" /></div>
            </div>
            <div class="details-row">
              <div class="details-label">Purpose:</div>
              <div><input type="text" name="purpose" value="{{../purpose}}" style="text-align: left;" /></div>
            </div>
          </div>
        </div>
      </div>
      {{/if}}

      <div class="items-header">
        <div class="items-title">Items</div>
        <div class="items-counter">
          <input type="text" name="firstItemCount" class="item-count-input" value="{{itemsCount}}" />
          <span>-</span>
          <input type="text" name="lastItemCount" class="item-count-input" value="{{currentTotalItemsCount}}" />
          <span>of</span>
          <input type="text" name="total" class="item-count-input total-count" value="{{../totalItems}}" />
        </div>
      </div>

      <div class="items-section">
        <table>
          <thead>
            <tr>
              <th style="text-align: center">#</th>
              <th>Item Name</th>
              <th style="text-align: center">Unit</th>
              <th style="text-align: center">Remaining GFQ</th>
              <th style="text-align: center">Qty</th>
            </tr>
          </thead>
          <tbody>
            {{#each items}}
            <tr>
              <td class="num-col"><input type="text" name="itemNum{{@../index}}_{{@index}}" value="{{itemNum}}" /></td>
              <td class="item-name-cell">
                <textarea class="item-name-input" name="itemName{{@../index}}_{{@index}}">{{itemName}}</textarea>
              </td>
              <td class="unit-col"><input type="text" name="unit{{@../index}}_{{@index}}" value="{{unit}}" /></td>
              <td class="remaining-col">
                <input type="text" name="remainingGfq{{@../index}}_{{@index}}" value="{{remainingGfq}}" />
              </td>
              <td class="qty-col">
                <input type="text" name="qty{{@../index}}_{{@index}}" value="{{qty}}" />
              </td>
            </tr>
            {{/each}}
          </tbody>
        </table>
      </div>
    </div>
    {{/each}}
  </div>
</body>

</html>