<html lang='en'>
  <head>
	<meta charset='UTF-8' />
	<meta name='viewport' content='width=device-width, initial-scale=1.0' />
	<title>Purchase Request System</title>
	<link rel='preconnect' href='https://rsms.me/' />
	<link rel='stylesheet' href='https://rsms.me/inter/inter.css' />
	<style>
		@page {
      	size: legal;
		margin: .3cm;
	  }
	  
	  body {
		font-family: 'Inter', sans-serif;
		font-size: 12px;
		line-height: 1.2;
		color: #000;
		margin: 0;
		padding: 0;
		width: 8.5in;
		height: 11in;
		box-sizing: border-box;
	  }

	  /* Support for Inter variable font */
	  @supports (font-variation-settings: normal) {
		body {
		  font-family: 'Inter var', sans-serif;
		}
	  }

	  .wrapper {
		padding-top: 30px !important;
		max-width: 7.5in;
		margin: 0 auto;
		height: 95vh;
		position: relative;
		padding: 0;
	  }

	  .header {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		align-items: flex-start;
	  }

	  .header > div {
		flex: 50%; /* or - flex: 0 50% - or - flex-basis: 50% - */
		/*demo*/
	  }

	  .logo-title {
		display: flex;
		align-items: center;
		gap: 8px;
	  }

	  .po-title {
		margin-top: 10px;
		font-size: 16px;
		color: #754445;
	  }

	  .logo {
		font-size: 20px;
		line-height: 1;
	  }

	  .title {
		font-weight: bold;
		font-size: 12px;
		color: #800000;
	  }

	  .page-info {
		text-align: right;
		font-size: 12px;
		height: 14px;
	  }

	  .po-section {
		font-size: 17px;
	  }

	  .date-extracted-section {   
		 text-align: right;
		font-size: 10px;
		line-height: 15px;
		vertical-align: middle;
		height: 15px;
	  }

	  .project-company-header {
		font-weight: bold;
		display: flex;
		margin-top: 10px;
		justify-content: center;
		align-items: center;
		text-align: center;
		width: 100%;
		border-bottom: 1px solid #000;
	  }

	  .pr-number {
		margin-top: 10px;
		display: flex;
		align-items: center;
		justify-content: left;
		/*gap: 10px;*/
	  }

	  .pr-label {
		font-size: 14px;
		font-weight: bold;
	  }

	  .pr-value {
		text-decoration: underline;
		font-weight: bold;
	  }

	  .date-section {
		display: flex;
		align-items: center;
	  }

	  .date-label {
		color: #323232;
		font-size: 7px;
		margin-left: 30px;
	  }

	  .date-value {
		font-size: 8px;
		font-weight: bold;
	  }

	  .note-section {
		font-size: 10px;
		height: 14px;
	  }

	  .bottom-note-section {
		font-size: 10px;
		margin-top: 10px;
		text-align: right;
		font-style: italic;
		width: 100%;
		margin-bottom: 25px;
	  }

	  .amount-words {
		font-style: italic;
		font-weight: bold;
	  }

	  .bottom-note-section input {
		border: none;
		width: 30%;
		font-size: 8px;
		margin-left: 5px;
		font-style: italic;
	  }

	  .section {
		margin: 10px 0;
		display: flex;
		gap: 10px;

		.details-grid-outer {
		  flex: 50%; 
		}
		.section-terms {
		  flex: 70%;
		  flex-basis: 70%;

		  .details-grid {
			display: grid;
			grid-template-columns: 1fr 1fr;
			gap: 10px;
			font-size: 10px;
		  padding-bottom: 10px;
		  }
		}
		.section-purchasing {
		  flex: 30%;
		  flex-basis: 30%;
		}

		.details-grid-outer2 {
			
		border: 1px solid #ccc;
		border-radius: 10px;
		padding: 10px;
		padding-bottom: 0px;
		}
	  }

	  .section-title {
		font-weight: bold;
		margin-bottom: 10px;
		font-size: 13px;
	  }

	  .section-description {
		margin-bottom: 5px;
		font-size: 9px;
	  }

	  .details-grid-outer {
		border: 1px solid #ccc;
		border-radius: 10px;
		padding: 10px;
		padding-bottom: 0px;
	  }

	  .signing-details-grid {
		display: grid;
		grid-template-columns: 1fr 1fr 1fr 1fr;
		gap: 10px;
		padding: 10px;
		font-size: 10px;
	  }

	  .details-row {
		font-size: 10px!important;
		display: flex;
		flex-direction: column;
		margin-bottom: 10px;
	  }

	  .details-label {
		color: #323232;
		font-weight: normal;
		font-size: 10px;
		margin-bottom: 4px;
	  }

	  .signing-details-label {
		color: #323232;
		font-weight: normal;
		font-size: 8px;
	  }

	  .signing-details-label input {
		margin-top: 20px;
		border-bottom: solid 1px;
		width: 150px;
	  }

	  .checks-container {
		display: flex;
		flex-direction: row;
		align-items: center;
		gap: 10px;
		width: 100%;
		padding: 0px 0 10px;
	  }

	  .check-item {
		display: flex;
		align-items: center;
		white-space: nowrap;
	  }

	  .check-item label {
		font-size: 8px;
		margin-right: 3px;
	  }

	  /* Style for the checkboxes */
	  .check-item input[type='checkbox'] {
		width: 10px;
		height: 10px;
		margin-right: 3px;
	  }

	  .check-textfield {
		border: none;
		border-bottom: 1px solid #000;
		font-size: 7px;
		padding: 0;
		margin: 0;
	  }

	  input,
	  select {
		border: none;
		background: transparent;
		width: 100%;
		font-family: inherit;
		font-size: inherit;
		outline: none;
	  }

	  .items-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 15px;
	  }

	  .items-title {
		font-weight: bold;
	  }

	  .table-container {
		border-radius: 8px;
		overflow: hidden;
		border: 1px solid #ccc;
		margin-top: 10px;
		margin-bottom: 15px;
		padding: 1px;
	  }

	  table {
		width: 100%;
		border-collapse: collapse;
		margin: 0;
		border: none;
	  }

	  th,
	  td {
		border: 1px solid #ccc;
		padding: 4px;
		text-align: left;
		font-size: 10px;
	  }

	  /* Remove border from outer edges of table cells */
	  tr:first-child th {
		border-top: none;
	  }

	  tr:last-child td {
		border-bottom: none;
	  }

	  th:first-child,
	  td:first-child {
		border-left: none;
	  }

	  th:last-child,
	  td:last-child {
		border-right: none;
	  }

	  th,
	  td {
		padding: 4px;
		text-align: left;
		font-size: 11px;
		color: #4F575E;
	  }

	  th {
		color: #4f575e;
		vertical-align: top;
		text-align: left;
		font-weight: 500;
		font-size: 9px;
	  }

	  td {
		height: 30px;
		font-size: 9px;
	  }

	  .base-col {
		width: 80px;
	  }

	  .num-col {
		width: 20px;
	  }

	  .unit-col {
		width: 30px;
	  }

	  .discount-col {
		width: 40px;
	  }

	  .pricing-col {
		width: 60px;
	  }

	  .pricing-col input {
		width: 90%;
		margin-right: 5px;
	  }

	  .item-name-cell {
		vertical-align: center;
		word-break: break-all;
		word-wrap: break-word;
		white-space: normal;
		padding: 4px;
		width: 200px;
	  }

	  .item-name-input {
		color: #4f575e;
		width: 100%;
		outline: none;
		border: none;
		background: transparent;
		font-family: inherit;
		font-size: inherit;
		text-decoration: underline;
		word-break: break-all;
		word-wrap: break-word;
		white-space: normal;
		text-align: left;
		resize: none;
		overflow: hidden;
	  }

	  .text-left {
		text-align: left;
	  }

	  .text-right {
		text-align: right;
	  }

	  .footer {
		position: absolute;
		bottom: 0;
		text-align: left;
		font-size: 10px;
		color: #666;
	  }

	  .main-title {
		text-align: center;
		font-size: 17px;

		span {
		  font-weight: bold;
		  display: inline-block;
		}
		div {
		  font-size: 10px;
		  .input {
			display: inline-block;
			width: 80px;
		  }
		}
	  }

	  .project-name-2 {
		font-weight: 700;
		font-size: 17px;
		vertical-align: middle;

	  }

	  .po-section-2 {
		font-weight: 400;
		font-size: 11px;
		vertical-align: middle;

	  }

	  .details-row-2 {
		font-weight: 400;
		font-size: 10px;
		vertical-align: middle;
		text-transform: capitalize;

	  }

	  .left-align {
		text-align: right;
	  }

	  .flex-col {
		flex-direction: column;

	  }
	   .footer-wrapper{
  display: flex;
  flex-direction: column;
	  }

	  .grow {

	  }
	</style>
  </head>

  <body>
	<div class='body'>

	  {{#each pagesData as |pageData pageIndex|}}
		<div class='wrapper' {{#unless @first}}style="page-break-before: always;"{{/unless}}>

		  {{#if @first}}
			<div>
			  <div class='header'>
				<div class='note-section'>
				  <span> <b>NOTE: </b>ISSUE SALES/SERVICE INVOICE TO: </span>
				</div>

				<div class='page-info'>
				  Page
				  <b>{{@pageData.page}}</b>
				  of
				  {{@root.totalPages}}
				</div>

				<div class='po-section'>
				  <span>
					<b>P.O.# {{@root.poNumber}}</b>
				  </span>
				</div>

				<div class='date-extracted-section'>
				  <span> Date Extracted: <b>{{@root.dateExtracted}}</b></span>
				</div>
			  </div>

			  <div class='po-title'>
				<b>PURCHASE ORDER</b>
			  </div>

			  <div class='main-title'>
				<span>{{@root.projectName}}</span>
				<div>TIN: <b>{{@root.tin}}</b> </div>
				<div>BUSINESS STYLE: <b>{{@root.businessStyle}}</b></div>
			  </div>

			  <div>
				<div class='project-company-header'></div>
			  </div>

			  <div class='section'>
				<div class='details-grid-outer'>
				  <div class='section-title'>Vendor</div>
				  <div class='details-grid'>
					<div class='details-row'>
					  <div class='details-label'>Company Name:</div>
					  <div><b>{{@root.companyName}}</b></div>
					</div>
					<div class='details-row'>
					  <div class='details-label'>TIN:</div>
					  <div><b>{{@root.vendorTin}}</b></div>
					</div>
					<div class='details-row'>
					  <div class='details-label'>Contact Details:</div>
					  <div><b>{{@root.contactName}}
						  {{@root.contactNumber}}</b></div>
					</div>
					<div class='details-row'>
					  <div class='details-label'>Street Address:</div>
					  <div><b>{{@root.streetAddress}}</b></div>
					</div>
				  </div>
				</div>
				<div class='details-grid-outer'>
				  <div class='section-title'>Deliver To</div>
				  <div class='details-grid'>
					<div class='details-row'>
					  <div class='details-label'>Project:</div>
					  <div><b>{{@root.projectName}}</b></div>
					</div>
					<div class='details-row'>
					  <div class='details-label'>Street Address:</div>
					  <div><b>{{@root.deliverToStreetAddress}}</b></div>
					</div>
					<div class='details-row'>
					  <div class='details-label'>Contact Person:</div>
					  <div><b>{{@root.deliverToContactPerson}}</b></div>
					</div>
				  </div>
				</div>
			  </div>
			</div>
		  {{else}}
			<div>
			  <div class='header'>
				<div class='note-section'>
				  <span> <b>NOTE: </b>ISSUE SALES/SERVICE INVOICE TO: </span>
				</div>

				<div class='page-info'>
				  Page
				  <b>{{@pageData.page}}</b>
				  of
				  {{@root.totalPages}}
				</div>

				<div class='po-section-2'>
				  <span>
					<b>P.O.# {{@root.poNumber}}</b>
				  </span>
				</div>

			  </div>

			  <div class='po-title'>
				<b>PURCHASE ORDER</b>
			  </div>
			  <div class='project-name-2'>
				{{@root.projectName}}
			  </div>
			  <div class='details-row-2'>
				<div class='details-label-2'>TIN:<b
				  >{{@root.vendorTin}}</b></div>
			  </div>
			  <div class='header'>
				<div class='details-row-2'>
				  <div class='details-label-2'>BUSINESS STYLE:
					<b>{{@root.contactName}} {{@root.contactNumber}}</b></div>
				</div>
				<div class='date-extracted-section'>
				  <span> Date Extracted: <b>{{@root.dateExtracted}}</b></span>
				</div>
			  </div>

			  <div>
				<div class='project-company-header'></div>
			  </div>

			</div>
		  {{/if}}

		  <div class='items-header'>
			<div class='items-title'>Items</div>
			<div>
			  <b>
				{{@pageData.from}}
				-
				{{@pageData.to}}
			  </b>

			  of
			  {{@root.totalItems}}
			</div>
		  </div>

		  <div class='table-container'>
			<table>
			  <thead>
				<tr>
				  <th><center>#</center></th>
				  <th>Item Name</th>
				  <th>Latest Note</th>
				  <th>Requested Qty</th>
				  <th>Unit</th>
				  <th>Unit Price</th>
				  <th>Discount</th>
				  <th>Total Price</th>
				</tr>
			  </thead>
			  <tbody>
				{{#each @pageData.data}}
				  <tr>
					<td class='num-col'><center>{{id}}</center></td>
					<td class='item-name-cell'>
					  <u>{{itemName}}</u>
					</td>
					<td class='item-name-cell'>
					  {{latestNote}}
					</td>
					<td class='base-col'>{{requestedQty}}</td>
					<td class='unit-col'>
					  {{unit}}
					</td>
					<td class='pricing-col text-right'>
					  {{unitPrice}}
					</td>
					<td class='discount-col text-right'>
					  {{discount}}
					</td>
					<td class='pricing-col text-right'>
					  {{totalPrice}}
					</td>
				  </tr>
				{{/each}}
				 {{#if @last}}
				  <tr>
					<td class='num-col'></td>
					<td class='item-name-cell'>
					
					</td>
					<td class='item-name-cell'>
					
					</td>
					<td class='base-col'></td>
					<td class='unit-col'>
					 
					</td>
					<td class='pricing-col text-right'>
					 
					</td>
					<td class='discount-col text-right'>
					 <b>{{@root.totalDiscount}}</b>
					</td>
					<td class='pricing-col text-right'>
					  <b>{{@root.grandTotal}}</b>
					</td>
				  </tr>
				   {{/if}}
			  </tbody>
			</table>
		  </div>

		  {{#if @last}}
			<div class="footer-wrapper">
			  <div class="bottom-note-section">
				Total Amount in words: <b>{{@root.amountInWords}}</b>
			  </div>

			  <div class="section">
				<div class="section-terms">
				  <div class="details-grid-outer2">
					<div class="section-title">Terms and Conditions</div>
					<div class="details-grid">
					  <div class="details-row">
						<div class="details-label">Terms of Payment:</div>
						<div><b>{{@root.termsOfPayment}}</b></div>
					  </div>
					  <div class="details-row">
						<div class="details-label">Warranty:</div>
						<div><b>{{@root.warranty}}</b></div>
					  </div>
					  <div class="details-row">
						<div class="details-label">Delivery Date:</div>
						<div><b>{{@root.deliveryTime}}</b></div>
					  </div>
					  <div class="details-row">
						<div class="details-label">Other Specifications:</div>
						<div><b>{{@root.otherSpecifications}}</b></div>
					  </div>
					</div>
				  </div>
				</div>

				<div class="section-purchasing">
				  <div class="details-grid-outer2">
					<div class="section-title">Purchasing</div>
					<div class="details-grid">
					  <div class="details-row">
						<div class="details-label">List Prepared By:</div>
						<div><b>{{@root.purchasingStaffName}}</b></div>
					  </div>
					  <div class="details-row">
						<div class="details-label">Noted By:</div>
						<div><b>{{@root.notedBy}}</b></div>
					  </div>
					  <div class="details-row">
						<div class="details-label">Approved By:</div>
						<div><b>{{@root.approvedBy}}</b></div>
					  </div>
					</div>
				  </div>
				</div>
			  </div>

			  <div class="grow">
				<div class="h-full w-full">
				</div>
		 	  </div>

			  <div class="footer">
				<div>
					<u>Reminders:</u>
				</div>
				<div>
					<b>Please indicate PO# on invoice and delivery receipt.</b>
				</div>
				<div>
					<b>Please issue invoice wiith complete details. Company, TIN, address, Business style.</b>
				</div>
			  </div>

		  </div>

		  {{/if}}

		</div>
	  {{/each}}

	</div>
  </body>
</html>
