<html lang='en'>
  <head>
    <meta charset='UTF-8' />
    <meta name='viewport' content='width=device-width, initial-scale=1.0' />
    <title>Purchase Request System</title>
    <link rel='preconnect' href='https://rsms.me/' />
    <link rel='stylesheet' href='https://rsms.me/inter/inter.css' />
    <style>
      @media print { .page, .page-break { break-after: page; } footer {
      break-inside: avoid; page-break-inside: avoid; margin-top: 15px;
      padding-top: 15px; } } @page { size: letter; } body { font-family:
      'Inter', sans-serif; font-size: 12px; line-height: 1.2; color: #000;
      margin: 0; padding: 0; box-sizing: border-box; } /* Support for Inter
      variable font */ @supports (font-variation-settings: normal) { body {
      font-family: 'Inter var', sans-serif; } } .wrapper { max-width: 7.5in;
      margin: 0 auto; padding: 0; } .header { display: flex; justify-content:
      space-between; align-items: flex-start; margin-top: 15px; padding-top:
      15px; } .logo-title { display: flex; align-items: center; gap: 8px; }
      .logo { font-size: 20px; line-height: 1; } .title { font-weight: bold;
      font-size: 12px; color: #754445; } .page-info { text-align: right;
      font-size: 12px; } .project-company-header { font-weight: bold; font-size:
      14px; display: flex; justify-content: center; align-items: center;
      text-align: center; width: 100%; padding-bottom: 10px; border-bottom: 1px
      solid #000; } .pr-number { margin-top: 10px; display: flex; align-items:
      center; justify-content: space-between; /*gap: 10px;*/ } .pr-label {
      font-size: 14px; font-weight: bold; } .pr-value { text-decoration:
      underline; font-weight: bold; } .date-section { display: flex;
      align-items: center; } .date-label { color: #323232; font-size: 7px;
      margin-left: 30px; } .date-value { font-size: 8px; font-weight: bold; }
      .note-section { font-size: 8px; margin-top: 10px; } .bottom-note-section {
      font-size: 8px; margin-top: 10px; text-align: right; width: 100%;
      margin-bottom: 25px; } .amount-words { font-style: italic; font-weight:
      bold; } .bottom-note-section input { border: none; border-bottom: 1px
      solid #000; width: 30%; font-size: 8px; margin-left: 5px; font-style:
      italic; } .section { margin: 10px 0; } .section-title { font-weight: bold;
      margin-bottom: 5px; font-size: 12px; } .section-description {
      margin-bottom: 5px; font-size: 9px; } .details-grid-outer { border: 1px
      solid #ccc; border-radius: 10px; padding: 10px; padding-bottom: 0px; }
      .details-grid { display: grid; grid-template-columns: 1fr 1fr 1fr; gap:
      10px; padding: 10px; font-size: 10px; } .signing-details-grid { display:
      grid; grid-template-columns: 1fr 1fr 1fr 1fr; gap: 10px; padding: 10px;
      font-size: 10px; } .details-row { display: flex; flex-direction: column; }
      .details-label { color: #323232; font-weight: normal; font-size: 8px;
      margin-bottom: 4px; } .signing-details-label { color: #323232;
      font-weight: normal; font-size: 8px; } .signing-details-label input {
      margin-top: 20px; border-bottom: solid 1px; width: 150px; }
      .checks-container { display: flex; flex-direction: row; align-items:
      center; gap: 10px; width: 100%; padding: 0px 0 10px; } .check-item {
      display: flex; align-items: center; white-space: nowrap; } .check-item
      label { font-size: 8px; margin-right: 3px; } /* Style for the checkboxes
      */ .check-item input[type='checkbox'] { width: 10px; height: 10px;
      margin-right: 3px; } .check-textfield { border: none; border-bottom: 1px
      solid #000; font-size: 7px; padding: 0; margin: 0; } input, select {
      border: none; background: transparent; width: 100%; font-family: inherit;
      font-size: inherit; outline: none; } .items-header { display: flex;
      justify-content: space-between; align-items: center; margin-top: 15px; }
      .items-title { font-weight: bold; } .table-container { border-radius: 8px;
      overflow: hidden; border: 1px solid #ccc; margin-top: 5px; margin-bottom:
      15px; padding: 1px; } table { width: 100%; border-collapse: collapse;
      margin: 0; border: none; } th, td { border: 1px solid #ccc; padding: 4px;
      text-align: left; font-size: 10px; } /* Remove border from outer edges of
      table cells */ tr:first-child th { border-top: none; } tr:last-child td {
      border-bottom: none; } th:first-child, td:first-child { border-left: none;
      } th:last-child, td:last-child { border-right: none; } th, td { padding:
      4px; text-align: left; font-size: 10px; } th { color: #4f575e;
      vertical-align: top; text-align: left; font-weight: bold; font-size: 8px;
      } td { height: 30px; } .base-col { width: 60px; } .num-col { width: 20px;
      } .pricing-col { width: 60px; } .pricing-col input { width: 90%;
      margin-right: 5px; } .item-name-cell { vertical-align: center; word-break:
      break-all; word-wrap: break-word; white-space: normal; padding: 4px; }
      .item-name-input { color: #4f575e; width: 100%; outline: none; border:
      none; background: transparent; font-family: inherit; font-size: inherit;
      text-decoration: underline; word-break: break-all; word-wrap: break-word;
      white-space: normal; text-align: left; resize: none; overflow: hidden;
      text-overflow: ellipsis; display: -webkit-box; -webkit-line-clamp: 3;
      -webkit-box-orient: vertical; line-clamp: 3; text-overflow: ellipsis; }
      .text-left { text-align: left; } .text-right { text-align: right; }
      .footer { margin-top: 20px; text-align: left; font-size: 10px; color:
      #666; }

    </style>
  </head>

  <body>
    <div class='wrapper'>
      {{#each pages}}
        <div class='page'>
          <div class='header'>
            <div class='logo-title'>
              <div class='title'>Non-RS Payment Request</div>
            </div>
            <div class='page-info'>
              Page
              <span style='width: 20px; text-align: center; font-weight:bold'>
                {{currentPage}}
              </span>
              of
              <input
                type='text'
                style='width: 20px; text-align: center'
                value={{@root.totalPages}}
              />
            </div>
          </div>

          <div>
            <div>
              <span class='project-company-header' name='project-company'>
                {{@root.projectCompany}}
              </span>
            </div>
          </div>

          <div class='pr-number'>
            <div class='pr-label'>
              P.R. #
              <span type='text' style='width: 200px; font-weight: bold'>
                {{@root.rsNumber}}
              </span>
            </div>
            <div class='date-label'>
              Date Prepared:
              <span
                class='date-value'
                type='text'
                style='width: 60px; font-weight: bold'
              >
                {{convertDateToDDMMMYYYY @root.datePrepared}}
              </span>
            </div>
            <div class='date-label'>
              Date Needed:
              <span
                class='date-value'
                type='text'
                style='width: 60px; font-weight: bold'
              >{{convertDateToDDMMMYYYY @root.dateNeeded}}</span>
            </div>
            <div class='date-label'>
              Time Needed:
              <input
                class='date-value'
                type='text'
                name='timeNeeded'
                style='width: 60px; font-weight: bold'
              />
            </div>
          </div>

          {{#if @first}}
            <div class='note-section'>
              <span>
                <b>NOTE: </b>Processing of voucher request upon receipt of
                Accounting takes three (3) days.
              </span>
            </div>

            <div class='section'>
              <div class='details-grid-outer'>
                <div class='section-title'>Request Details</div>
                <div class='details-grid'>
                  <div class='details-row'>
                    <div class='details-label'>Payable To:</div>
                    <div style='font-weight: 500;'>{{@root.payableTo}}</div>
                  </div>
                  <div class='details-row'>
                    <div class='details-label'>Tin:</div>
                    <div style='font-weight: 500;'>{{@root.tin}}</div>
                  </div>
                  <div class='details-row'>
                    <div class='details-label'>Total PR Balance:</div>
                    <div style='font-weight: 500;'>{{formatCurrency
                        @root.requestBalance
                        3
                        'currency'
                      }}</div>
                  </div>
                </div>
              </div>
            </div>
          {{/if}}

          <div class='items-header'>
            <div class='items-title'>Items</div>
            <div>
              <span style='width: 20px; text-align: center; font-weight: bold'>
                {{itemsCount}}
              </span>
              -
              <span style='width: 20px; text-align: center; font-weight: bold'>
                {{currentTotalItemsCount}}
              </span>
              of
              <span style='width: 20px; text-align: center;'>
                {{@root.totalItems}}
              </span>
            </div>
          </div>

          <div class='table-container'>
            <table>
              <thead>
                <tr>
                  <th>#</th>
                  <th>Item Name</th>
                  <th>Qty</th>
                  <th>Unit</th>
                  <th>Unit Price</th>
                  <th>Discount</th>
                  <th>Total Price</th>
                </tr>
              </thead>
              <tbody>
                {{#each items}}
                  <tr>
                    <td class='num-col'>
                      {{itemNum}}
                    </td>
                    <td class='item-name-cell'>
                      <span class='item-name-input'>
                        {{itemName}}
                      </span>
                    </td>
                    <td class='base-col'>
                      <span>
                        {{qty}}
                      </span>
                    </td>
                    <td class='base-col'>
                      <span>
                        {{unit}}
                      </span>
                    </td>
                    <td class='pricing-col text-right'>
                      <span>
                        {{formatCurrency unitPrice 3 'currency'}}
                      </span>
                    </td>
                    <td class='pricing-col'>
                      <span class='text-left'>
                        {{formatCurrency discountValue 2 'currency'}}
                      </span>
                    </td>
                    <td class='pricing-col text-right'>
                      <span>
                        {{formatCurrency discountedPrice 3 'currency'}}
                      </span>
                    </td>
                  </tr>
                {{/each}}
                {{#if @last}}
                  {{#if (lt items.length 15)}}
                    <tr>
                      <td class='num-col'>
                        <span></span>
                      </td>
                      <td class='item-name-cell'>
                        <span class='item-name-input'></span>
                      </td>
                      <td class='base-col'>
                        <span></span>
                      </td>
                      <td class='base-col'>
                        <span></span>
                      </td>
                      <td class='pricing-col text-right'>
                        <span></span>
                      </td>
                      <td class='pricing-col'>
                        <span class='text-left'></span>
                      </td>
                      <td
                        class='pricing-col text-right'
                        style='font-weight: bold'
                      >
                        <span>{{formatCurrency
                            @root.totalPrice
                            3
                            'currency'
                          }}</span>
                      </td>
                    </tr>
                  {{/if}}
                {{/if}}
              </tbody>
            </table>
          </div>
          {{#if @last}}
            <footer>
              <div class='bottom-note-section'>
                Total Amount in words:
                <span>
                  <em>
                    <strong>
                      {{amountInWords @root.totalPrice}}
                    </strong>
                  </em>
                </span>
              </div>

              <div class='section'>
                <div class='details-grid-outer'>
                  <div class='section-description'>
                    Checks pay to "Cash" uncrossed must be approved by Pres. /
                    EVP SVP. Please issue checks.
                  </div>
                  <div class='checks-container'>
                    <div class='check-item'>
                      <input type='checkbox' />
                      <label for='cash'>Pay To Cash</label>
                      <input type='text' class='check-textfield' />
                    </div>

                    <div class='check-item'>
                      <input type='checkbox' />
                      <label for='encashment'>For Encashment</label>
                      <input type='text' class='check-textfield' />
                    </div>

                    <div class='check-item'>
                      <input type='checkbox' />
                      <label for='uncrossed'>Uncrossed Check</label>
                      <input type='text' class='check-textfield' />
                    </div>

                    <div class='check-item'>
                      <input type='checkbox' />
                      <label for='managers'>Manager's Check</label>
                      <input type='text' class='check-textfield' />
                    </div>
                  </div>
                </div>
              </div>

              <div class='section'>
                <div class='details-grid-outer'>
                  <div class='section-description'>Supporting Documents:</div>
                  <div class='checks-container'>
                    <div class='check-item'>
                      <input type='checkbox' />
                      <label for='cash'>Attached</label>
                      <input type='text' />
                    </div>

                    <div class='check-item'>
                      <input type='checkbox' />
                      <label for='encashment'>To Follow</label>
                      <input type='text' />
                    </div>

                    <div class='check-item'>
                      <input type='checkbox' />
                      <label for='uncrossed'>None Available</label>
                      <input type='text' />
                    </div>

                    <div class='check-item'>
                      <input type='checkbox' />
                      <label for='managers'>Orig. RS/OS/CS</label>
                      <input type='text' />
                    </div>
                  </div>
                </div>
              </div>

              <div class='section'>
                <div class='details-grid-outer'>
                  <div class='checks-container'>
                    <div class='signing-details-label'>
                      <label for='requested-by'>Requested By: </label>
                      <input type='text' />
                    </div>

                    <div class='signing-details-label'>
                      <label for='endorsed-by'>Endorsed By:</label>
                      <input type='text' />
                    </div>

                    <div class='signing-details-label'>
                      <label for='approved-by'>Approved By:</label>
                      <input type='text' />
                    </div>

                    <div class='signing-details-label'>
                      <label for='countersigned-by'>Countersigned By: </label>
                      <input type='text' />
                    </div>
                  </div>
                </div>
              </div>
            </footer>
          {{/if}}
        </div>
      {{/each}}
    </div>
  </body>
</html>