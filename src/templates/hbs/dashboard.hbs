<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="preconnect" href="https://rsms.me/" />
    <link rel="stylesheet" href="https://rsms.me/inter/inter.css" />
    <style>
      @media print {
        .page {
          break-after: page;
        }
        footer {
          break-inside: avoid;
          page-break-inside: avoid;
          margin-top: 15px;
          padding-top: 15px;
        }
      }
      @page {
        size: legal;
        margin: 0.5in;
      }
      body {
        font-family: 'Inter', sans-serif;
        font-size: 12px;
        line-height: 1.2;
        color: #000;
        margin: 0;
        padding: 0;
        width: 8.5in;
        height: 14in;
        box-sizing: border-box;
      }
      @supports (font-variation-settings: normal) {
        body {
          font-family: 'Inter var', sans-serif;
        }
      }
      .wrapper {
        max-width: 7.5in;
        margin: 0 auto;
        padding: 0;
        padding-bottom: 40px;
      }
      .header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-top: 1.5rem;
      }
      .logo-title {
        display: flex;
        align-items: center;
        gap: 0 0.5rem;
      }
      .logo {
        font-size: 20px;
        line-height: 1;
      }
      .title {
        font-weight: bold;
        font-size: 12px;
        color: #450a0a;
      }
      .page-info {
        text-align: right;
        font-size: 12px;
        white-space: nowrap;
        margin-top: 10px;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 2px;
      }
      .page-number-input {
        border: none;
        width: 18px;
        text-align: center;
        padding: 1px;
        font-weight: bold;
        margin: 0;
      }

      .dashboard-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 0.75rem 0;
      }
      .dashboard-text-container {
        display: flex;
        width: 100%;
      }
      .dashboard-text {
        font-weight: bold;
      }
      .dashboard-input {
        border: none;
        background: transparent;
        width: 100%;
        font-family: inherit;
        font-size: inherit;
        outline: none;
        padding: 0;
        font-weight: bold;
        max-width: 300px;
        text-align: start;
      }
      .row-info {
        text-align: right;
        font-size: 12px;
        white-space: nowrap;
      }
      .row-count-input {
        border: none;
        background: transparent;
        font-family: inherit;
        font-size: inherit;
        outline: none;
        width: 32px;
        padding: 1px;
        text-align: center;
        font-weight: bold;
      }
      .row-count-input.total-rows {
        text-align: right;
        width: 32px;
      }

      .table-container {
        width: 100%;
        table-layout: fixed;
        border-collapse: separate;
        border-spacing: 0;
        border: 1.5px solid #ced4da;
        border-radius: 8px;
        overflow: hidden;
        margin-top: 5px;
        margin-bottom: 15px;
        padding: 1px;
      }
      table {
        width: 100%;
        border-collapse: collapse;
        margin: 0;
        border: none;
      }
      thead {
        display: table-header-group;
      }
      tr {
        page-break-inside: avoid;
      }
      th {
        background-color: #f8f9fa;
        color: #4f575e;
        text-align: left;
        font-weight: bold;
        font-size: 8px;
        padding: 8px 4px;
        border-bottom: 1px solid #ced4da;
        border-right: none;
      }      td {
        font-size: 8px;
        height: 45px;
        vertical-align: middle;
        padding: 4px;
        border-bottom: 1px solid #ced4da;
        border-right: none;
        text-align: center;
      }
      tbody tr:last-child td {
        border-bottom: none;
      }

      .table-input {
        border: none;
        background: transparent;
        width: 100%;
        max-width: 100%;
        box-sizing: border-box;
        font-family: inherit;
        font-size: inherit;
        outline: none;
        text-align: center;
        background-color: #e6f0ff;
        padding: 1px;
        font-weight: bold;
        color: #4f575e;
      }
      .ref-number-input {
        text-decoration: underline;
      }
      .textarea-input {
        width: 100%;
        box-sizing: border-box;
        font-family: inherit;
        font-size: inherit;
        padding: 1px;
        background-color: #e6f0ff;
        border: none;
        color: #4f575e;
        font-weight: bold;
        resize: none;
        overflow-wrap: break-word;
      }
    </style>
  </head>
  <body>
    <div class="wrapper">
      {{#each pages}}
      <div class="page" {{#if @index}}style="page-break-before: always;"{{/if}}>
        <div class="header">
          <div class="logo-title">
                <svg
                    width='22'
                    height='24'
                    viewBox='0 0 22 24'
                    fill='none'
                    xmlns='http://www.w3.org/2000/svg'
                  >
                    <path
                      d='M4.86392 9.24902H4.53815V14.3166H4.86392V9.24902Z'
                      fill='#653332'
                    />
                    <path
                      fill-rule='evenodd'
                      clip-rule='evenodd'
                      d='M13.3701 9.24902H13.7321L14.8904 14.3166H14.5465L14.2027 12.7239H12.8815L12.5376 14.3166H12.2119L13.3701 9.24902ZM13.5692 9.79197H13.533L12.9848 12.2895H14.1022L13.5692 9.79197Z'
                      fill='#653332'
                    />
                    <path
                      d='M8.48357 14.3166H8.73694V12.4162L9.71425 9.24902H9.38848L8.61025 11.8733L7.83203 9.24902H7.52435L8.48357 12.4162V14.3166Z'
                      fill='#653332'
                    />
                    <path
                      d='M10.6916 9.24902H10.3658V14.3166H11.7413V13.846H10.6916V9.24902Z'
                      fill='#653332'
                    />
                    <path
                      fill-rule='evenodd'
                      clip-rule='evenodd'
                      d='M18.8721 14.3166V9.24902C18.8721 9.24902 19.4874 9.24902 19.8675 9.24902C20.0847 9.24902 20.3018 9.32924 20.6922 9.71958C21.2832 10.3106 21.2611 11.7828 21.2611 11.7828C21.2611 11.7828 21.3153 13.0135 20.7724 13.7917C20.4256 14.2887 19.7951 14.3166 19.7951 14.3166H18.8721ZM19.1254 13.846V9.75578C19.1254 9.75578 19.6865 9.75578 20.0123 9.75578C20.0666 9.75578 20.3474 9.79197 20.6922 10.3892C20.9801 10.8879 20.9715 11.8009 20.9715 11.8009C20.9715 11.8009 20.9801 12.7501 20.6922 13.2488C20.3474 13.846 19.9399 13.846 19.9399 13.846H19.1254Z'
                      fill='#653332'
                    />
                    <path
                      d='M15.8315 9.24902H15.5781V14.3166H15.8315V9.90056L17.4604 14.3166H17.8223V9.24902H17.5327V13.6469L15.9039 9.24902H15.8315Z'
                      fill='#653332'
                    />
                    <path
                      d='M6.49275 9.24902H6.20317H5.58783V9.75578H6.20317V14.3166H6.49275V9.75578H7.10809V9.24902H6.49275Z'
                      fill='#653332'
                    />
                    <path
                      d='M2.29395 14.4252C1.38903 14.4252 0.737488 13.2421 0.737488 11.7828C0.737488 10.3235 1.43434 9.17664 2.29395 9.17664C2.89179 9.17664 3.41937 9.71267 3.66942 10.5521H3.30373C3.08351 10.0047 2.72062 9.611 2.29395 9.611C1.59615 9.611 1.06326 10.613 1.06326 11.7925C1.06326 12.972 1.55937 13.9546 2.29395 13.9546C2.70634 13.9546 3.05893 13.5844 3.28114 13.0678H3.65132C3.39608 13.8775 2.87881 14.4252 2.29395 14.4252Z'
                      fill='#653332'
                    />
                    <path
                      fill-rule='evenodd'
                      clip-rule='evenodd'
                      d='M11.0355 0.959961C13.781 0.959961 15.4696 3.45753 16.7799 7.43917H12.7005C12.3916 4.16172 11.7953 1.6839 11.144 1.6839C10.4027 1.6839 9.33458 4.26379 8.94273 8.3079H5.16648C6.00121 3.95388 8.2899 0.959961 11.0355 0.959961ZM5.12189 15.2939C5.94324 19.7823 8.37603 23.04 11.0355 23.04C13.3241 23.04 15.6223 20.3991 16.6934 16.5969H12.667C12.2898 19.798 11.5832 21.9722 10.9812 21.9722C10.4258 21.9721 9.47657 19.1606 9.04464 15.2939H5.12189Z'
                      fill='#653332'
                    />
                  </svg>
            <div class="title">Purchase Request System</div>
          </div>
          <div class="page-info">
            <span>Page</span>
            <input
              type="text"
              name="currentPage"
              class="page-number-input"
              value="{{this.currentPage}}"
            />
            <span>of</span>
            <input
              type="text"
              name="totalPage"
              class="page-number-input"
              value="{{../totalPage}}"
            />
          </div>
        </div>
        <div class="dashboard-header">
          <div class="dashboard-text-container">
            <span class="dashboard-text">Dashboard - &nbsp;</span>
            <input
              type="text"
              name="dashboardText"
              class="dashboard-input"
              value="{{../dashboardText}}"
            />
          </div>
          <div class="row-info">
            <input
              type="text"
              name="itemsCount"
              class="row-count-input"
              value="{{this.itemsCount}}"
            />
            <span class="dashboard-text">-</span>
            <input
              type="text"
              name="currentTotalItemsCount"
              class="row-count-input"
              value="{{this.currentTotalItemsCount}}"
            />
            <span>of</span>
            <input
              type="text"
              name="totalRows"
              class="row-count-input total-rows"
              value="{{../totalRows}}"
            />
          </div>
        </div>
        <div class="table-container">
          <table>
            <thead>
              <tr>
                <th>Ref. Number</th>
                <th>Document Type</th>
                <th>Requestor</th>
                <th>Company</th>
                <th>Proj/Dept</th>
                <th>Last Updated</th>
                <th>Status</th>
              </tr>
            </thead>
            <tbody>
              {{#each items}}
              <tr>
                <td>
                  <textarea
                    name="ref-no-r{{@../index}}-{{@index}}"
                    class="textarea-input ref-number-input"
                  >{{refNumber}}</textarea>
                </td>
                <td>
                  <textarea
                    name="doc-type-r{{@../index}}-{{@index}}"
                    class="textarea-input"
                  >{{docType}}</textarea>
                </td>
                <td>
                  <textarea
                    name="requestor-r{{@../index}}-{{@index}}"
                    class="textarea-input"
                  >{{requestor}}</textarea>
                </td>
                <td>
                  <textarea
                    name="company-r{{@../index}}-{{@index}}"
                    class="textarea-input"
                  >{{company}}</textarea>
                </td>
                <td>
                  <textarea
                    name="proj-dept-r{{@../index}}-{{@index}}"
                    class="textarea-input"
                  >{{projDept}}</textarea>
                </td>
                <td>
                  <textarea
                    name="last-update-r{{@../index}}-{{@index}}"
                    class="textarea-input"
                  >{{lastUpdate}}</textarea>
                </td>
                <td>
                  <textarea
                    name="status-r{{@../index}}-{{@index}}"
                    class="textarea-input"
                  >{{status}}</textarea>
                </td>
              </tr>
              {{/each}}
            </tbody>
          </table>
        </div>
      </div>
      {{/each}}
    </div>
    <script>
      function autoResizeTextarea(ta) {
        ta.style.height = 'auto';
        const contentHeight = ta.scrollHeight;

        const extra = 10;

        ta.style.height = contentHeight + extra + 'px';
      }

      document.addEventListener('DOMContentLoaded', () => {
        document.querySelectorAll('.textarea-input').forEach(ta => {
          ta.style.overflowY = 'hidden';
          autoResizeTextarea(ta);
          ta.addEventListener('input', () => autoResizeTextarea(ta));
        });

        document.querySelectorAll('textarea[name^="status-r"]').forEach(input => {
          let statusValue = input.value;
          statusValue = statusValue.toUpperCase();
          statusValue = statusValue.replaceAll('_', ' ');
          input.value = statusValue;
        });
      });
      /* Alternative (also working) but fragment on some scenarios given that it prioritizes getting 22 rows per page, ergo it limits the maximum height of each row.
      function autoResizeTextarea(ta) {
        ta.style.height = 'auto';
        const cs         = window.getComputedStyle(ta);
        const padBorder  = 
          parseFloat(cs.paddingTop) +
          parseFloat(cs.borderTopWidth) +
          parseFloat(cs.borderBottomWidth);

        // scrollHeight includes the content only, so add padding+border + your extra
        const newHeight = ta.scrollHeight + padBorder + 5; 
        ta.style.height = newHeight + 'px';
      }
      document.addEventListener('DOMContentLoaded', () => {
        document.querySelectorAll('.textarea-input').forEach(ta => {
          ta.style.overflowY = 'hidden';
          autoResizeTextarea(ta);
          ta.addEventListener('input', () => autoResizeTextarea(ta));
        });
      });
      
      */
    </script>
  </body>
</html>
