const { z } = require('zod');
const { sort } = require('../constants');
const { sortSchema } = require('./sortEntity');
const { stringFieldError } = require('../../app/utils');

const createCanvassRequisitionSchema = z.object({
  requisitionId: z.number({
    required_error: 'Requisition ID is required',
  }),
  status: z.string({
    required_error: 'Status is required',
  }),
  notes: z.string().optional(),
  isDraft: z.boolean({
    required_error: 'Draft status is required',
  }),
  attachments: z
    .array(
      z.object({
        filePath: z.string(),
        originalname: z.string(),
      }),
    )
    .optional(),
});

const canvassRequisitionSortSchema = sortSchema(
  sort.CANVASS_REQUISITION_SORT_COLUMNS,
);

module.exports = {
  createCanvassRequisitionSchema,
  canvassRequisitionSortSchema,
};
