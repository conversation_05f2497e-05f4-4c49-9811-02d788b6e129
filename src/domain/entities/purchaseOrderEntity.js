const { z } = require('zod');
const { sort, requisition, filter } = require('../constants');
const { sortSchema } = require('./sortEntity');
const { filterSchema } = require('./filterEntity');
const {
  createIdParamsSchema,
  createOptionalIdParamsSchema,
  createNumberSchema,
} = require('../../app/utils');

const getPurchaseOrderByIdParams = z.object({
  purchaseOrderId: createIdParamsSchema('Purchase Order ID'),
});

const getPurchaseOrderPDFByIdParams = z.object({
  id: createIdParamsSchema('Purchase Order ID'),
});

const getAllPurchaseOrderParamsSchema = z.object({
  requisitionId: createIdParamsSchema('Requisition ID'),
});

const getPurchaseOrderApproverEntityParams = z.object({
  purchaseOrderId: createIdParamsSchema('Requisition ID'),
});

const getAllPurchaseOrderQuerySchema = z.object({
  sort: z.enum(['asc', 'desc']).optional(),
  sortBy: sortSchema(sort.PURCHASE_ORDER_SORT_COLUMNS).optional(),
  page: z.string().regex(/^\d+$/).transform(Number).optional(),
  limit: z.string().regex(/^\d+$/).transform(Number).optional(),
});

const updatePurchaseOrderParams = z.array(
  z.object({
    quantityPurchased: z.preprocess(
      (value) => {
        return parseFloat(value);
      },
      z.number().min(1, 'Quantity Purchased must be greater than 0'),
    ),

    purchaseOrderItemId: createIdParamsSchema('Purchase Order Item ID'),
  }),
);

const submitPurchaseOrderBodySchema = z
  .object({
    warrantyId: createOptionalIdParamsSchema('Warranty ID'),
    isNewDeliveryAddress: z.boolean(),
    newDeliveryAddress: z
      .string()
      .max(100, 'New delivery address must not exceed 100 characters')
      .nullable(),
    addedDiscount: z
      .number()
      .nonnegative()
      .optional()
      .refine(
        (val) => {
          if (val === undefined || val === '') return true; // optional
          return /^\d+(\.\d{1,2})?$/.test(val);
        },
        {
          message:
            'Discount must be a valid number with up to 2 decimal places',
        },
      ),
    isAddedDiscountFixedAmount: z.boolean().optional(),
    isAddedDiscountPercentage: z.boolean().optional(),
  })
  .superRefine((data, ctx) => {
    if (data.isNewDeliveryAddress && !data.newDeliveryAddress) {
      ctx.addIssue({
        path: ['newDeliveryAddress'],
        code: z.ZodIssueCode.custom,
        message: 'New delivery address is required',
      });
    }

    const val = data.addedDiscount;
    const isFixed = data.isAddedDiscountFixedAmount;
    const isPercentage = data.isAddedDiscountPercentage;

    if (val !== undefined && val !== null) {
      if (isFixed) {
        const maxFixedAmount = 99999999999999999999; // 20 digits
        if (val > maxFixedAmount) {
          ctx.addIssue({
            path: ['addedDiscount'],
            code: z.ZodIssueCode.too_big,
            message: 'Discount must not exceed 20 digits',
            maximum: maxFixedAmount,
            type: 'number',
            inclusive: true,
          });
        }
      }

      if (isPercentage) {
        if (val > 100) {
          ctx.addIssue({
            path: ['addedDiscount'],
            code: z.ZodIssueCode.too_big,
            message: 'Percentage discount must not exceed 100%',
            maximum: 100,
            type: 'number',
            inclusive: true,
          });
        }
      }

      if (!isFixed && !isPercentage && val > 0) {
        ctx.addIssue({
          path: ['isAddedDiscountFixedAmount'],
          code: z.ZodIssueCode.custom,
          message:
            'Either fixed amount or percentage must be selected if a discount is provided',
        });
        ctx.addIssue({
          path: ['isAddedDiscountPercentage'],
          code: z.ZodIssueCode.custom,
          message:
            'Either fixed amount or percentage must be selected if a discount is provided',
        });
      }
    }
  });

const addAdditionalFeesSchema = z
  .object({
    withholdingTaxDeduction: z
      .number()
      .nonnegative()
      .optional()
      .refine(
        (val) => {
          if (val === undefined || val === '') return true; // optional
          return /^\d+(\.\d{1,2})?$/.test(val);
        },
        {
          message:
            'Withholding tax deduction must be a valid number with up to 2 decimal places',
        },
      ),
    deliveryFee: z
      .number()
      .nonnegative()
      .optional()
      .refine(
        (val) => {
          if (val === undefined || val === '') return true; // optional
          return /^\d+(\.\d{1,2})?$/.test(val);
        },
        {
          message:
            'Delivery fee must be a valid number with up to 2 decimal places',
        },
      ),
    tip: z
      .number()
      .nonnegative()
      .optional()
      .refine(
        (val) => {
          if (val === undefined || val === '') return true; // optional
          return /^\d+(\.\d{1,2})?$/.test(val);
        },
        {
          message: 'Tip must be a valid number with up to 2 decimal places',
        },
      ),
    extraCharges: z
      .number()
      .nonnegative()
      .optional()
      .refine(
        (val) => {
          if (val === undefined || val === '') return true; // optional
          return /^\d+(\.\d{1,2})?$/.test(val);
        },
        {
          message:
            'Extra charges must be a valid number with up to 2 decimal places',
        },
      ),
  })
  .strict();

const addPurchaseOrderAdhocApproverSchema = z
  .object({
    approverId: createNumberSchema('Approver Id'),
  })
  .strict();

const submitWithUpdatesSchema = z
  .object({
    // Action flags to determine which parts to process
    action: z.object({
      mainFields: z.boolean().default(false),
      items: z.boolean().default(false),
      additionalFees: z.boolean().default(false),
    }),

    // Submit PO fields - processed if action.addressAndDiscount is true
    warrantyId: createOptionalIdParamsSchema('Warranty ID').optional(),
    isNewDeliveryAddress: z.boolean().optional(),
    newDeliveryAddress: z
      .string()
      .max(100, 'New delivery address must not exceed 100 characters')
      .nullable()
      .optional(),
    addedDiscount: z
      .number()
      .nonnegative()
      .optional()
      .refine(
        (val) => {
          if (val === undefined || val === '') return true; // optional
          return /^\d+(\.\d{1,2})?$/.test(val);
        },
        {
          message:
            'Discount must be a valid number with up to 2 decimal places',
        },
      ),
    isAddedDiscountFixedAmount: z.boolean().optional(),
    isAddedDiscountPercentage: z.boolean().optional(),

    // Additional fees fields - processed if action.additionalFees is true
    withholdingTaxDeduction: z.number().nonnegative().optional(),
    deliveryFee: z.number().nonnegative().optional(),
    tip: z.number().nonnegative().optional(),
    extraCharges: z.number().nonnegative().optional(),

    // Update PO items - processed if action.items is true
    items: z
      .array(
        z.object({
          quantityPurchased: z.preprocess(
            (value) => {
              return parseFloat(value);
            },
            z.number().min(1, 'Quantity Purchased must be greater than 0'),
          ),
          purchaseOrderItemId: createIdParamsSchema('Purchase Order Item ID'),
        }),
      )
      .optional(),
  })
  .superRefine((data, ctx) => {
    // Validate address and discount fields if that action is enabled
    if (data.action.mainFields) {
      // Check if isNewDeliveryAddress is provided when action.mainFields is true
      if (data.isNewDeliveryAddress === undefined) {
        ctx.addIssue({
          path: ['isNewDeliveryAddress'],
          code: z.ZodIssueCode.custom,
          message: 'New delivery address flag is required.',
        });
      }

      // Validate new delivery address if isNewDeliveryAddress is true
      if (data.isNewDeliveryAddress && !data.newDeliveryAddress) {
        ctx.addIssue({
          path: ['newDeliveryAddress'],
          code: z.ZodIssueCode.custom,
          message:
            'New delivery address is required when new delivery address is selected.',
        });
      }

      // Validate discount fields if addedDiscount is provided
      if (data.addedDiscount !== undefined && data.addedDiscount > 0) {
        const isFixed = data.isAddedDiscountFixedAmount;
        const isPercentage = data.isAddedDiscountPercentage;

        if (isFixed && isPercentage) {
          ctx.addIssue({
            path: ['isAddedDiscountFixedAmount'],
            code: z.ZodIssueCode.custom,
            message:
              'Cannot select both fixed amount and percentage for discount.',
          });
        }

        if (!isFixed && !isPercentage) {
          ctx.addIssue({
            path: ['isAddedDiscountFixedAmount'],
            code: z.ZodIssueCode.custom,
            message:
              'Either fixed amount or percentage must be selected if a discount is provided.',
          });
          ctx.addIssue({
            path: ['isAddedDiscountPercentage'],
            code: z.ZodIssueCode.custom,
            message:
              'Either fixed amount or percentage must be selected if a discount is provided.',
          });
        }

        if (isPercentage && data.addedDiscount > 100) {
          ctx.addIssue({
            path: ['addedDiscount'],
            code: z.ZodIssueCode.too_big,
            message: 'Percentage discount must not exceed 100%',
            maximum: 100,
            type: 'number',
            inclusive: true,
          });
        }
      }
    }

    // Validate items if that action is enabled
    if (data.action.items && (!data.items || data.items.length === 0)) {
      ctx.addIssue({
        path: ['items'],
        code: z.ZodIssueCode.custom,
        message: 'Items are required.',
      });
    }

    // Validate additional fees if that action is enabled
    if (data.action.additionalFees) {
      const hasAnyFee =
        data.withholdingTaxDeduction !== undefined ||
        data.deliveryFee !== undefined ||
        data.tip !== undefined ||
        data.extraCharges !== undefined;

      if (!hasAnyFee) {
        ctx.addIssue({
          path: ['additionalFees'],
          code: z.ZodIssueCode.custom,
          message:
            'At least one fee field must be provided when submitting additional fees.',
        });
      }
    }
  });

module.exports = {
  getPurchaseOrderByIdParams,
  getAllPurchaseOrderParamsSchema,
  getAllPurchaseOrderQuerySchema,
  updatePurchaseOrderParams,
  submitPurchaseOrderBodySchema,
  getPurchaseOrderApproverEntityParams,
  addPurchaseOrderAdhocApproverSchema,
  getPurchaseOrderPDFByIdParams,
  addAdditionalFeesSchema,
  submitWithUpdatesSchema,
};
