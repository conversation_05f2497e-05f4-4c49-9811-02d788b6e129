const { z } = require('zod');
const { createIdParamsSchema } = require('../../app/utils');
const { sort, filter } = require('../constants');
const { sortSchemaV2, sortSchema } = require('./sortEntity');
const { filterSchema } = require('./filterEntity');

/**
 * Schema for item history download query parameters
 */
const downloadItemHistoryQuerySchema = z
  .object({
    type: z.enum(['ofm', 'non-ofm'], {
      required_error: 'Item type is required',
      invalid_type_error: 'Item type must be either "ofm" or "non-ofm"',
    }),
    sortBy: z.string().optional(),
    filterBy: z.string().optional(),
  })
  .strict();

const downloadItemHistoryParamsSchema = z
  .object({
    id: createIdParamsSchema('Item History ID'),
  })
  .strict();

/**
 * Schema for dashboard download query parameters
 */
const downloadDashboardQuerySchema = z
  .object({
    search: z.string().optional(),
    sortBy: z.string().optional(),
    filterBy: z.string().optional(),
    requestType: z.enum(['all', 'pending', 'approved', 'rejected']).optional(),
    limit: z.string().regex(/^\d+$/).transform(Number).optional(),
  })
  .strict();

/**
 * Schema for non-RS dashboard download body
 */
const downloadNonRSDashboardBodySchema = z
  .object({
    category: z.string().optional(),
  })
  .strict();

const downloadItemHistorySortSchema = sortSchemaV2(sort.HISTORY_SORT_COLUMNS);
const downloadItemHistoryFilterSchema = filterSchema(
  filter.HISTORY_FILTER_COLUMNS,
);

/**
 * Schema for OFM Items download query parameters
 */
const downloadOfmItemsQuerySchema = z
  .object({
    searchBy: z.union([z.string(), z.record(z.string())]).optional(),
    filterBy: z.union([z.string(), z.record(z.any())]).optional(),
    sortBy: z
      .union([z.string(), sortSchemaV2(sort.ITEM_SORT_COLUMNS)])
      .optional(),
  })
  .strict();

/**
 * Schema for OFM List download query parameters
 */
const downloadOfmListQuerySchema = z
  .object({
    searchBy: z.union([z.string(), z.record(z.string())]).optional(),
    filterBy: z.union([z.string(), z.record(z.any())]).optional(),
    sortBy: z.union([z.string(), z.record(z.any())]).optional(),
  })
  .strict();

module.exports = {
  downloadItemHistoryQuerySchema,
  downloadItemHistoryParamsSchema,
  downloadDashboardQuerySchema,
  downloadNonRSDashboardBodySchema,
  downloadItemHistorySortSchema,
  downloadItemHistoryFilterSchema,
  downloadOfmItemsQuerySchema,
  downloadOfmListQuerySchema,
};
