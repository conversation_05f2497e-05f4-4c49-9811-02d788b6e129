const { z } = require('zod');
const { sort, filter } = require('../constants');
const { sortSchema } = require('./sortEntity');
const { stringFieldError } = require('../../app/utils');
const { filterSchema } = require('./filterEntity');

const getSupplierByIdSchema = z.object({
  id: z
    .string()
    .min(1, 'Supplier id is required')
    .nonempty('Supplier id cannot be empty')
    .refine((val) => val !== ':id', {
      message: 'No supplier id provided',
    }),
});

const updateSupplierSchema = z
  .object({
    contactPerson: z
      .string({
        errorMap: () => ({ message: 'Contact Person is a Required Field' }),
      })
      .trim()
      .max(100, 'Contact must be at most 100 characters')
      .refine((value) => value === '' || /^[A-Za-z\s'.-]+$/.test(value), {
        message:
          'Name can only contain letters, spaces, and specific special characters: -\'."',
      })
      .optional(),

    contactNumber: z
      .string({
        errorMap: () => ({ message: 'Contact Number is a Required Field' }),
      })
      .trim()
      .min(3, 'Contact Number must be at least 3 characters')
      .max(50, 'Contact Number must be at most 50 characters')
      .refine(
        (value) => value === '' || /^[A-Za-z0-9\-\/()[\]@,]+$$/.test(value),
        {
          message: `Contact can only contain aplhanumeric and specific special characters such as: -,/()[]@`,
        },
      )
      .optional(),

    status: z.string().trim().optional(),

    lineOfBusiness: z
      .string({
        errorMap: () => ({ message: 'Line of Business is a Required Field' }),
      })
      .trim()
      .max(50, 'Line of business must be at most 50 characters')
      .optional(),
  })
  .superRefine((data, ctx) => {
    const { status, ...supplierData } = data;

    if (status) return z.NEVER;

    const errorMessages = {
      contactNumber: 'Contact Number',
      contactPerson: 'Contact Person',
      lineOfBusiness: 'Line of business',
    };

    const supplierFields = Object.entries(supplierData);
    supplierFields?.map(([key, value]) => {
      if (!value) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: `${errorMessages[key]} is a Required Field`,
          path: [key],
        });
      }
    });
  });

const destroySupplierByIdSchema = z.object({
  id: z
    .string()
    .min(1, 'Supplier id is required')
    .nonempty('Supplier id cannot be empty')
    .refine((val) => val !== ':id', {
      message: 'No supplier id provided',
    }),
});

const getAllSuppliersSchema = z
  .object({
    search: z.string().optional(),
    sort: z.enum(['asc', 'desc']).optional(),
    sortBy: sortSchema(sort.SUPPLIER_SORT_COLUMNS).optional(),
    page: z.string().regex(/^\d+$/).transform(Number).optional(),
    filterBy: z.string().optional(),
    limit: z.string().regex(/^\d+$/).transform(Number).optional(),
  })
  .strict();

const createSupplierCommentSchema = z.object({
  comment: z
    .string({
      required_error: 'Comment is required',
      invalid_type_error: 'Comment must be a string',
    })
    .trim()
    .min(1, 'Comment cannot be empty'),
});

const isoDateRegex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/;
const getSupplierCommentsSchema = z.object({
  commentDateFrom: z
    .string()
    .optional()
    .refine((date) => !date || isoDateRegex.test(date), {
      message:
        'commentDateFrom must be a valid ISO 8601 date format (e.g., 2024-11-03T05:37:44.228Z)',
    }),
  commentDateTo: z
    .string()
    .optional()
    .refine((date) => !date || isoDateRegex.test(date), {
      message:
        'commentDateTo must be a valid ISO 8601 date format (e.g., 2024-11-03T05:37:44.228Z)',
    }),
});
const getSupplierAttachmentsSchema = z.object({
  attachmentDateFrom: z
    .string()
    .optional()
    .refine((date) => !date || isoDateRegex.test(date), {
      message:
        'attachmentDateFrom must be a valid ISO 8601 date format (e.g., 2024-11-03T05:37:44.228Z)',
    }),
  attachmentDateTo: z
    .string()
    .optional()
    .refine((date) => !date || isoDateRegex.test(date), {
      message:
        'attachmentDateTo must be a valid ISO 8601 date format (e.g., 2024-11-03T05:37:44.228Z)',
    }),
});

const updateSupplierAttachments = z
  .array(
    z
      .object({
        id: z.number(),
        path: z.string(),
        fileName: z.string().optional(),
        model: z.string().optional(),
        modelId: z.number().optional(),
        userAttachment: z
          .object({
            id: z.number(),
            firstName: z.object({
              firstName: z
                .string(stringFieldError('First name'))
                .trim()
                .max(100, 'First name must be at most 100 characters')
                .regex(
                  /^[A-Za-z'.-]+$/,
                  'First name can only contain letters and specific special characters: -\'."',
                ),

              lastName: z
                .string(stringFieldError('Last name'))
                .trim()
                .max(100, 'Last name must be at most 100 characters')
                .regex(
                  /^[A-Za-z'.-]+$/,
                  'Last name can only contain letters and specific special characters: -\'."',
                ),
            }),
            createdAt: z
              .string()
              .optional()
              .refine((date) => !date || isoDateRegex.test(date), {
                message:
                  'commentDateFrom must be a valid ISO 8601 date format (e.g., 2024-11-03T05:37:44.228Z)',
              }),
            updatedAt: z
              .string()
              .optional()
              .refine((date) => !date || isoDateRegex.test(date), {
                message:
                  'commentDateFrom must be a valid ISO 8601 date format (e.g., 2024-11-03T05:37:44.228Z)',
              }),
          })
          .optional(),
      })
      .strict(),
  )
  .optional();

const deleteSupplierAttachment = z.object({
  id: z.number(),
  path: z.string(),
});

const markCommentOrAttachmentAsSeenSchema = z.object({
  model: z.enum(['comment', 'attachment']),
  supplierId: z.number(),
});

const supplierSortSchema = sortSchema(sort.SUPPLIER_SORT_COLUMNS);
const supplierFilterSchema = filterSchema(filter.SUPPLIER_FILTER_COLUMNS);

module.exports = {
  getSupplierByIdSchema,
  updateSupplierSchema,
  destroySupplierByIdSchema,
  getAllSuppliersSchema,
  createSupplierCommentSchema,
  getSupplierCommentsSchema,
  getSupplierAttachmentsSchema,
  supplierSortSchema,
  markCommentOrAttachmentAsSeenSchema,
  updateSupplierAttachments,
  deleteSupplierAttachment,
  supplierFilterSchema,
};
