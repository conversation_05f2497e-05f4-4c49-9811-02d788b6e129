const { z } = require('zod');
const {
  createIdParamsSchema,
  createNumberSchema,
  decimalFormat,
} = require('../../app/utils');
const { sortSchema } = require('./sortEntity');
const { sort, note } = require('../constants');

const createUpdateInvoiceReportSchema = z
  .object({
    requisitionId: createIdParamsSchema('Requisition ID'),
    purchaseOrderId: createIdParamsSchema('Purchase Order ID'),
    isDraft: z.string().refine((val) => val === 'true' || val === 'false', {
      message: 'Draft status must be a string that is either "true" or "false"',
    }),
    supplierInvoice: z
      .object({
        number: z.string().max(100).regex(note.REGEX, {
          message: 'Supplier invoice number contains invalid characters.',
        }),
        invoiceDate: z
          .string()
          .date('Date Format: YYYY-MM-DD')
          .refine((data) => new Date(data) <= new Date(), {
            message: 'Supplier invoice date must be current or past date.',
            path: ['invoiceDate'],
          }),
        amount: decimalFormat('Supplier invoice amount'),
      })
      .strict(),
    attachmentIds: z.array(createNumberSchema('Attachment ID')).min(1, {
      message: 'At least one attachment ID is required.',
    }),
    note: z
      .string()
      .max(100)
      .regex(note.REGEX, 'Note contains invalid characters.')
      .nullable(),
    deliveryReceiptIds: z.array(z.number().int()).min(1, {
      message: 'At least one delivery receipt ID is required.',
    }),
  })
  .strict();

const filterDeliveryReportsSchema = z
  .object({
    search: z.string().optional(),
    sortBy: sortSchema(sort.INVOICE_REPORT_DR_ITEMS_COLUMNS).optional(),
    page: z.string().regex(/^\d+$/).transform(Number).optional(),
    paginate: z.union([z.boolean(), z.enum(['true', 'false'])]).optional(),
    limit: z.string().regex(/^\d+$/).transform(Number).optional(),
  })
  .strict();

const filterDeliveryReportItemsSchema = z
  .object({
    search: z.string().optional(),
    sortBy: sortSchema(sort.INVOICE_REPORT_DR_ITEMS_COLUMNS).optional(),
    page: z.string().regex(/^\d+$/).transform(Number).optional(),
    limit: z.string().regex(/^\d+$/).transform(Number).optional(),
  })
  .strict();

const getInvoiceReportsFromRequisitionQuerySchema = z.object({
  search: z.string().optional(),
  sortBy: sortSchema(sort.DELIVERY_RECEIPT_SORT_COLUMNS).optional(),
  page: z.string().regex(/^\d+$/).transform(Number).optional(),
  limit: z.string().regex(/^\d+$/).transform(Number).optional(),
});

const getInvoiceReportsFromPurchaseOrderQuerySchema = z.object({
  paymentRequestId: createIdParamsSchema('Payment Request ID').optional(),
});

const getInvoiceReportsByPaymentRequestQuerySchema = z.object({
  sortBy: sortSchema(
    sort.PAYMENT_REQUEST_INVOICE_REPORT_SORT_COLUMNS,
  ).optional(),
  page: z.string().regex(/^\d+$/).transform(Number).optional(),
  limit: z.string().regex(/^\d+$/).transform(Number).optional(),
});

module.exports = {
  createUpdateInvoiceReportSchema,
  filterDeliveryReportsSchema,
  filterDeliveryReportItemsSchema,
  getInvoiceReportsFromRequisitionQuerySchema,
  getInvoiceReportsFromPurchaseOrderQuerySchema,
  getInvoiceReportsByPaymentRequestQuerySchema,
};
