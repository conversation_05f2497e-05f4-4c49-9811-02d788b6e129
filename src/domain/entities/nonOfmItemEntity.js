const { z } = require('zod');
const { sort, filter, note } = require('../constants');
const { sortSchema } = require('./sortEntity');
const { filterSchema } = require('./filterEntity');

const nonOfmItemSchema = z.object({
  id: z.number().optional(),
  itemName: z
    .string()
    .max(100)
    .regex(note.REGEX, 'Non-ofm Item Name contains invalid characters.'),
  itemType: z.string().max(20),
  unit: z.string().max(20),
  acctCd: z.string().max(20).nullable().optional(),
  notes: z
    .string('Non-ofm Item note is Require')
    .max(100)
    .regex(note.REGEX, 'Non-ofm item note contains invalid characters.'),
});

const getNonOfmItemsSchema = z.object({
  search: z.string().optional(),
  sort: z.enum(['asc', 'desc']).optional(),
  page: z.string().regex(/^\d+$/).transform(Number).optional(),
  limit: z.string().regex(/^\d+$/).transform(Number).optional(),
});

const createNonOfmItemSchema = nonOfmItemSchema.omit({ id: true });

const updateNonOfmItemSchema = nonOfmItemSchema.partial().omit({ id: true });

const nonOfmItemSortSchema = sortSchema(sort.NONOFM_ITEM_SORT_COLUMNS);
const nonOfmItemFilterSchema = filterSchema(filter.NON_OFM_ITEM_FILTER_COLUMNS);

const filterNonOfmItemSchema = z.object({
  itemName: z.string().optional(),
  itemType: z.string().optional(),
  unit: z.string().optional(),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
});

module.exports = {
  nonOfmItemSchema,
  getNonOfmItemsSchema,
  createNonOfmItemSchema,
  updateNonOfmItemSchema,
  nonOfmItemSortSchema,
  filterNonOfmItemSchema,
  nonOfmItemFilterSchema,
};
