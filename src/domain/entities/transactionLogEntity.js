const { z } = require('zod');
const { log } = require('../constants');
const { createNumberSchema, stringFieldError } = require('../../app/utils');
const { PRS_JOURNEY } = log;

const transactionLogRequestSchema = z
  .object({
    userId: createNumberSchema('User ID'),
    rsId: createNumberSchema('Requisition ID'),
    level: z.enum(Object.values(PRS_JOURNEY)),
    message: z
      .string(stringFieldError('Transaction message'))
      .max(255, 'Transaction message must not exceed 255 characters'),
    metadata: z.object({}).passthrough().optional(),
  })
  .strict();

const transactionLogResponseSchema = transactionLogRequestSchema.extend({
  time: z.date(),
});

module.exports = {
  transactionLogRequestSchema,
  transactionLogResponseSchema,
};
