const {
  createNumberSchema,
  stringFieldError,
} = require('../../app/utils/index');
const { z } = require('zod');
const { APPROVAL_TYPES } = require('../constants/approvalConstants');
const { ASSOCIATION_AREAS } = require('../constants/areaConstants');

const validApprovalCodes = Object.values(APPROVAL_TYPES).map(
  (type) => type.code,
);

const validAreaCodes = Object.values(ASSOCIATION_AREAS).map(
  (area) => area.code,
);

const validAreaCodesNames = Object.values(ASSOCIATION_AREAS).map(
  (area) => area.name,
);

const level1ApproverSchema = z
  .object({
    level: z.literal(1, {
      message: 'Level 1 requires area code',
    }),
    approverId: createNumberSchema('Approver'),
    areaCode: z
      .string(stringFieldError('Area code'))
      .refine((code) => validAreaCodes.includes(code), {
        message: `Area code must be one of: ${validAreaCodesNames.join(', ')}`,
      }),
  })
  .strict();

const higherLevelApproverSchema = z
  .object({
    level: createNumberSchema('Approver level').min(2, {
      message: 'Level 1 must have one approver for each area code',
    }),
    approverId: createNumberSchema('Approver'),
  })
  .strict();

const approverSchema = z
  .object({
    level: createNumberSchema('Approver level'),
    approverId: createNumberSchema('Approver'),
  })
  .strict();

const optionalApproverSchema = z
  .object({
    approverId: createNumberSchema('Optional approver'),
  })
  .strict();

const setupApprovalSchema = z
  .object({
    approvalTypeCode: z
      .string(stringFieldError('Approval type code'))
      .refine((code) => validApprovalCodes.includes(code), {
        message: `Approval type code must be one of: ${validAreaCodesNames.join(', ')}`,
      }),
    approvers: z
      .array(approverSchema, {
        invalid_type_error: 'Invalid list of approvers',
        required_error: 'Approvers are required',
      })
      .refine(
        (approvers) => {
          const sortedLevels = approvers
            .map((a) => a.level)
            .sort((a, b) => a - b);
          return sortedLevels.every((level, index) => level === index + 1);
        },
        { message: 'Levels must be sequential starting from 1' },
      )
      .refine(
        (approvers) => {
          const approverIds = approvers.map((a) => a.approverId);
          return approverIds.length === new Set(approverIds).size;
        },
        { message: 'An approver cannot be assigned to multiple levels' },
      ),
    optionalApprovers: z
      .array(optionalApproverSchema)
      .max(1, { message: 'Only one optional approver is allowed' })
      .optional()
      .transform((val) => val || []),
  })
  .strict()
  .refine(
    (schema) => {
      const approverIds = new Set(schema.approvers.map((a) => a.approverId));
      const optionalIds = schema.optionalApprovers.map((a) => a.approverId);

      return optionalIds.every((id) => !approverIds.has(id));
    },
    { message: 'Optional approver cannot be an existing approver' },
  );

const setupAssociationApprovalSchema = z
  .object({
    approvalTypeCode: z
      .string(stringFieldError('Approval type code'))
      .refine((code) => validApprovalCodes.includes(code), {
        message: `Approval type code must be one of: ${validAreaCodesNames.join(', ')}`,
      }),
    approvers: z
      .array(
        z.union([level1ApproverSchema, higherLevelApproverSchema], {
          errorMap: (issue, ctx) => {
            const error = issue?.unionErrors?.[1]?.issues?.map((error) => {
              return error?.message;
            });

            if (error.length > 0) {
              return { message: error[0] ?? ctx.defaultError };
            }

            return { message: ctx.defaultError };
          },
        }),
      )
      .refine(
        (approvers) => {
          const level1Approvers = approvers.filter((a) => a.level === 1);

          if (level1Approvers.length > 0) {
            const areaCodes = level1Approvers.map((a) => a.areaCode);
            const uniqueAreaCodes = new Set(areaCodes);

            return (
              uniqueAreaCodes.size === validAreaCodes.length &&
              areaCodes.every((code) => validAreaCodes.includes(code))
            );
          }

          return true;
        },
        {
          message: `Level 1 must have one approver for each area code: (${validAreaCodesNames.join(
            ', ',
          )})`,
        },
      )
      .refine(
        (approvers) => {
          const sortedLevels = [
            ...new Set(approvers.map((a) => a.level)),
          ].sort();

          return sortedLevels.every((level, index) => level === index + 1);
        },
        { message: 'Levels must be sequential starting from 1' },
      )
      .refine(
        (approvers) => {
          const approverIds = approvers.map((a) => a.approverId);
          return approverIds.length === new Set(approverIds).size;
        },
        {
          message: 'An approver cannot be assigned to multiple levels or areas',
        },
      ),
  })
  .strict();

module.exports = {
  setupApprovalSchema,
  setupAssociationApprovalSchema,
};
