const { z } = require('zod');
const { attachment } = require('../constants');
const { createIdParamsSchema, createNumberSchema } = require('../../app/utils');

const createAttachmentSchema = z
  .object({
    model: z.enum(Object.values(attachment.MODELS)),
    modelId: z
      .string()
      .refine((val) => Number.isInteger(Number(val)) && Number(val) > 0, {
        message: 'Model ID is invalid.',
      })
      .optional(),
  })
  .strict();

const assignModelIdToAttachmentsSchema = z
  .object({
    attachmentIds: z.array(createNumberSchema('Attachment ID')),
    model: z.enum(Object.values(attachment.MODELS)),
    modelId: createNumberSchema('Attachment Model ID'),
  })
  .strict();

const getAttachmentsParamsSchema = z.object({
  model: z.enum(Object.values(attachment.MODELS)),
  modelId: createIdParamsSchema('Attachment Model ID'),
});

const getAttachmentsQuerySchema = z.object({
  search: z.string().optional(),
});

const deleteAttachmentsSchema = z.object({
  attachmentIds: z.array(createNumberSchema('Attachment ID')),
  model: z.enum(Object.values(attachment.MODELS)),
  modelId: createNumberSchema('Attachment Model ID'),
});

const deleteAttachmentParams = z.object({
  attachmentId: createIdParamsSchema('Attachment ID'),
});

module.exports = {
  createAttachmentSchema,
  assignModelIdToAttachmentsSchema,
  deleteAttachmentsSchema,
  deleteAttachmentParams,
  getAttachmentsParamsSchema,
  getAttachmentsQuerySchema,
};
