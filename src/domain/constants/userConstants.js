// ! IMPORTANT: This is used for seeding roles and permissions.
// ! Do not change or it may break the seeding process.
const USER_TYPES = Object.freeze({
  ROOT_USER: 'Root User',
  ADMIN: 'Admin',
  ENGINEERS: 'Engineers',
  SUPERVISOR: 'Supervisor',
  ASSISTANT_MANAGER: 'Assistant Manager',
  DEPARTMENT_HEAD: 'Department Head',
  DEPARTMENT_DIVISION_HEAD: 'Division Head',
  AREA_STAFF: 'Area Staff/Department Secretary',
  PURCHASING_STAFF: 'Purchasing Staff',
  PURCHASING_HEAD: 'Purchasing Head',
  MANAGEMENT: 'Management',
  PURCHASING_ADMIN: 'Purchasing Admin',
});

const APPROVERS = Object.freeze({
  SUPERVISOR: USER_TYPES.SUPERVISOR,
  ASSISTANT_MANAGER: USER_TYPES.ASSISTANT_MANAGER,
  DEPARTMENT_HEAD: USER_TYPES.DEPARTMENT_HEAD,
  DEPARTMENT_DIVISION_HEAD: USER_TYPES.DEPARTMENT_DIVISION_HEAD,
  AREA_STAFF: USER_TYPES.AREA_STAFF,
});

const USER_STATUS = Object.freeze({
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  ON_LEAVE: 'on-leave',
});

const USER_TYPE_IDS = Object.freeze({
  ADMIN: process.env.ADMIN_ROLE_ID || 2,
  PURCHASING_HEAD: process.env.PURCHASING_HEAD_ROLE_ID || 11,
});

module.exports = {
  USER_TYPES,
  USER_STATUS,
  APPROVERS,
  USER_TYPE_IDS,
};
