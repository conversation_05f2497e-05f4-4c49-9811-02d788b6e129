const { NOTIFICATION_TYPES } = require('./notificationConstants');

const APPROVER = Object.freeze({
  MODEL_APPROVER: {
    requisition: 'requisitionApproverRepository',
    canvass: 'canvassApproverRepository',
    purchaseOrder: 'purchaseOrderApproverRepository',
    nonRequisition: 'nonRequisitionApproverRepository',
    rsPaymentRequest: 'rsPaymentRequestApproverRepository',
  },
  WHERE_MODEL_ID_APPROVER: {
    requisition: (modelId) => ({ requisitionId: modelId }),
    canvass: (modelId) => ({ canvassRequisitionId: modelId }),
    purchaseOrder: (modelId) => ({ purchaseOrderId: modelId }),
    nonRequisition: (modelId) => ({ nonRequisitionId: modelId }),
    rsPaymentRequest: (modelId) => ({ paymentRequestId: modelId }),
  },
  NOTIFICATION_FEATURE: {
    requisition: `Requisition Slip`,
    canvass: 'Canvass Sheet',
    purchaseOrder: 'Purchase Order',
    nonRequisition: 'Non-RS Payment Request',
    rsPaymentRequest: 'Payment Request',
  },
  NOTIFICATION_TYPES: {
    requisition: NOTIFICATION_TYPES.REQUISITION_SLIP,
    canvass: NOTIFICATION_TYPES.CANVASS,
    purchaseOrder: NOTIFICATION_TYPES.PURCHASE_ORDER,
    nonRequisition: NOTIFICATION_TYPES.NON_RS,
    rsPaymentRequest: NOTIFICATION_TYPES.PAYMENT_REQUEST,
  },
  MODEL: {
    requisition: 'requisitionRepository',
    canvass: 'canvassRequisitionRepository',
    purchaseOrder: 'purchaseOrderRepository',
    nonRequisition: 'nonRequisitionRepository',
    rsPaymentRequest: 'rsPaymentRequestRepository',
  },
  FEATURE_CODE: {
    requisition: 'RS',
    canvass: 'CS',
    purchaseOrder: 'PO',
    rsPaymentRequest: 'PR',
  },
});

module.exports = {
  APPROVER,
};
