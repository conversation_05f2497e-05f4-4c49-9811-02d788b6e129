// TODO: Update the area codes and names - once list is finalized
const ASSOCIATION_AREAS = {
  MET14: {
    id: 1,
    code: 'MET14',
    name: 'MET 1-4',
  },
  TMR_TGR_TGH_OTR: {
    id: 2,
    code: 'TMR_TGR_TGH_OTR',
    name: 'TMR / TGR / TGH / OTR',
  },
  GET_CER_BMCAI: {
    id: 3,
    code: 'GET_CER_BMCAI',
    name: 'GET / CER / BMCAI',
  },
  CDC_OFC_MLA_ORTIGAS_VCB_SALES_OFFICE: {
    id: 4,
    code: 'CDC_OFC_MLA_ORTIGAS_VCB_SALES_OFFICE',
    name: 'CDC OFC / MLA / ORTIGAS / VCB / SALES OFFICE',
  },
  CC10_RADA: {
    id: 5,
    code: 'CC10_RADA',
    name: 'CC10 / RADA',
  },
  PPT12_101X_GCR_CL1_CL3: {
    id: 6,
    code: 'PPT12_101X_GCR_CL1_CL3',
    name: 'PPT1&2 / 101X / GCR / CL1 / CL3',
  },
  ONP_PH1_TNP: {
    id: 7,
    code: 'ONP_PH1_TNP',
    name: 'ONP / PH1 / TNP',
  },
  MEM2_NRT: {
    id: 8,
    code: 'MEM2_NRT',
    name: 'MEM2 / NRT',
  },
};

module.exports = {
  ASSOCIATION_AREAS,
};
