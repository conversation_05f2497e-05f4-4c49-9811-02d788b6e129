const { CANVASS_STATUS } = require('./canvassConstants');
const { STATUSES } = require('./deliveryReceiptConstants');
const { PO_STATUS } = require('./purchaseOrderConstants');
const { RS_PAYMENT_REQUEST_STATUS } = require('./rsPaymentRequestConstants');

const SUPPLIER_SYNC_ACCEPTED_STATUSES = [
  'partially_canvassed',
  'canvass_approval',
  'po_approval',
  'delivery',
  'payment_request',
  'pr_approval',
  'rs_in_progress',
  'rs_draft',
];

const COMPANY_SYNC_ACCEPTED_STATUSES = [
  // // scenario #2: Open RS with a Status of Draft and For Approval
  // // 'draft',
  // // 'for_rs_approval', // ?? may ganito bang status sa RS?
  // 'rs_in_progress',
  // // scenario #3: Open RS with a Status of Canvassing or Partially Canvassed
  // 'partially_canvassed',
  // 'canvass_approval',
  // // scenario #4: Open RS with a Status of PO Created and PO Approval
  // 'for_po_approval',
  // 'for_po_review',
  // // scenario #5: Open RS with a Status of For Delivery
  // 'delivery',
  // // scenario #6: Open RS with a Status of For Payment Request and PR Approval
  // 'payment_request',
  // 'pr_approval',

  //CANVASS
  CANVASS_STATUS.DRAFT,
  CANVASS_STATUS.PARTIAL,
  CANVASS_STATUS.FOR_APPROVAL,

  //PO
  PO_STATUS.FOR_PO_APPROVAL,
  PO_STATUS.FOR_DELIVERY,
  PO_STATUS.FOR_SENDING,

  //RR
  STATUSES.DRAFT,
  STATUSES.DELIVERED,

  //PR
  RS_PAYMENT_REQUEST_STATUS.FOR_PR_APPROVAL,
  RS_PAYMENT_REQUEST_STATUS.DRAFT,
];

module.exports = {
  SUPPLIER_SYNC_ACCEPTED_STATUSES,
  COMPANY_SYNC_ACCEPTED_STATUSES,
};
