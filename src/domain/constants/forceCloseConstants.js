/**
 * Force Close Constants
 * Contains all constants related to force close functionality
 */

/**
 * Force Close Scenarios
 * Represents the 3 scenarios from requirements document for force closing requisitions
 */
const FORCE_CLOSE_SCENARIOS = Object.freeze({
  ACTIVE_PO_PARTIAL_DELIVERY: 'ACTIVE_PO_PARTIAL_DELIVERY', // Scenario 1: Active Purchase Orders with Partial Deliveries
  CLOSED_PO_WITH_REMAINING_CANVASS_QTY: 'CLOSED_PO_WITH_REMAINING_CANVASS_QTY', // Scenario 2: Closed POs with Remaining Canvass Quantities (exact match to requirements)
  CLOSED_PO_PENDING_CS: 'CLOSED_PO_PENDING_CS', // Scenario 3: Closed Purchase Orders with Pending Canvass Sheet Approvals
});

/**
 * Force Close Status Constants
 * Additional status values needed for force close functionality
 */
const FORCE_CLOSE_STATUS = Object.freeze({
  // Requisition status (already exists in requisitionConstants.js)
  RS_CLOSED: 'closed',

  // Canvass status for cancelled canvass sheets
  CS_CANCELLED: 'cs_cancelled',

  // Delivery Receipt status for cancelled delivery receipts
  RR_CANCELLED: 'rr_cancelled',

  // Invoice Report status for cancelled invoice reports
  IR_CANCELLED: 'ir_cancelled',

  // Payment Request status for cancelled payment requests
  PR_CANCELLED: 'pr_cancelled',
});

/**
 * Force Close Validation Error Messages
 * Standardized error messages for force close validation
 */
const FORCE_CLOSE_ERRORS = Object.freeze({
  // Authorization errors
  ACCESS_DENIED: 'Access Denied - User not authorized',
  NOT_REQUESTER_OR_ASSIGNEE:
    'Only the requester or assigned purchasing staff can force close this requisition',

  // Requisition status errors
  RS_NOT_APPROVED: 'Requisition must be fully approved before force closing',
  RS_NOT_IN_PROGRESS:
    'Requisition must be in progress to be eligible for force close',
  RS_STILL_CANCELLABLE:
    'No Force Close Button Yet - Still eligible for RS Cancellation',

  // Scenario validation errors
  NO_VALID_SCENARIO: 'No valid force close scenario detected',
  NO_DELIVERIES_YET:
    'Cannot force close - No deliveries have been made yet. Please manually cancel the Purchase Order first',
  NO_DELIVERIES_MANUAL_CANCEL:
    'Cannot force close - No deliveries have been made yet. Please manually cancel the Purchase Order first',
  INVALID_PO_STATUS:
    'Purchase Order status must be for_delivery, closed_po, or cancelled_po',
  UNPAID_DELIVERIES:
    'Cannot force close - All delivered items must be paid before force closing',
  AUTO_CLOSE_DETECTED:
    'Requisition should auto-close instead of force close - all conditions met for automatic closure',

  // Enhanced payment validation errors
  PAYMENT_AMOUNT_MISMATCH:
    'Payment amount does not match delivered amount (DR Qty × Unit Price ≠ PR Amount)',
  PAYMENT_REQUEST_NOT_FOUND:
    'No corresponding payment request found for delivery receipt',
  PAYMENT_REQUEST_NOT_APPROVED: 'Payment request exists but is not approved',
  PAYMENT_REQUEST_INVALID_STATUS:
    'Payment request has invalid status for force close validation',
  MULTIPLE_PAYMENT_REQUESTS:
    'Multiple payment requests found for single delivery receipt',
  PAYMENT_CALCULATION_ERROR: 'Error calculating payment amounts for validation',

  // Enhanced delivery validation errors
  DELIVERY_VALIDATION_ERROR: 'Error validating delivery status and quantities',
  INVALID_DELIVERY_STATUS:
    'Delivery receipt has invalid status for force close validation',
  NO_DELIVERY_ITEMS: 'Delivery receipt has no items',
  DELIVERY_QUANTITY_MISMATCH:
    'Delivered quantity exceeds purchase order quantity',
  DELIVERY_ITEM_NOT_IN_PO: 'Delivery receipt item not found in purchase order',
  DELIVERY_CALCULATION_ERROR:
    'Error calculating delivery quantities and completion rates',

  // Enhanced auto-close detection errors
  AUTO_CLOSE_DETECTION_ERROR: 'Error during auto-close detection analysis',
  AUTO_CLOSE_CRITERIA_FAILED: 'Auto-close criteria validation failed',
  AUTO_CLOSE_CONFIDENCE_LOW: 'Auto-close confidence below threshold',
  REMAINING_QUANTITIES_ERROR:
    'Error validating remaining quantities for canvassing',
  PENDING_APPROVALS_ERROR: 'Error validating pending canvass sheet approvals',
  DRAFT_DOCUMENTS_ERROR: 'Error validating draft documents status',

  // Enhanced system and execution errors
  EXECUTION_FAILED: 'Force close execution failed',
  UNKNOWN_SCENARIO: 'Unknown or invalid force close scenario',
  TRANSACTION_FAILED: 'Database transaction failed during force close',
  REPOSITORY_ERROR: 'Repository operation failed',
  VALIDATION_SYSTEM_ERROR: 'Validation system encountered an error',

  // Notes validation errors
  NOTES_REQUIRED: 'Force close notes are required',
  NOTES_TOO_LONG: 'Force close notes must not exceed 500 characters',
  NOTES_INVALID_CHARS:
    'Force close notes can only contain alphanumeric characters and specific special characters',
  NOTES_NO_EMOJIS: 'Force close notes cannot contain emojis',

  // General errors
  REQUISITION_NOT_FOUND: 'Requisition not found',
});

/**
 * Force Close Action Types
 * Types of actions performed during force close execution
 */
const FORCE_CLOSE_ACTIONS = Object.freeze({
  // Common actions
  UPDATE_RS_STATUS: 'Updated RS status to CLOSED',
  ADD_NOTES: 'Added force close notes to RS comments',
  LOG_ACTIVITY: 'Logged force close activity',

  // Scenario 1: Active PO with Partial Delivery actions
  UPDATE_PO_AMOUNTS: 'Update PO amounts to reflect delivered quantities only',
  UPDATE_PO_QUANTITIES: 'Update PO quantities to reflect delivered quantities',
  GENERATE_PO_NOTES: 'Generate PO notes for modifications',
  RETURN_GFQ: 'Return unfulfilled quantities to GFQ (OFM/OFM-TOM only)',
  CLOSE_PO: 'Close For Delivery PO',

  // Scenario 2: Closed PO with Remaining Quantities actions
  ZERO_REMAINING_QTY: 'Zero out remaining quantities to be canvassed',
  RETURN_REMAINING_GFQ:
    'Return unfulfilled quantities to GFQ (OFM/OFM-TOM only)',

  // Scenario 3: Closed PO with Pending CS actions
  CANCEL_PENDING_CS: 'Cancel pending canvass sheet approvals',
  ZERO_REMAINING_QTY_CS: 'Zero out remaining quantities from cancelled CS',
  RETURN_CS_GFQ:
    'Return quantities from cancelled CS to GFQ (OFM/OFM-TOM only)',
});

/**
 * Force Close Validation Steps
 * Steps in the force close validation flowchart
 */
const FORCE_CLOSE_VALIDATION_STEPS = Object.freeze({
  CHECK_USER: 'CheckUser',
  CHECK_RS: 'CheckRS',
  CHECK_PO: 'CheckPO',
  CHECK_REM_QTY: 'CheckRemQty',
  CHECK_CS: 'CheckCS',
});

/**
 * Force Close Button Visibility Rules
 * Conditions for when the Force Close button should be visible
 * Implements acceptance criteria: Button shows when RS Status is "RS In Progress" AND PO Status is "For Delivery"
 */
const FORCE_CLOSE_BUTTON_RULES = Object.freeze({
  // Button replaces Cancel button when RS status is "RS In Progress"
  SHOW_WHEN_IN_PROGRESS: 'rs_in_progress',

  // Required PO status for button visibility
  REQUIRED_PO_STATUS: 'for_delivery',

  // Button visibility conditions
  REQUIRES_AUTHORIZATION: true,
  REQUIRES_APPROVED_STATUS: true,
  REQUIRES_VALID_SCENARIO: true,
  REQUIRES_ACTIVE_PO_FOR_DELIVERY: true, // New requirement from acceptance criteria
});

/**
 * Force Close Item Types
 * Types of items that can have GFQ quantities returned
 */
const FORCE_CLOSE_ITEM_TYPES = Object.freeze({
  OFM: 'ofm',
  OFM_TOM: 'ofm-tom',
  NON_OFM: 'non-ofm',
  NON_OFM_TOM: 'non-ofm-tom',
});

/**
 * Force Close GFQ Return Rules
 * Rules for returning quantities to GFQ during force close
 */
const FORCE_CLOSE_GFQ_RULES = Object.freeze({
  // Only OFM and OFM-TOM items can have quantities returned to GFQ
  ELIGIBLE_TYPES: [FORCE_CLOSE_ITEM_TYPES.OFM, FORCE_CLOSE_ITEM_TYPES.OFM_TOM],

  // Non-OFM items do not return quantities to GFQ
  NON_ELIGIBLE_TYPES: [
    FORCE_CLOSE_ITEM_TYPES.NON_OFM,
    FORCE_CLOSE_ITEM_TYPES.NON_OFM_TOM,
  ],
});

/**
 * Force Close Document Types
 * Types of documents that can be cancelled during force close
 */
const FORCE_CLOSE_DOCUMENT_TYPES = Object.freeze({
  CANVASS_SHEET: 'canvass_sheet',
  DELIVERY_RECEIPT: 'delivery_receipt',
  INVOICE_REPORT: 'invoice_report',
  PAYMENT_REQUEST: 'payment_request',
});

/**
 * Force Close Notification Types
 * Types of notifications sent during force close process
 */
const FORCE_CLOSE_NOTIFICATIONS = Object.freeze({
  APPROVER_CANCELLATION: 'approver_cancellation',
  FORCE_CLOSE_COMPLETED: 'force_close_completed',
  FORCE_CLOSE_FAILED: 'force_close_failed',
});

/**
 * Force Close History Actions
 * Action types for history/audit logging
 */
const FORCE_CLOSE_HISTORY_ACTIONS = Object.freeze({
  FORCE_CLOSED: 'FORCE_CLOSED',
  CANVASS_CANCELLED: 'CANVASS_CANCELLED',
  DOCUMENT_CANCELLED: 'DOCUMENT_CANCELLED',
  GFQ_RETURNED: 'GFQ_RETURNED',
  PO_MODIFIED: 'PO_MODIFIED',
});

/**
 * Force Close Enhanced Error Handling Rules
 * Enhanced error handling categories and recovery strategies
 */
const FORCE_CLOSE_ERROR_HANDLING = Object.freeze({
  // Error severity levels
  SEVERITY_LEVELS: {
    LOW: 'low',
    MEDIUM: 'medium',
    HIGH: 'high',
    CRITICAL: 'critical',
  },

  // Error categories for enhanced handling
  ERROR_CATEGORIES: {
    AUTHORIZATION: 'authorization',
    VALIDATION: 'validation',
    BUSINESS_LOGIC: 'business_logic',
    SYSTEM: 'system',
    NETWORK: 'network',
    DATABASE: 'database',
    CONFIGURATION: 'configuration',
  },

  // Recovery strategies
  RECOVERY_STRATEGIES: {
    RETRY: 'retry',
    FALLBACK: 'fallback',
    MANUAL_INTERVENTION: 'manual_intervention',
    SYSTEM_ADMIN: 'system_admin',
    USER_ACTION: 'user_action',
    NO_RECOVERY: 'no_recovery',
  },

  // Error context types
  CONTEXT_TYPES: {
    VALIDATION_STEP: 'validation_step',
    EXECUTION_PHASE: 'execution_phase',
    DATA_OPERATION: 'data_operation',
    BUSINESS_RULE: 'business_rule',
    SYSTEM_OPERATION: 'system_operation',
  },
});

/**
 * Force Close Auto-Close Detection Rules
 * Rules for detecting when a requisition should auto-close instead of force close
 */
const FORCE_CLOSE_AUTO_CLOSE_DETECTION = Object.freeze({
  // Auto-close criteria
  CRITERIA: {
    ALL_POS_CLOSED: 'all_pos_closed_or_cancelled',
    NO_REMAINING_QUANTITIES: 'no_remaining_quantities_for_canvassing',
    NO_PENDING_APPROVALS: 'no_pending_canvass_sheet_approvals',
    NO_DRAFT_DOCUMENTS: 'no_draft_documents_pending',
  },

  // Auto-close validation results
  VALIDATION_RESULTS: {
    SHOULD_AUTO_CLOSE: 'should_auto_close',
    REQUIRES_FORCE_CLOSE: 'requires_force_close',
    INSUFFICIENT_DATA: 'insufficient_data',
    VALIDATION_ERROR: 'validation_error',
  },

  // Auto-close detection categories
  DETECTION_CATEGORIES: {
    COMPLETE_FULFILLMENT: 'complete_fulfillment',
    CANCELLED_ORDERS: 'cancelled_orders',
    MIXED_COMPLETION: 'mixed_completion',
    PENDING_WORK: 'pending_work',
  },

  // Minimum confidence threshold for auto-close detection
  CONFIDENCE_THRESHOLD: 95, // Percentage
});

/**
 * Force Close Delivery Validation Rules
 * Enhanced rules for validating delivery status and quantities
 */
const FORCE_CLOSE_DELIVERY_VALIDATION = Object.freeze({
  // Valid delivery receipt statuses for force close validation
  VALID_DR_STATUSES: ['Delivered', 'APPROVED', 'DR_APPROVED', '', null],

  // Valid purchase order statuses for delivery validation
  VALID_PO_STATUSES: ['for_delivery', 'closed', 'closed_po', 'cancelled_po'],

  // Delivery validation categories
  VALIDATION_CATEGORIES: {
    STATUS_VALID: 'status_valid',
    QUANTITY_VALID: 'quantity_valid',
    ITEMS_VALID: 'items_valid',
    COMPLETION_VALID: 'completion_valid',
  },

  // Delivery completion thresholds
  COMPLETION_THRESHOLDS: {
    FULLY_DELIVERED: 100,
    PARTIALLY_DELIVERED: 0.01, // Any delivery > 0%
    NO_DELIVERY: 0,
  },

  // Delivery validation results
  VALIDATION_RESULTS: {
    FULLY_DELIVERED: 'fully_delivered',
    PARTIALLY_DELIVERED: 'partially_delivered',
    NO_DELIVERIES: 'no_deliveries',
    OVER_DELIVERED: 'over_delivered',
    INVALID: 'invalid',
  },
});

/**
 * Force Close Payment Validation Rules
 * Enhanced rules for validating payment prerequisites with multiple DR/PR support
 */
const FORCE_CLOSE_PAYMENT_VALIDATION = Object.freeze({
  // Payment amount tolerance for rounding differences
  AMOUNT_TOLERANCE: 0.01,

  // Required payment request statuses
  VALID_PR_STATUSES: ['Closed', 'APPROVED'],

  // Required delivery receipt statuses
  VALID_DR_STATUSES: ['DR_APPROVED', 'APPROVED', 'Delivered', '', null],

  // Payment validation categories
  VALIDATION_CATEGORIES: {
    AMOUNT_MATCH: 'amount_match',
    STATUS_VALID: 'status_valid',
    REQUEST_EXISTS: 'request_exists',
    CALCULATION_VALID: 'calculation_valid',
    MULTIPLE_DR_PR: 'multiple_dr_pr_validation',
  },

  // Payment validation results
  VALIDATION_RESULTS: {
    FULLY_PAID: 'fully_paid',
    PARTIALLY_PAID: 'partially_paid',
    UNPAID: 'unpaid',
    OVERPAID: 'overpaid',
    INVALID: 'invalid',
    PARTIAL_PAYMENT: 'partial_payment',
  },

  // DR amount calculation methods (priority order)
  DR_CALCULATION_METHODS: {
    INVOICE: 'invoice', // Use delivery receipt invoices (most accurate)
    ITEMS_CALCULATION: 'items_calculation', // Calculate from DR items × unit price
    PO_ESTIMATION: 'po_estimation', // Estimate from PO items (fallback)
  },

  // Discount types supported in unit price calculation
  DISCOUNT_TYPES: {
    NONE: 'none',
    PERCENTAGE: 'percentage',
    FIXED: 'fixed',
  },

  // Multiple DR/PR validation rules
  MULTIPLE_VALIDATION_RULES: {
    // Total approved PR amount must equal total DR amount
    TOTAL_AMOUNT_MATCH: true,

    // Each DR should have at least one corresponding approved PR
    REQUIRE_PR_FOR_EACH_DR: true,

    // Allow multiple PRs for a single DR (split payments)
    ALLOW_SPLIT_PAYMENTS: true,

    // Allow multiple DRs for a single PO
    ALLOW_MULTIPLE_DELIVERIES: true,
  },
});

/**
 * Force Close Notes Configuration
 * Configuration for force close notes validation
 */
const FORCE_CLOSE_NOTES_CONFIG = Object.freeze({
  MAX_LENGTH: 500,
  MIN_LENGTH: 1,
  REQUIRED: true,
  ALLOW_EMOJIS: false,
  ALLOWED_CHARS_REGEX: /^[\p{L}\p{N}\p{P}\p{Z}\p{Sm}\p{Sc}\p{Sk}\r\n]*$/u,
});

module.exports = {
  FORCE_CLOSE_SCENARIOS,
  FORCE_CLOSE_STATUS,
  FORCE_CLOSE_ERRORS,
  FORCE_CLOSE_ACTIONS,
  FORCE_CLOSE_VALIDATION_STEPS,
  FORCE_CLOSE_BUTTON_RULES,
  FORCE_CLOSE_ITEM_TYPES,
  FORCE_CLOSE_GFQ_RULES,
  FORCE_CLOSE_DOCUMENT_TYPES,
  FORCE_CLOSE_NOTIFICATIONS,
  FORCE_CLOSE_HISTORY_ACTIONS,
  FORCE_CLOSE_ERROR_HANDLING,
  FORCE_CLOSE_AUTO_CLOSE_DETECTION,
  FORCE_CLOSE_DELIVERY_VALIDATION,
  FORCE_CLOSE_PAYMENT_VALIDATION,
  FORCE_CLOSE_NOTES_CONFIG,
};
