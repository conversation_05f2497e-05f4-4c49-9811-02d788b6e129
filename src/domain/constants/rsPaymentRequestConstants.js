const RS_PAYMENT_REQUEST_STATUS = Object.freeze({
  DRAFT: 'PR Draft',
  REJECTED: 'PR Rejected',
  FOR_PR_APPROVAL: 'For PR Approval',
  SUBMITTED: 'submitted',
  APPROVED: 'Closed',
  PR_CANCELLED: 'pr_cancelled', // Added for force close functionality
});

const PR_APPROVER_STATUS = Object.freeze({
  PENDING: 'pending',
  REJECTED: 'rejected',
  APPROVED: 'approved',
  CLOSED: 'closed',
});

module.exports = {
  RS_PAYMENT_REQUEST_STATUS,
  PR_APPROVER_STATUS,
};
