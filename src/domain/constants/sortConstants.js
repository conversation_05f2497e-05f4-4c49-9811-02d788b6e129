const SORT_DIRECTIONS = [
  'ASC',
  'DESC',
  'asc',
  'desc',
  'asc nulls first',
  'desc nulls last',
  'asc nulls last',
  'desc nulls first',
];

const COMPANY_SORT_COLUMNS = [
  'name',
  'code',
  'tin',
  'initial',
  'address',
  'createdAt',
  'updatedAt',
  'contactNumber',
  'category',
];

const USER_SORT_COLUMNS = [
  'email',
  'status',
  'firstName',
  'lastName',
  'username',
  'userType',
  'department',
  'createdAt',
  'updatedAt',
];

const SUPPLIER_SORT_COLUMNS = [
  'name',
  'tin',
  'address',
  'citizenshipCode',
  'natureOfIncome',
  'iccode',
  'contactPerson',
  'contactNumber',
  'status',
  'createdAt',
  'updatedAt',
  'lineOfBusiness',
];

const PROJECT_SORT_COLUMNS = [
  'name',
  'code',
  'initial',
  'startDate',
  'endDate',
  'createdAt',
  'updatedAt',
  'address',
  'company',
];

const DEPARTMENT_SORT_COLUMNS = [
  'name',
  'code',
  'deptSupervisor',
  'companyId',
  'created_at',
  'updated_at',
];

const APPROVER_SORT_COLUMNS = [
  'model',
  'user_id',
  'alt_approver',
  'approval_order',
  'created_at',
  'updated_at',
];

const ITEM_SORT_COLUMNS = [
  'gfq',
  'unit',
  'trade',
  'itemCd',
  'itmDes',
  'isSteelbars',
  'acctCd',
  'createdAt',
  'updatedAt',
];

const AUDIT_LOG_SORT_COLUMNS = ['action_type', 'module', 'created_at'];

const NONOFM_ITEM_SORT_COLUMNS = [
  'itemName',
  'itemType',
  'unit',
  'companyCode',
  'departmentCode',
  'projectCode',
  'tradeCode',
  'createdAt',
  'updatedAt',
  'acctCd',
];

const OFM_ITEM_LIST_SORT_COLUMNS = ['listName', 'project', 'trade', 'company'];

const OFM_LIST_ITEM_SORT_COLUMNS = [
  'itmDes',
  'acctCd',
  'unit',
  'gfq',
  'ofmListId',
  'ofmItemId',
  'createdAt',
  'updatedAt',
];

const TRADE_SORT_COLUMNS = [
  'tradeName',
  'tradeCode',
  'created_at',
  'updated_at',
];

const REQUISITION_SORT_COLUMNS = [
  'ref_number',
  'doc_type',
  'requestor',
  'company',
  'project',
  'department',
  'createdAt',
  'updatedAt',
  'status',
];

const REQUISITION_ITEM_LIST_SORT_COLUMNS = [
  'itemName',
  'quantity',
  'notes',
  'createdAt',
  'updatedAt',
];

const CANVASS_REQUISITION_SORT_COLUMNS = [
  'requisitionId',
  'status',
  'notes',
  'isDraft',
  'createdAt',
  'updatedAt',
];

const CANVASS_SORT_COLUMNS = [
  'status',
  'lastApprover',
  'canvassNumber',
  'createdAt',
  'updatedAt',
];

const CANVASS_ITEM_SORT_COLUMNS = [
  'requisitionCanvassId',
  'supplierId',
  'itemId',
  'terms',
  'quantity',
  'unitPrice',
  'discount',
  'discountedUnitPrice',
  'createdAt',
  'updatedAt',
];

const DELIVERY_RECEIPT_SORT_COLUMNS = [
  'drNumber',
  'supplier',
  'latestDeliveryDate',
  'latestDeliveryStatus',
];

const PAYMENT_REQUEST_SORT_COLUMNS = [
  'prNumber',
  'lastUpdate',
  'lastApprover',
  'status',
];

const HISTORY_SORT_COLUMNS = [
  'rsNumber',
  'company',
  'project',
  'department',
  'dateRequested',
  'quantityRequested',
  'price',
  'dateDelivered',
  'quantityDelivered',
];
const LEAVE_SORT_COLUMNS = ['startDate', 'totalDays', 'endDate'];

const STEELBARS_SORT_COLUMNS = [
  'id',
  'grade',
  'diameter',
  'length',
  'weight',
  'pricePerKg',
  'kgPerMeter',
  'createdAt',
  'updatedAt',
];

const RS_HISTORY_SORT_COLUMNS = [
  // canvass
  'canvassNumber',
  'item',
  'supplier',
  'price',
  'discount',
  'canvassDate',
  'status',

  // order
  'poNumber',
  'poPrice',
  'dateOrdered',

  // delivery
  'drNumber',
  'quantityOrdered',
  'dateDelivered',
  'quantityDelivered',

  // payment
  'prNumber',
  'amount',

  // return
  'quantityReturned',
  'returnDate',

  // item
  'quantityRequested',

  //invoice
  'createdAt',
  'updatedAt',
  'status',
  'invoiceAmount',
  'issuedInvoiceDate',
  'supplierInvoiceNo',
  'irNumber',
];
const PR_ITEMS_COLUMNS = ['amount', 'itemName', 'accountCode', 'qtyDelivered'];

const PURCHASE_ORDER_SORT_COLUMNS = [
  'poNumber',
  'supplierName',
  'status',
  'createdAt',
  'updatedAt',
  'lastApproverName',
];

const PURCHASE_ORDER_LIST_SORT_COLUMNS = [
  'id',
  'itemId',
  'quantityPurchased',
  'quantityRequested',
  'originalPrice',
  'canvassedPrice',
  'accountCode',
  'unit',
  'itemName',
  'discountValue',
  'weight',
  'grade',
  'length',
  'diameter',
];

const NON_RS_SORT_COLUMNS = [
  'nonRsNumber',
  'chargeTo',
  'requestor',
  'totalDiscountedAmount',
  'status',
  'updatedAt',
];

const NON_RS_HISTORY_SORT_COLUMNS = ['approver', 'status', 'updatedAt'];

const INVOICE_REPORT_DR_COLUMNS = ['drNumber', 'dateDelivered', 'status'];

const DELIVERY_RECEIPT_ITEMS_COLUMNS = [
  'item',
  'requestedQty',
  'deliveredQty',
  'returnedQty',
  'unit',
];

const PURCHASE_HISTORY_SORT_COLUMNS = [
  'rsNumber',
  'supplierName',
  'pricePerUnit',
  'quantityPurchased',
  'datePurchased',
];

const PAYMENT_REQUEST_INVOICE_REPORT_SORT_COLUMNS = [
  'irNumber',
  'supplierInvoiceNo',
  'supplierInvoiceAmount',
];

module.exports = {
  PR_ITEMS_COLUMNS,
  SORT_DIRECTIONS,
  USER_SORT_COLUMNS,
  COMPANY_SORT_COLUMNS,
  SUPPLIER_SORT_COLUMNS,
  PROJECT_SORT_COLUMNS,
  DEPARTMENT_SORT_COLUMNS,
  APPROVER_SORT_COLUMNS,
  ITEM_SORT_COLUMNS,
  AUDIT_LOG_SORT_COLUMNS,
  NONOFM_ITEM_SORT_COLUMNS,
  OFM_ITEM_LIST_SORT_COLUMNS,
  OFM_LIST_ITEM_SORT_COLUMNS,
  TRADE_SORT_COLUMNS,
  REQUISITION_SORT_COLUMNS,
  REQUISITION_ITEM_LIST_SORT_COLUMNS,
  CANVASS_REQUISITION_SORT_COLUMNS,
  CANVASS_SORT_COLUMNS,
  CANVASS_ITEM_SORT_COLUMNS,
  DELIVERY_RECEIPT_SORT_COLUMNS,
  HISTORY_SORT_COLUMNS,
  LEAVE_SORT_COLUMNS,
  STEELBARS_SORT_COLUMNS,
  RS_HISTORY_SORT_COLUMNS,
  PURCHASE_ORDER_SORT_COLUMNS,
  PURCHASE_ORDER_LIST_SORT_COLUMNS,
  PAYMENT_REQUEST_SORT_COLUMNS,
  NON_RS_SORT_COLUMNS,
  NON_RS_HISTORY_SORT_COLUMNS,
  INVOICE_REPORT_DR_COLUMNS,
  DELIVERY_RECEIPT_ITEMS_COLUMNS,
  PURCHASE_HISTORY_SORT_COLUMNS,
  PAYMENT_REQUEST_INVOICE_REPORT_SORT_COLUMNS,
};
