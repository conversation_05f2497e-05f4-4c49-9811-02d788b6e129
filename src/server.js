class Server {
  constructor({ fastify }) {
    this.fastify = fastify;
  }

  // Run the server!
  async startFastify() {
    try {
      await this.fastify.listen({
        port: process.env.PORT || 4000,
        host: process.env.HOST || '0.0.0.0',
      });

      this.fastify.log.info(
        `Server listening on port ${process.env.PORT || 4000}`,
      );
      this.fastify.log.info(this.fastify.printRoutes({ includeHooks: true }));
    } catch (err) {
      this.fastify.log.error(`Error starting server: ${err}`);
      process.exit(1);
    }
  }
}

module.exports = Server;
