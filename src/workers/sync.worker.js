const { Worker } = require('bullmq');
const redisConfig = require('../infra/queue/redis');
const diContainer = require('../container');

const isProduction = process.env.NODE_ENV === 'production';

// Enhanced worker with production-specific error handling and monitoring
const worker = new Worker(
  'prs-data-sync',
  async (job) => {
    const startTime = Date.now();
    let scope;

    try {
      scope = diContainer.createScope();

      // Load services one by one to isolate the TimescaleDB alias issue
      console.log('Loading fastify...');
      const { fastify } = scope.cradle;

      console.log('Loading itemService...');
      const { itemService } = scope.cradle;

      console.log('Loading supplierSyncService...');
      const { supplierSyncService } = scope.cradle;

      const { type, userFromToken } = job.data;

      // Enhanced logging for production
      const logContext = {
        jobId: job.id,
        jobType: type,
        userId: userFromToken?.id,
        timestamp: new Date().toISOString(),
        attempt: job.attemptsMade + 1,
        maxAttempts: job.opts.attempts,
      };

      fastify.log.info('Worker started processing job', logContext);

      // Validate job data
      if (!type) {
        throw new Error('Job type is required');
      }

      if (!userFromToken) {
        throw new Error('User token is required for sync operations');
      }

      await job.updateProgress(10);

      let result;

      switch (type) {
        case 'supplier':
          fastify.log.info('Starting supplier synchronization', logContext);
          await job.updateProgress(25);

          result = await supplierSyncService.syncSuppliers({ userFromToken });

          await job.updateProgress(90);
          fastify.log.info('Supplier synchronization completed', {
            ...logContext,
            result,
          });
          await job.updateProgress(100);

          return {
            success: true,
            type: 'supplier',
            result,
            duration: Date.now() - startTime,
            timestamp: new Date().toISOString(),
          };

        case 'item':
          fastify.log.info('Starting item synchronization', logContext);
          await job.updateProgress(25);

          result = await itemService.syncItemsAtBackground(userFromToken);

          await job.updateProgress(90);
          fastify.log.info('Item synchronization completed', {
            ...logContext,
            result,
          });
          await job.updateProgress(100);

          return {
            success: true,
            type: 'item',
            result,
            duration: Date.now() - startTime,
            timestamp: new Date().toISOString(),
          };

        default:
          throw new Error(
            `Unknown sync type: ${type}. Supported types: supplier, item`,
          );
      }
    } catch (error) {
      // Enhanced error logging for production
      const errorContext = {
        jobId: job.id,
        jobType: job.data.type,
        userId: job.data.userFromToken?.id,
        error: error.message,
        stack: isProduction ? undefined : error.stack, // Don't log stack traces in production
        duration: Date.now() - startTime,
        attempt: job.attemptsMade + 1,
        maxAttempts: job.opts.attempts,
        timestamp: new Date().toISOString(),
      };

      if (scope?.cradle?.fastify) {
        scope.cradle.fastify.log.error('Worker job failed', errorContext);
      } else {
        console.error('Worker job failed', errorContext);
      }

      // Re-throw the error to let BullMQ handle retries
      throw error;
    }
  },
  {
    connection: redisConfig,
    concurrency: parseInt(process.env.REDIS_WORKER_CONCURRENCY) || 2, // Allow 2 concurrent syncs

    // Production-specific worker settings for long-running jobs
    ...(isProduction && {
      settings: {
        stalledInterval: parseInt(process.env.REDIS_STALLED_INTERVAL) || 60000, // Check every 60 seconds for long jobs
        maxStalledCount: parseInt(process.env.REDIS_MAX_STALLED_COUNT) || 2, // Allow more stalls for long jobs
      },
    }),
  },
);

// Enhanced event handlers for production monitoring
worker.on('completed', (job, result) => {
  const logData = {
    jobId: job.id,
    jobType: job.data.type,
    userId: job.data.userFromToken?.id,
    duration: result?.duration,
    timestamp: new Date().toISOString(),
    result: isProduction
      ? { success: result?.success, type: result?.type }
      : result,
  };

  console.log('Job completed successfully', logData);

  // Auto-remove completed jobs to save memory
  job.remove().catch((error) => {
    console.warn(`Failed to remove completed job ${job.id}:`, error.message);
  });
});

worker.on('failed', (job, error) => {
  const logData = {
    jobId: job.id,
    jobType: job.data?.type,
    userId: job.data?.userFromToken?.id,
    error: error.message,
    stack: isProduction ? undefined : error.stack,
    attempt: job.attemptsMade,
    maxAttempts: job.opts.attempts,
    timestamp: new Date().toISOString(),
    willRetry: job.attemptsMade < job.opts.attempts,
  };

  console.error('Job failed', logData);

  // In production, only remove jobs that have exhausted all retries
  if (isProduction && job.attemptsMade >= job.opts.attempts) {
    job.remove().catch((removeError) => {
      console.warn(
        `Failed to remove failed job ${job.id}:`,
        removeError.message,
      );
    });
  }
});

worker.on('stalled', (jobId) => {
  console.warn('Job stalled and will be retried', {
    jobId,
    timestamp: new Date().toISOString(),
  });
});

worker.on('error', (error) => {
  console.error('Worker error', {
    error: error.message,
    stack: isProduction ? undefined : error.stack,
    timestamp: new Date().toISOString(),
  });
});

// Graceful shutdown handling
process.on('SIGTERM', async () => {
  console.log('Received SIGTERM, shutting down worker gracefully...');
  await worker.close();
  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('Received SIGINT, shutting down worker gracefully...');
  await worker.close();
  process.exit(0);
});

// Log worker startup
console.log('Redis sync worker started', {
  queueName: 'prs-data-sync',
  concurrency: 1,
  isProduction,
  redisHost: redisConfig.host,
  redisPort: redisConfig.port,
  timestamp: new Date().toISOString(),
});

module.exports = worker;
