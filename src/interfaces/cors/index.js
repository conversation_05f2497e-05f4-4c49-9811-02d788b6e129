const corsSettings = () => {
  return (req, callback) => {
    const corsOptions = {
      // This is NOT recommended for production as it enables reflection exploits
      origin: true,
      exposedHeaders: ['Content-Disposition'],
    };

    // do not include CORS headers for requests from localhost
    // if (/^localhost$/m.test(req.headers.origin)) {
    //   corsOptions.origin = false;
    // }

    // callback expects two parameters: error and options
    callback(null, corsOptions);
  };
};

module.exports = corsSettings;
