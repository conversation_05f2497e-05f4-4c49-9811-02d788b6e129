const healthRoutes = async function (fastify) {
  // Health check endpoint
  fastify.get(
    '/health',
    {
      schema: {
        description: 'Health check endpoint',
        tags: ['health'],
        response: {
          200: {
            type: 'object',
            properties: {
              status: { type: 'string' },
              timestamp: { type: 'string' },
              service: { type: 'string' },
              version: { type: 'string' },
              environment: { type: 'string' },
              uptime: { type: 'number' },
              responseTime: { type: 'string' },
              checks: { type: 'object' },
            },
          },
          503: {
            type: 'object',
            properties: {
              status: { type: 'string' },
              timestamp: { type: 'string' },
              checks: { type: 'object' },
            },
          },
        },
      },
    },
    async (request, reply) => {
      const healthController =
        await fastify.diScope.resolve('healthController');
      return healthController.healthCheck(request, reply);
    },
  );

  // Readiness check endpoint
  fastify.get(
    '/ready',
    {
      schema: {
        description: 'Readiness check endpoint',
        tags: ['health'],
        response: {
          200: {
            type: 'object',
            properties: {
              status: { type: 'string' },
              timestamp: { type: 'string' },
            },
          },
          503: {
            type: 'object',
            properties: {
              status: { type: 'string' },
              error: { type: 'string' },
              timestamp: { type: 'string' },
            },
          },
        },
      },
    },
    async (request, reply) => {
      const healthController =
        await fastify.diScope.resolve('healthController');
      return healthController.readinessCheck(request, reply);
    },
  );

  // Liveness check endpoint
  fastify.get(
    '/live',
    {
      schema: {
        description: 'Liveness check endpoint',
        tags: ['health'],
        response: {
          200: {
            type: 'object',
            properties: {
              status: { type: 'string' },
              timestamp: { type: 'string' },
              uptime: { type: 'number' },
            },
          },
        },
      },
    },
    async (request, reply) => {
      const healthController =
        await fastify.diScope.resolve('healthController');
      return healthController.livenessCheck(request, reply);
    },
  );

  // Metrics endpoint
  fastify.get(
    '/metrics',
    {
      schema: {
        description: 'Application metrics endpoint',
        tags: ['health'],
        response: {
          200: {
            type: 'object',
            properties: {
              timestamp: { type: 'string' },
              service: { type: 'string' },
              environment: { type: 'string' },
              uptime: { type: 'number' },
              memory: { type: 'object' },
              cpu: { type: 'object' },
              version: { type: 'string' },
              platform: { type: 'string' },
              arch: { type: 'string' },
              database: { type: 'object' },
            },
          },
        },
      },
    },
    async (request, reply) => {
      const healthController =
        await fastify.diScope.resolve('healthController');
      return healthController.metricsCheck(request, reply);
    },
  );
};

module.exports = healthRoutes;
