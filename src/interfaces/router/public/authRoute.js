async function authRoute(fastify) {
  const authController = fastify.diScope.resolve('authController');
  const entities = fastify.diScope.resolve('entities');

  fastify.route({
    method: 'POST',
    url: '/login',
    schema: {
      body: entities.user.loginRequest,
    },
    handler: authController.login.bind(authController),
  });

  fastify.route({
    method: 'POST',
    url: '/token',
    preHandler: fastify.auth([fastify.verifyRefreshToken]),
    handler: authController.refreshUserToken.bind(authController),
  });

  fastify.route({
    method: 'POST',
    url: '/verify-otp',
    preHandler: fastify.auth([fastify.verifyOTPToken]),
    schema: {
      body: entities.user.verifyOTPRequest,
    },
    handler: authController.verifyOTP.bind(authController),
  });

  fastify.route({
    method: 'POST',
    url: '/setup-otp',
    preHandler: fastify.auth([fastify.verifyOTPToken]),
    schema: {
      body: entities.user.setupOTPRequest,
    },
    handler: authController.setupOTP.bind(authController),
  });

  fastify.route({
    method: 'POST',
    url: '/update-temp-password',
    preHandler: fastify.auth([fastify.verifyPassToken]),
    schema: {
      body: entities.user.updatePassRequest,
    },
    handler: authController.updateTempPassword.bind(authController),
  });

  fastify.route({
    method: 'POST',
    url: '/forgot-password',
    schema: {
      body: entities.user.forgotPasswordRequest,
    },
    handler: authController.forgotPassword.bind(authController),
  });
}

module.exports = authRoute;
