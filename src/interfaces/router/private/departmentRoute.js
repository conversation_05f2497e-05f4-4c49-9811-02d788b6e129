async function departmentRoutes(fastify) {
  const entities = fastify.diScope.resolve('entities');
  const departmentController = fastify.diScope.resolve('departmentController');
  const { PERMISSIONS } = fastify.diScope.resolve('constants').permission;

  fastify.route({
    method: 'GET',
    url: '/',
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.GET_DEPARTMENTS)]),
    handler: departmentController.getAllDepartments.bind(departmentController),
  });

  fastify.route({
    method: 'GET',
    url: '/:id',
    schema: {
      params: entities.department.getDepartmentByIdSchema,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.GET_DEPARTMENTS)]),
    handler: departmentController.getDepartmentById.bind(departmentController),
  });

  fastify.route({
    method: 'GET',
    url: '/:id/approvals',
    schema: {
      params: entities.department.getDepartmentByIdSchema,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.GET_DEPARTMENTS)]),
    handler:
      departmentController.getDepartmentApprovals.bind(departmentController),
  });

  fastify.route({
    method: 'PUT',
    url: '/:id/approvals',
    schema: {
      params: entities.department.getDepartmentByIdSchema,
      body: entities.approval.setupApprovalSchema,
    },
    preHandler: fastify.auth([
      fastify.authorize(PERMISSIONS.UPDATE_DEPARTMENTS),
    ]),
    handler:
      departmentController.setupDepartmentApprovals.bind(departmentController),
  });

  fastify.route({
    method: 'POST',
    url: '/sync',
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.SYNC_DEPARTMENTS)]),
    handler: departmentController.syncDepartments.bind(departmentController),
  });

  fastify.route({
    method: 'GET',
    url: '/association-approvals',
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.GET_DEPARTMENTS)]),
    handler:
      departmentController.getDepartmentAssociationApprovals.bind(
        departmentController,
      ),
  });

  fastify.route({
    method: 'PUT',
    url: '/association-approvals',
    schema: {
      body: entities.approval.setupAssociationApprovalSchema,
    },
    preHandler: fastify.auth([
      fastify.authorize(PERMISSIONS.UPDATE_DEPARTMENTS),
    ]),
    handler:
      departmentController.setupDepartmentAssociationApprovals.bind(
        departmentController,
      ),
  });
}

module.exports = departmentRoutes;
