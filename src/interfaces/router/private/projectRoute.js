async function projectRoutes(fastify) {
  const entities = fastify.diScope.resolve('entities');
  const projectController = fastify.diScope.resolve('projectController');
  const { PERMISSIONS } = fastify.diScope.resolve('constants').permission;

  fastify.route({
    method: 'GET',
    url: '/',
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.GET_PROJECTS)]),
    handler: projectController.getAllProjects.bind(projectController),
  });

  fastify.route({
    method: 'GET',
    url: '/:projectId',
    schema: {
      params: entities.project.projectIdParams,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.GET_PROJECTS)]),
    handler: projectController.getProject.bind(projectController),
  });

  fastify.route({
    method: 'GET',
    url: '/:projectId/approvals',
    schema: {
      params: entities.project.projectIdParams,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.GET_PROJECTS)]),
    handler: projectController.getProjectApprovals.bind(projectController),
  });

  fastify.route({
    method: 'POST',
    url: '/sync',
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.SYNC_PROJECTS)]),
    handler: projectController.syncProjects.bind(projectController),
  });

  fastify.route({
    method: 'PUT',
    url: '/:projectId',
    schema: {
      params: entities.project.projectIdParams,
      body: entities.project.updateProjectSchema,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.UPDATE_PROJECTS)]),
    handler: projectController.updateProject.bind(projectController),
  });

  fastify.route({
    method: 'PUT',
    url: '/:projectId/approvals',
    schema: {
      params: entities.project.projectIdParams,
      body: entities.approval.setupApprovalSchema,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.UPDATE_PROJECTS)]),
    handler: projectController.setupProjectApprovals.bind(projectController),
  });

  fastify.route({
    method: 'POST',
    url: '/:projectId/trades/:tradeId/engineers',
    schema: {
      params: entities.project.projectEngineerParams,
      body: entities.project.projectEngineerSchema,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.UPDATE_PROJECTS)]),
    handler: projectController.assignProjectEngineers.bind(projectController),
  });

  fastify.route({
    method: 'GET',
    url: '/:projectId/trades/:tradeId/engineers',
    schema: {
      params: entities.project.projectEngineerParams,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.GET_PROJECTS)]),
    handler: projectController.getProjectEngineers.bind(projectController),
  });

  fastify.route({
    method: 'GET',
    url: '/:projectId/trades/:tradeId/available-engineers',
    schema: {
      params: entities.project.projectEngineerParams,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.GET_PROJECTS)]),
    handler: projectController.getAvailableEngineers.bind(projectController),
  });

  fastify.route({
    method: 'DELETE',
    url: '/:projectId/trades/:tradeId/engineers/:engineerId',
    schema: {
      params: entities.project.deleteEngineerParams,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.DELETE_PROJECTS)]),
    handler: projectController.deleteEngineer.bind(projectController),
  });
}

module.exports = projectRoutes;
