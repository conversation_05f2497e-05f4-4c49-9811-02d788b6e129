async function downloadRoutes(fastify) {
  const downloadController = fastify.diScope.resolve('downloadController');
  const entities = fastify.diScope.resolve('entities');

  fastify.route({
    method: 'GET',
    url: '/dashboard',
    handler: downloadController.downloadDashboardExcel.bind(downloadController),
  });

  fastify.route({
    method: 'POST',
    url: '/non-rs-dashboard',
    handler: downloadController.downloadNonRSDashboard.bind(downloadController),
  });

  fastify.route({
    method: 'GET',
    url: '/item-history/:id',
    schema: {
      querystring: entities.downloadExcel.downloadItemHistoryQuerySchema,
      params: entities.downloadExcel.downloadItemHistoryParamsSchema,
    },
    handler: downloadController.downloadItemHistory.bind(downloadController),
  });

  fastify.route({
    method: 'GET',
    url: '/ofm-items',
    schema: {
      querystring: entities.downloadExcel.downloadOfmItemsQuerySchema,
    },
    handler: downloadController.downloadOfmItems.bind(downloadController),
  });

  fastify.route({
    method: 'GET',
    url: '/ofm-list',
    schema: {
      querystring: entities.downloadExcel.downloadOfmListQuerySchema,
    },
    handler: downloadController.downloadOfmList.bind(downloadController),
  });
}

module.exports = downloadRoutes;
