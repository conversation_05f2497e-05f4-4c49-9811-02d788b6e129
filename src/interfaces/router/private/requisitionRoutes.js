async function requisitionRoutes(fastify) {
  const entities = fastify.diScope.resolve('entities');
  const { PERMISSIONS } = fastify.diScope.resolve('constants').permission;
  const requisitionController = fastify.diScope.resolve(
    'requisitionController',
  );
  const requisitionItemListController = fastify.diScope.resolve(
    'requisitionItemListController',
  );
  const requisitionApproverController = fastify.diScope.resolve(
    'requisitionApproverController',
  );
  const deliveryReceiptController = fastify.diScope.resolve(
    'deliveryReceiptController',
  );
  const rsPaymentRequestController = fastify.diScope.resolve(
    'rsPaymentRequestController',
  );
  const purchaseOrderController = fastify.diScope.resolve(
    'purchaseOrderController',
  );
  const invoiceReportController = fastify.diScope.resolve(
    'invoiceReportController',
  );
  const requisitionService = fastify.diScope.resolve('requisitionService');
  const tomItemController = fastify.diScope.resolve('tomItemController');
  const utils = fastify.diScope.resolve('utils');

  fastify.route({
    method: 'POST',
    url: '/',
    preHandler: [
      fastify.auth([fastify.authorize(PERMISSIONS.CREATE_DASHBOARD)]),
      fastify.uploadFile,
      //manual validation
      async (req) => {
        const parsed = JSON.parse(JSON.stringify(req.body));
        const { attachments, ...requisitionDetails } = parsed;

        const checker = typeof requisitionDetails.itemList;

        const itemList =
          checker === 'string'
            ? JSON.parse(requisitionDetails.itemList)
            : requisitionDetails.itemList;

        const newPayload = {
          ...requisitionDetails,
          itemList,
        };

        const validated = await utils.parseDomain(
          entities.requisition.createRequisitionSchema,
          newPayload,
        );

        req.body = { ...validated, attachments };
        return req.body;
      },
    ],
    handler: requisitionController.createRequisition.bind(
      requisitionController,
    ),
  });

  fastify.route({
    method: 'GET',
    url: '/',
    schema: {
      query: entities.requisition.getRequisitionsSchema,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.GET_DASHBOARD)]),
    handler: requisitionController.getRequisitions.bind(requisitionController),
  });

  fastify.route({
    method: 'GET',
    url: '/:requisitionId',
    schema: {
      params: entities.requisition.getRequisitionByIdSchema,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.GET_DASHBOARD)]),
    handler: requisitionController.getRequisitionById.bind(
      requisitionController,
    ),
  });

  fastify.route({
    method: 'PUT',
    url: '/:requisitionId',
    preHandler: [
      fastify.auth([fastify.authorize(PERMISSIONS.CREATE_DASHBOARD)]),
      fastify.uploadFile,
      async (req) => {
        const parsed = JSON.parse(JSON.stringify(req.body));
        const { attachments, updateAttachments, ...requisitionDetails } =
          parsed;

        const checker = typeof requisitionDetails.itemList;

        const itemList =
          checker === 'string'
            ? JSON.parse(requisitionDetails.itemList)
            : requisitionDetails.itemList;

        const newPayload = {
          ...requisitionDetails,
          itemList,
        };

        const validated = await utils.parseDomain(
          entities.requisition.updateRequisitionSchema,
          newPayload,
        );

        if (updateAttachments) {
          req.body = { ...validated, attachments, updateAttachments };
        } else {
          req.body = { ...validated, attachments };
        }

        return req.body;
      },
    ],
    handler: requisitionController.updateRequisition.bind(
      requisitionController,
    ),
  });

  fastify.route({
    method: 'GET',
    url: '/:requisitionId/items',
    schema: {
      params: entities.requisition.getRequisitionByIdSchema,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.GET_DASHBOARD)]),
    handler: requisitionController.getRequisitionItems.bind(
      requisitionController,
    ),
  });

  fastify.route({
    method: 'GET',
    url: '/:requisitionId/canvass-item-list',
    schema: {
      params: entities.requisition.getRequisitionByIdSchema,
      query: entities.requisition.getRequisitionByIdQuerySchema,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.GET_DASHBOARD)]),
    handler: requisitionController.getCanvassItemList.bind(
      requisitionController,
    ),
  });

  fastify.route({
    method: 'GET',
    url: '/:requisitionId/canvass',
    schema: {
      params: entities.requisition.getRequisitionByIdParams,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.GET_CANVASS)]),
    handler: requisitionController.getCanvassRequisition.bind(
      requisitionController,
    ),
  });

  fastify.route({
    method: 'PUT',
    url: '/submit',
    preHandler: [
      fastify.auth([fastify.authorize(PERMISSIONS.CREATE_DASHBOARD)]),
      fastify.uploadFile,
      async (req) => {
        const parsed = JSON.parse(JSON.stringify(req.body));
        const { attachments, ...requisitionDetails } = parsed;

        const checker = typeof requisitionDetails.itemList;

        const itemList =
          checker === 'string'
            ? JSON.parse(requisitionDetails.itemList)
            : requisitionDetails.itemList;

        const newPayload = {
          ...requisitionDetails,
          itemList,
        };

        req.body = { ...newPayload, attachments };
        return req.body;
      },
    ],
    handler: requisitionController.submit.bind(requisitionController),
    onResponse: async (req) => requisitionService.saveHistory(req),
  });

  fastify.route({
    method: 'POST',
    url: '/:requisitionId/item-lists',
    schema: {
      params: entities.requisition.getRequisitionByIdSchema,
      body: entities.requisitionItemList.createRequisitionItemListSchema,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.UPDATE_DASHBOARD)]),
    handler: requisitionItemListController.createRequisitionItemList.bind(
      requisitionItemListController,
    ),
  });

  fastify.route({
    method: 'GET',
    url: '/:requisitionId/attachments',
    onRequest: async (req) =>
      (req.params = { ...req.params, id: req.params.requisitionId }),
    schema: {
      params: entities.requisition.getRequisitionByIdSchema,
      query: entities.requisition.getRequisitionAttachmentsSchema,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.GET_DASHBOARD)]),
    handler: requisitionController.getRequisitionAttachments.bind(
      requisitionController,
    ),
  });

  fastify.route({
    method: 'POST',
    url: '/:requisitionId/attachments',
    onRequest: async (req) =>
      (req.params = { ...req.params, id: req.params.requisitionId }),
    preHandler: [
      fastify.auth([fastify.authorize(PERMISSIONS.UPDATE_DASHBOARD)]),
      fastify.uploadLimit({
        route: 'requisition',
      }),
      fastify.uploadFile,
    ],
    schema: {
      params: entities.requisition.getRequisitionByIdSchema,
    },
    handler: requisitionController.createRequisitionAttachments.bind(
      requisitionController,
    ),
  });

  fastify.route({
    method: 'POST',
    url: '/:requisitionId/comments',
    onRequest: async (req) =>
      (req.params = { ...req.params, id: req.params.requisitionId }),
    schema: {
      params: entities.requisition.getRequisitionByIdSchema,
      body: entities.requisition.createRequisitionCommentSchema,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.UPDATE_DASHBOARD)]),
    handler: requisitionController.createRequisitionComment.bind(
      requisitionController,
    ),
  });

  fastify.route({
    method: 'GET',
    url: '/:requisitionId/comments',
    onRequest: async (req) =>
      (req.params = { ...req.params, id: req.params.requisitionId }),
    schema: {
      params: entities.requisition.getRequisitionByIdSchema,
      query: entities.requisition.getRequisitionCommentsSchema,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.GET_DASHBOARD)]),
    handler: requisitionController.getRequisitionComments.bind(
      requisitionController,
    ),
  });

  /* User token specific route */
  fastify.route({
    method: 'POST',
    url: '/seen',
    schema: {
      body: entities.requisition.markCommentOrAttachmentAsSeenSchema,
    },
    handler: requisitionController.markCommentOrAttachmentAsSeen.bind(
      requisitionController,
    ),
  });

  fastify.route({
    method: 'DELETE',
    url: '/requisition-item-lists/:id',
    schema: {
      params: entities.requisitionItemList.requisitionItemListIdSchema,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.DELETE_DASHBOARD)]),
    handler: requisitionItemListController.removeRequisitionItemList.bind(
      requisitionItemListController,
    ),
  });

  fastify.route({
    method: 'PUT',
    url: '/requisition-item-lists/:id',
    schema: {
      params: entities.requisitionItemList.requisitionItemListIdSchema,
      body: entities.requisitionItemList.updateRequisitionItemListSchema,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.UPDATE_DASHBOARD)]),
    handler: requisitionItemListController.updateRequisitionItemList.bind(
      requisitionItemListController,
    ),
  });

  /* Database Constants - no permission needed */
  fastify.route({
    method: 'GET',
    url: '/charge-to-lists',
    handler: requisitionController.getChargeToList.bind(requisitionController),
  });

  /* Delivery options - no permission needed */
  fastify.route({
    method: 'GET',
    url: '/delivery-addresses',
    handler: requisitionController.getDeliveryAddresses.bind(
      requisitionController,
    ),
  });

  fastify.route({
    method: 'POST',
    url: '/:requisitionId/tom-item-list',
    schema: {
      params: entities.requisition.getRequisitionByIdSchema,
      body: entities.requisition.createRequisitionItemSchema,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.UPDATE_DASHBOARD)]),
    handler: requisitionController.createRequisitionTomItemList.bind(
      requisitionController,
    ),
  });

  fastify.route({
    method: 'DELETE',
    url: '/tom-items/:id',
    schema: {
      params: entities.tomItem.tomItemIdSchema,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.DELETE_DASHBOARD)]),
    handler: tomItemController.removeTomItemById.bind(tomItemController),
  });

  fastify.route({
    method: 'PUT',
    url: '/tom-items/:id',
    schema: {
      params: entities.tomItem.tomItemIdSchema,
      body: entities.tomItem.updateTomItemSchema,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.UPDATE_DASHBOARD)]),
    handler: tomItemController.updateTomItem.bind(tomItemController),
  });

  fastify.route({
    method: 'PUT',
    url: '/:requisitionId/approve',
    schema: {
      params: entities.requisition.getRequisitionByIdSchema,
      body: entities.requisition.requisitionApproverIdSchema,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.UPDATE_DASHBOARD)]),
    handler: requisitionController.approveRequisition.bind(
      requisitionController,
    ),
  });

  fastify.route({
    method: 'PUT',
    url: '/:requisitionId/reject',
    schema: {
      params: entities.requisition.getRequisitionByIdSchema,
      body: entities.requisition.rejectRequisitionSchema,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.UPDATE_DASHBOARD)]),
    handler: requisitionController.rejectRequisition.bind(
      requisitionController,
    ),
  });

  fastify.route({
    method: 'PUT',
    url: '/:requisitionId/assign',
    schema: {
      params: entities.requisition.getRequisitionByIdSchema,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.UPDATE_DASHBOARD)]),
    handler: requisitionController.assignRequisition.bind(
      requisitionController,
    ),
  });

  fastify.route({
    method: 'GET',
    url: '/:requisitionId/approvers',
    schema: {
      params: entities.requisition.getRequisitionByIdSchema,
    },
    handler: requisitionApproverController.getRequisitionApprovers.bind(
      requisitionApproverController,
    ),
  });

  fastify.route({
    method: 'POST',
    url: '/:requisitionId/approver',
    schema: {
      params: entities.requisition.getRequisitionByIdSchema,
      body: entities.requisition.requisitionApproverIdSchema,
    },
    handler: requisitionApproverController.addRequisitionApprovers.bind(
      requisitionApproverController,
    ),
  });

  fastify.route({
    method: 'PUT',
    url: '/:requisitionId/approver/:id',
    schema: {
      params: entities.requisition.editApproverParamsSchema,
      body: entities.requisition.editApproverSchema,
    },
    handler: requisitionApproverController.editRequisitionApprovers.bind(
      requisitionApproverController,
    ),
  });

  fastify.route({
    method: 'PUT',
    url: '/:requisitionId/cancel',
    schema: {
      params: entities.requisition.cancelRequisitionByIdSchema,
    },
    handler: requisitionController.cancelRequisition.bind(
      requisitionController,
    ),
  });

  // GET /v1/requisitions/:requisitionId/delivery-receipts
  fastify.route({
    method: 'GET',
    url: '/:requisitionId/delivery-receipts',
    schema: {
      params:
        entities.deliveryReceipt.getDeliveryReceiptsFromRequisitionParamsSchema,
      query:
        entities.deliveryReceipt.getDeliveryReceiptsFromRequisitionQuerySchema,
    },
    handler:
      deliveryReceiptController.getDeliveryReceiptsFromRequisitionId.bind(
        deliveryReceiptController,
      ),
  });

  fastify.route({
    method: 'PUT',
    url: '/seen-badge',
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.UPDATE_DASHBOARD)]),
    handler: requisitionController.seenBadge.bind(requisitionController),
  });

  fastify.route({
    method: 'GET',
    url: '/:requisitionId/purchase-order',
    schema: {
      params: entities.purchaseOrder.getAllPurchaseOrderParamsSchema,
      query: entities.purchaseOrder.getAllPurchaseOrderQuerySchema,
    },
    handler: purchaseOrderController.getAllPurchaseOrders.bind(
      purchaseOrderController,
    ),
  });

  fastify.route({
    method: 'GET',
    url: '/:requisitionId/history',
    schema: {
      params: entities.requisition.getRequisitionByIdSchema,
      query: entities.requisitionHistory.getRequisitionHistorySchema,
    },
    handler: requisitionController.getRequisitionHistory.bind(
      requisitionController,
    ),
  });

  fastify.route({
    method: 'GET',
    url: '/:requisitionId/purchase-order-for-delivery',
    schema: {
      params: entities.requisition.getRequisitionByIdSchema,
    },
    handler: purchaseOrderController.getPOListForDelivery.bind(
      purchaseOrderController,
    ),
  });

  // GET /v1/requisitions/:requisitionId/payment-requests
  fastify.route({
    method: 'GET',
    url: '/:requisitionId/payment-requests',
    schema: {
      params:
        entities.rsPaymentRequest.getPaymentRequestsFromRequisitionParamsSchema,
      query:
        entities.rsPaymentRequest.getPaymentRequestsFromRequisitionQuerySchema,
    },
    handler:
      rsPaymentRequestController.getPaymentRequestsFromRequisitionId.bind(
        rsPaymentRequestController,
      ),
  });

  // GET /v1/requisitions/:requisitionId/delivery-returns
  fastify.route({
    method: 'GET',
    url: '/:requisitionId/delivery-returns',
    schema: {
      params:
        entities.deliveryReceipt.getDeliveryReceiptsFromRequisitionParamsSchema,
      query:
        entities.deliveryReceipt.getDeliveryReceiptsFromRequisitionQuerySchema,
    },
    handler: deliveryReceiptController.getDeliveryReturnsFromRequisitionId.bind(
      deliveryReceiptController,
    ),
  });

  // GET /v1/requisitions/:requisitionId/invoice-reports
  fastify.route({
    method: 'GET',
    url: '/:requisitionId/invoice-reports',
    schema: {
      params:
        entities.invoiceReport.getInvoiceReportsFromRequisitionParamsSchema,
      query: entities.invoiceReport.getInvoiceReportsFromRequisitionQuerySchema,
    },
    handler: invoiceReportController.getInvoiceReportFromRequisitionId.bind(
      invoiceReportController,
    ),
  });

  fastify.route({
    method: 'GET',
    url: '/options',
    handler: requisitionController.getOptions.bind(requisitionController),
  });

  fastify.route({
    method: 'DELETE',
    url: '/:requisitionId/items/:itemId',
    schema: {
      params: entities.requisitionItemList.deleteRequisitionItemListSchema,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.DELETE_DASHBOARD)]),
    handler: requisitionItemListController.deleteRequisitionItemList.bind(
      requisitionItemListController,
    ),
  });
}

module.exports = requisitionRoutes;
