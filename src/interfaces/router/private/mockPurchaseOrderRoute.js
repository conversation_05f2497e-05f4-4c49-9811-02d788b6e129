async function mockPurchaseOrderRoutes(fastify) {
  const mockPurchaseOrderController = fastify.diScope.resolve(
    'mockPurchaseOrderController',
  );

  // GET /v1/mock-purchase-orders
  fastify.route({
    method: 'GET',
    url: '/',
    handler: mockPurchaseOrderController.getAll.bind(
      mockPurchaseOrderController,
    ),
  });

  // GET /v1/mock-purchase-orders/:id
  fastify.route({
    method: 'GET',
    url: '/:id',
    handler: mockPurchaseOrderController.getById.bind(
      mockPurchaseOrderController,
    ),
  });
}

module.exports = mockPurchaseOrderRoutes;
