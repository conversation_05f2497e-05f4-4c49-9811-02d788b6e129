async function requisitionRoutesV2(fastify) {
  const entities = fastify.diScope.resolve('entities');
  const { PERMISSIONS } = fastify.diScope.resolve('constants').permission;
  const requisitionController = fastify.diScope.resolve(
    'requisitionController',
  );
  fastify.route({
    method: 'GET',
    url: '/',
    schema: {
      query: entities.requisition.getRequisitionsSchema,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.GET_DASHBOARD)]),
    handler: requisitionController.getRequisitionsV2.bind(
      requisitionController,
    ),
  });
}

module.exports = requisitionRoutesV2;
