async function purchaseOrderRouts(fastify) {
  const purchaseOrderController = fastify.diScope.resolve(
    'purchaseOrderController',
  );
  const deliveryReceiptController = fastify.diScope.resolve(
    'deliveryReceiptController',
  );
  const entities = fastify.diScope.resolve('entities');

  fastify.register((instance, _opts, done) => {
    instance.useTransaction();

    // POST /v1/purchase-orders/:id/submit
    instance.route({
      method: 'POST',
      url: '/:purchaseOrderId/submit',
      schema: {
        params: entities.purchaseOrder.getPurchaseOrderByIdParams,
        body: entities.purchaseOrder.submitPurchaseOrderBodySchema,
      },
      handler: purchaseOrderController.submitPurchaseOrder.bind(
        purchaseOrderController,
      ),
    });

    // POST /v1/purchase-orders/:id/submit-with-updates
    instance.route({
      method: 'POST',
      url: '/:purchaseOrderId/submit-with-updates',
      schema: {
        params: entities.purchaseOrder.getPurchaseOrderByIdParams,
        body: entities.purchaseOrder.submitWithUpdatesSchema,
      },
      handler: purchaseOrderController.submitPurchaseOrderWithUpdates.bind(
        purchaseOrderController,
      ),
    });

    // POST /v1/purchase-orders/:id/additional-fees
    instance.route({
      method: 'POST',
      url: '/:purchaseOrderId/additional-fees',
      schema: {
        params: entities.purchaseOrder.getPurchaseOrderByIdParams,
        body: entities.purchaseOrder.addAdditionalFeesSchema,
      },
      handler: purchaseOrderController.addAdditionalFees.bind(
        purchaseOrderController,
      ),
    });

    // POST /v1/purchase-orders/:id/cancel
    instance.route({
      method: 'POST',
      url: '/:purchaseOrderId/cancel',
      schema: {
        params: entities.purchaseOrder.getPurchaseOrderByIdParams,
      },
      handler: purchaseOrderController.cancelPurchaseOrder.bind(
        purchaseOrderController,
      ),
    });

    // POST /v1/purchase-orders/:id/approve
    instance.route({
      method: 'POST',
      url: '/:purchaseOrderId/approve',
      schema: {
        params: entities.purchaseOrder.getPurchaseOrderApproverEntityParams,
        body: entities.note.approveReasonSchema,
      },
      handler: purchaseOrderController.approvePurchaseOrder.bind(
        purchaseOrderController,
      ),
    });

    // POST /v1/purchase-orders/:id/reject
    instance.route({
      method: 'POST',
      url: '/:purchaseOrderId/reject',
      schema: {
        params: entities.purchaseOrder.getPurchaseOrderApproverEntityParams,
        body: entities.note.rejectReasonSchema,
      },
      handler: purchaseOrderController.rejectPurchaseOrder.bind(
        purchaseOrderController,
      ),
    });

    // POST /v1/purchase-orders/:id/add-adhoc-approver
    instance.route({
      method: 'POST',
      url: '/:purchaseOrderId/add-adhoc-approver',
      schema: {
        params: entities.purchaseOrder.getPurchaseOrderApproverEntityParams,
        body: entities.purchaseOrder.addPurchaseOrderAdhocApproverSchema,
      },
      handler: purchaseOrderController.addPurchaseOrderAdhocApprover.bind(
        purchaseOrderController,
      ),
    });

    // PUT /v1/purchase-orders/:id/update-for-delivery
    instance.route({
      method: 'PUT',
      url: '/:purchaseOrderId/update-for-delivery',
      schema: {
        params: entities.purchaseOrder.getPurchaseOrderByIdParams,
      },
      handler: purchaseOrderController.updatePurchaseOrderForDelivery.bind(
        purchaseOrderController,
      ),
    });

    done();
  });

  // GET /v1/purchase-orders/cancelled-items
  fastify.route({
    method: 'GET',
    url: '/cancelled-items',
    handler: purchaseOrderController.getCancelledItemsByPOIds.bind(
      purchaseOrderController,
    ),
  });

  // GET /v1/purchase-orders/:id
  fastify.route({
    method: 'GET',
    url: '/:purchaseOrderId',
    schema: {
      params: entities.purchaseOrder.getPurchaseOrderByIdParams,
    },
    handler: purchaseOrderController.getPOById.bind(purchaseOrderController),
  });

  // GET /v1/purchase-orders/:id/items
  fastify.route({
    method: 'GET',
    url: '/:purchaseOrderId/items',
    schema: {
      params: entities.purchaseOrder.getPurchaseOrderByIdParams,
    },
    handler: purchaseOrderController.getPOItemsById.bind(
      purchaseOrderController,
    ),
  });

  // GET /v1/purchase-orders/:id/delivery-receipts
  fastify.route({
    method: 'GET',
    url: '/:purchaseOrderId/delivery-receipts',
    schema: {
      params: entities.purchaseOrder.getPurchaseOrderByIdParams,
      query:
        entities.deliveryReceipt
          .getDeliveryReceiptsFromPurchaseOrderQuerySchema,
    },
    handler:
      deliveryReceiptController.getDeliveryReceiptsFromPurchaseOrderId.bind(
        deliveryReceiptController,
      ),
  });

  // PATCH /v1/purchase-orders/:id/items
  fastify.route({
    method: 'PATCH',
    url: '/:purchaseOrderId/items',
    schema: {
      params: entities.purchaseOrder.getPurchaseOrderByIdParams,
      body: entities.purchaseOrder.updatePurchaseOrderParams,
    },
    handler: purchaseOrderController.updatePurchaseOrder.bind(
      purchaseOrderController,
    ),
  });

  // GET /v1/purchase-orders/warranties
  fastify.route({
    method: 'GET',
    url: '/warranties',
    schema: {
      query: entities.warranty.getWarrantiesSchema,
    },
    handler: purchaseOrderController.getWarranties.bind(
      purchaseOrderController,
    ),
  });

  // POST /v1/purchase-orders/warranties
  fastify.route({
    method: 'POST',
    url: '/warranties',
    schema: {
      body: entities.warranty.createWarrantySchema,
    },
    handler: purchaseOrderController.createWarranty.bind(
      purchaseOrderController,
    ),
  });

  // GET /v1/purchase-orders/:id/approvers
  fastify.route({
    method: 'GET',
    url: '/:purchaseOrderId/approvers',
    schema: {
      params: entities.purchaseOrder.getPurchaseOrderApproverEntityParams,
    },
    handler: purchaseOrderController.getPurchaseOrderApprovers.bind(
      purchaseOrderController,
    ),
  });

  // GET /v1/purchase-orders/:id/assignee
  fastify.route({
    method: 'GET',
    url: '/:purchaseOrderId/assignee',
    schema: {
      params: entities.purchaseOrder.getPurchaseOrderApproverEntityParams,
    },
    handler: purchaseOrderController.getPurchaseOrderAssignee.bind(
      purchaseOrderController,
    ),
  });

  // DELETE /v1/purchase-orders/:id/remove-adhoc-approver
  fastify.route({
    method: 'DELETE',
    url: '/:purchaseOrderId/remove-adhoc-approver',
    schema: {
      params: entities.purchaseOrder.getPurchaseOrderApproverEntityParams,
    },
    handler: purchaseOrderController.removePurchaseOrderAdhocApprover.bind(
      purchaseOrderController,
    ),
  });

  // GET /v1/purchase-orders/allowed-po-adhoc-approvers
  fastify.route({
    method: 'GET',
    url: '/allowed-po-adhoc-approvers',
    handler: purchaseOrderController.getAllAllowedPOAdhocApprovers.bind(
      purchaseOrderController,
    ),
  });

  // POST /v1/purchase-orders/:id/resubmit-rejected-po
  fastify.route({
    method: 'POST',
    url: '/:purchaseOrderId/resubmit-rejected-po',
    schema: {
      params: entities.purchaseOrder.getPurchaseOrderApproverEntityParams,
    },
    handler: purchaseOrderController.resubmitRejectedPurchaseOrder.bind(
      purchaseOrderController,
    ),
  });

  // GET /v1/purchase-orders/:id/for-delivery
  fastify.route({
    method: 'GET',
    url: '/:purchaseOrderId/for-delivery',
    schema: {
      params: entities.purchaseOrder.getPurchaseOrderByIdParams,
    },
    handler: purchaseOrderController.getPODetailsForDelivery.bind(
      purchaseOrderController,
    ),
  });

  // POST /v1/purchase-orders/:id/close
  fastify.route({
    method: 'POST',
    url: '/:purchaseOrderId/close',
    schema: {
      params: entities.purchaseOrder.getPurchaseOrderByIdParams,
    },
    handler: purchaseOrderController.closePurchaseOrder.bind(
      purchaseOrderController,
    ),
  });

  // GET /v1/purchase-orders/:id/pdf
  fastify.route({
    method: 'GET',
    url: '/:purchaseOrderId/pdf',
    schema: {
      params: entities.purchaseOrder.getPurchaseOrderByIdParams,
    },
    handler: purchaseOrderController.generatePurchaseOrderPdf.bind(
      purchaseOrderController,
    ),
  });

  // GET /v1/purchase-orders/:id/invoice-reports
  fastify.route({
    method: 'GET',
    url: '/:purchaseOrderId/invoice-reports',
    schema: {
      query:
        entities.invoiceReport.getInvoiceReportsFromPurchaseOrderQuerySchema,
      params: entities.purchaseOrder.getPurchaseOrderByIdParams,
    },
    handler: purchaseOrderController.getInvoiceReportsFromPurchaseOrderId.bind(
      purchaseOrderController,
    ),
  });
}

module.exports = purchaseOrderRouts;
