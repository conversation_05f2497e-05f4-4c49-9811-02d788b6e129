async function supplierRoutes(fastify) {
  const entities = fastify.diScope.resolve('entities');
  const supplierController = fastify.diScope.resolve('supplierController');
  const { PERMISSIONS } = fastify.diScope.resolve('constants').permission;

  fastify.route({
    method: 'GET',
    url: '/',
    schema: {
      query: entities.supplier.getAllSuppliersSchema,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.GET_SUPPLIERS)]),
    handler: supplierController.getAllSuppliers.bind(supplierController),
  });

  fastify.route({
    method: 'GET',
    url: '/active',
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.GET_SUPPLIERS)]),
    handler: supplierController.getAllActiveSuppliers.bind(supplierController),
  });

  fastify.route({
    method: 'GET',
    url: '/:id',
    schema: {
      params: entities.supplier.getSupplierByIdSchema,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.GET_SUPPLIERS)]),
    handler: supplierController.getSupplierById.bind(supplierController),
  });

  fastify.route({
    method: 'PUT',
    url: '/:id',
    schema: {
      params: entities.supplier.getSupplierByIdSchema,
      body: entities.supplier.updateSupplierSchema,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.UPDATE_SUPPLIERS)]),
    handler: supplierController.updateSupplier.bind(supplierController),
  });

  // TODO: Ask sir @richard if this endpoint is necessary
  fastify.route({
    method: 'DELETE',
    url: '/:id',
    schema: {
      params: entities.supplier.destroySupplierByIdSchema,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.DELETE_SUPPLIERS)]),
    handler: supplierController.destroySupplierById.bind(supplierController),
  });

  fastify.route({
    method: 'POST',
    url: '/sync',
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.SYNC_SUPPLIERS)]),
    handler: supplierController.syncSuppliers.bind(supplierController),
  });

  fastify.route({
    method: 'POST',
    url: '/:id/comments',
    schema: {
      params: entities.supplier.getSupplierByIdSchema,
      body: entities.supplier.createSupplierCommentSchema,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.UPDATE_SUPPLIERS)]),
    handler: supplierController.createSupplierComment.bind(supplierController),
  });

  fastify.route({
    method: 'GET',
    url: '/:id/comments',
    schema: {
      params: entities.supplier.getSupplierByIdSchema,
      query: entities.supplier.getSupplierCommentsSchema,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.UPDATE_SUPPLIERS)]),
    handler: supplierController.getSupplierComments.bind(supplierController),
  });

  fastify.route({
    method: 'POST',
    url: '/:id/attachments',
    preHandler: [
      fastify.auth([fastify.authorize(PERMISSIONS.UPDATE_SUPPLIERS)]),
      fastify.uploadFile,
    ],
    schema: {
      params: entities.supplier.getSupplierByIdSchema,
    },
    handler:
      supplierController.createSupplierAttachments.bind(supplierController),
  });

  fastify.route({
    method: 'GET',
    url: '/:id/attachments',
    schema: {
      params: entities.supplier.getSupplierByIdSchema,
      query: entities.supplier.getSupplierAttachmentsSchema,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.GET_SUPPLIERS)]),
    handler: supplierController.getSupplierAttachments.bind(supplierController),
  });

  /* User token specific route */
  fastify.route({
    method: 'POST',
    url: '/seen',
    schema: {
      body: entities.supplier.markCommentOrAttachmentAsSeenSchema,
    },
    handler:
      supplierController.markCommentOrAttachmentAsSeen.bind(supplierController),
  });

  fastify.route({
    method: 'PUT',
    url: '/:id/attachments',
    schema: { body: entities.supplier.deleteSupplierAttachment },
    handler: supplierController.updateAttachment.bind(supplierController),
  });
}

module.exports = supplierRoutes;
