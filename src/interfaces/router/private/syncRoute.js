async function syncRoutes(fastify) {
  const syncController = fastify.diScope.resolve('syncController');

  const { permission: permissionConstants, user: userConstants } =
    fastify.diScope.resolve('constants');
  const { PERMISSIONS } = permissionConstants;

  fastify.route({
    method: 'GET',
    url: '/:jobId',
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.GET_USERS)]),
    handler: syncController.getSyncStatus.bind(syncController),
  });
}

module.exports = syncRoutes;
