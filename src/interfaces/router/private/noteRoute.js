async function noteRoutes(fastify) {
  const entities = fastify.diScope.resolve('entities');
  const noteController = fastify.diScope.resolve('noteController');

  // POST /notes
  fastify.route({
    method: 'POST',
    url: '/',
    schema: {
      body: entities.note.createNoteSchema,
    },
    handler: noteController.createNote.bind(noteController),
  });

  // GET /notes/:model/:modelId?dateFrom=&dateTo=
  fastify.route({
    method: 'GET',
    url: '/:model/:modelId',
    schema: {
      params: entities.note.getNotesParamsSchema,
      query: entities.note.getNotesParamsQuery,
    },
    handler: noteController.getNotes.bind(noteController),
  });

  // PUT /notes/:model/:modelId/seen?dateFrom=&dateTo=
  fastify.route({
    method: 'PUT',
    url: '/:model/:modelId/seen',
    schema: {
      params: entities.note.getNotesParamsSchema,
      query: entities.note.getNotesParamsQuery,
    },
    handler: noteController.markNoteAsSeen.bind(noteController),
  });
}

module.exports = noteRoutes;
