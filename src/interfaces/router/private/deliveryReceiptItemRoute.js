async function deliveryReceiptItemRoutes(fastify) {
  const entities = fastify.diScope.resolve('entities');
  const deliveryReceiptItemController = fastify.diScope.resolve(
    'deliveryReceiptItemController',
  );

  // GET /v1/delivery-receipt-items/:id/history
  fastify.route({
    method: 'GET',
    url: '/:id/history',
    schema: {
      route: entities.deliveryReceiptItem.getDeliveryReceiptItemByIdSchema,
    },
    handler: deliveryReceiptItemController.getItemHistory.bind(
      deliveryReceiptItemController,
    ),
  });

  // PUT /v1/delivery-receipt-items/:id
  fastify.route({
    method: 'PUT',
    url: '/:id',
    schema: {
      body: entities.deliveryReceiptItem.updateDeliveryReceiptItemSchema,
    },
    handler: deliveryReceiptItemController.updateItem.bind(
      deliveryReceiptItemController,
    ),
  });

  // POST /v1/delivery-receipt-items/:id/cancel-returns
  fastify.route({
    method: 'POST',
    url: '/:id/cancel-returns',
    schema: {
      route: entities.deliveryReceiptItem.getDeliveryReceiptItemByIdSchema,
    },
    handler: deliveryReceiptItemController.cancelReturns.bind(
      deliveryReceiptItemController,
    ),
  });
}

module.exports = deliveryReceiptItemRoutes;
