async function itemRoutes(fastify) {
  const entities = fastify.diScope.resolve('entities');
  const itemController = fastify.diScope.resolve('itemController');
  const { PERMISSIONS } = fastify.diScope.resolve('constants').permission;

  fastify.route({
    method: 'POST',
    url: '/sync',
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.SYNC_OFM_ITEMS)]),
    handler: itemController.syncItems.bind(itemController),
  });

  fastify.route({
    method: 'GET',
    url: '/',
    schema: {
      query: entities.item.getItemsSchema,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.GET_OFM_ITEMS)]),
    handler: itemController.getItems.bind(itemController),
  });

  fastify.route({
    method: 'POST',
    url: '/non-ofm',
    preHandler: fastify.auth([
      fastify.authorize(PERMISSIONS.CREATE_NON_OFM_ITEMS),
    ]),
    schema: { body: entities.nonOfmItem.nonOfmItemSchema },
    handler: itemController.createNonOfmItem.bind(itemController),
  });

  fastify.route({
    method: 'GET',
    url: '/non-ofm',
    preHandler: fastify.auth([
      fastify.authorize(PERMISSIONS.GET_NON_OFM_ITEMS),
    ]),
    handler: itemController.getAllNonOfmItems.bind(itemController),
  });

  fastify.route({
    method: 'GET',
    url: '/non-ofm/:id',
    preHandler: fastify.auth([
      fastify.authorize(PERMISSIONS.GET_NON_OFM_ITEMS),
    ]),
    handler: itemController.getNonOfmItemById.bind(itemController),
  });

  fastify.route({
    method: 'PUT',
    url: '/non-ofm/:id',
    preHandler: fastify.auth([
      fastify.authorize(PERMISSIONS.UPDATE_NON_OFM_ITEMS),
    ]),
    handler: itemController.updateNonOfmItem.bind(itemController),
  });

  fastify.route({
    method: 'GET',
    url: '/non-ofm/units',
    preHandler: fastify.auth([
      fastify.authorize(PERMISSIONS.GET_NON_OFM_ITEMS),
    ]),
    handler: itemController.getNonOfmUniqueUnits.bind(itemController),
  });

  fastify.route({
    method: 'GET',
    url: '/ofm/units',
    preHandler: fastify.auth([
      fastify.authorize(PERMISSIONS.GET_NON_OFM_ITEMS),
    ]),
    handler: itemController.getOfmUniqueUnits.bind(itemController),
  });

  fastify.route({
    method: 'GET',
    url: '/ofm-lists',
    schema: {
      query: entities.item.getOfmItemListsSchema,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.GET_OFM_LISTS)]),
    handler: itemController.getAllOfmItemLists.bind(itemController),
  });

  fastify.route({
    method: 'GET',
    url: '/ofm-list-items',
    schema: {
      query: entities.item.getOfmListItemsSchema,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.GET_OFM_ITEMS)]),
    handler: itemController.getAllOfmListItems.bind(itemController),
  });

  fastify.route({
    method: 'GET',
    url: '/:id',
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.GET_OFM_ITEMS)]),
    handler: itemController.getOfmItemById.bind(itemController),
  });

  /* Database Trade Constants - no permission needed */
  fastify.route({
    method: 'GET',
    url: '/trades',
    schema: {
      querystring: entities.trade.getTradesSchema,
    },
    handler: itemController.getAllTrades.bind(itemController),
  });

  fastify.route({
    method: 'GET',
    url: '/ofm-lists/:id',
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.GET_OFM_ITEMS)]),
    handler: itemController.getOfmItemListById.bind(itemController),
  });

  fastify.route({
    method: 'GET',
    url: '/ofm-items-by-list/:id',
    schema: {
      query: entities.item.getOfmListItemsSchema,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.GET_OFM_ITEMS)]),
    handler: itemController.getOfmItemsByListId.bind(itemController),
  });

  fastify.route({
    method: 'GET',
    url: '/history/:id',
    schema: {
      params: entities.item.getItemParams,
      query: entities.history.getHistorySchema,
    },
    handler: itemController.itemHistory.bind(itemController),
  });

  fastify.route({
    method: 'GET',
    url: '/ofm/:id/purchase-history',
    schema: {
      params: entities.item.getItemParams,
      query: entities.history.getHistorySchema,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.GET_OFM_ITEMS)]),
    handler: itemController.itemPurchaseHistory.bind(itemController, 'ofm'),
  });

  fastify.route({
    method: 'GET',
    url: '/non-ofm/:id/purchase-history',
    schema: {
      params: entities.item.getItemParams,
      query: entities.history.getHistorySchema,
    },
    preHandler: fastify.auth([
      fastify.authorize(PERMISSIONS.GET_NON_OFM_ITEMS),
    ]),
    handler: itemController.itemPurchaseHistory.bind(itemController, 'non-ofm'),
  });

  fastify.route({
    method: 'PUT',
    url: '/ofm/:id',
    schema: {
      params: entities.item.getItemParams,
      body: entities.item.updateItemSchema,
    },
    // preHandler: fastify.auth([fastify.authorize(PERMISSIONS.UPDATE_OFM_ITEMS)]),
    handler: itemController.updateOfmItem.bind(itemController),
  });

  fastify.route({
    method: 'PUT',
    url: '/ofm/bulk-update',
    // preHandler: fastify.auth([fastify.authorize(PERMISSIONS.UPDATE_OFM_ITEMS)]),
    handler: itemController.bulkUpdateOfmItems.bind(itemController),
  });
}

module.exports = itemRoutes;
