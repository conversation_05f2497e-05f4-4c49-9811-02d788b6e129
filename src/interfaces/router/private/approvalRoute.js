async function approvalRoutes(fastify) {
  const approvalController = fastify.diScope.resolve('approvalController');

  fastify.route({
    method: 'GET',
    url: '/types',
    handler: approvalController.getApprovalTypes.bind(approvalController),
  });

  fastify.route({
    method: 'GET',
    url: '/areas',
    handler: approvalController.getApprovalAreas.bind(approvalController),
  });
}

module.exports = approvalRoutes;
