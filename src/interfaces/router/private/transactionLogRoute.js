async function transactionLogRoute(fastify) {
  const entities = fastify.diScope.resolve('entities');
  const transactionLogController = fastify.diScope.resolve(
    'transactionLogController',
  );
  const utils = fastify.diScope.resolve('utils');

  fastify.route({
    method: 'GET',
    url: '/:rsId',
    handler: transactionLogController.getRequisitionJourney.bind(
      transactionLogController,
    ),
  });

  // fastify.route({
  //   method: 'POST',
  //   url: '/',
  //   preHandler: [
  //     async(req) => {
  //       const parsed = JSON.parse(JSON.stringify(req.body));

  //       const validated = await utils.parseDomain(
  //         entities.transactionLog.transactionLogRequestSchema,
  //         parsed,
  //       );

  //       req.body = validated;
  //       return req.body;
  //     }
  //   ],
  //   handler: transactionLogController.createTransactionLog.bind(transactionLogController),
  // });
}

module.exports = transactionLogRoute;
