async function deliveryReceiptRoutes(fastify) {
  const entities = fastify.diScope.resolve('entities');
  const deliveryReceiptController = fastify.diScope.resolve(
    'deliveryReceiptController',
  );

  fastify.register((instance, _opts, done) => {
    instance.useTransaction();

    // POST /v1/delivery-receipts
    instance.route({
      method: 'POST',
      url: '/',
      schema: {
        body: entities.deliveryReceipt.createDeliveryReceiptSchema,
      },
      handler: deliveryReceiptController.createDeliveryReceipt.bind(
        deliveryReceiptController,
      ),
    });

    // PUT /v1/delivery-receipts/:id
    instance.route({
      method: 'PUT',
      url: '/:id',
      schema: {
        body: entities.deliveryReceipt.updateDeliveryReceiptSchema,
      },
      handler: deliveryReceiptController.updateDeliveryReceiptById.bind(
        deliveryReceiptController,
      ),
    });

    done();
  });

  // GET /v1/delivery-receipts/:id
  fastify.route({
    method: 'GET',
    url: '/:id',
    schema: {
      params: entities.deliveryReceipt.getDeliveryReceiptByIdSchema,
    },
    handler: deliveryReceiptController.getDeliveryReceiptById.bind(
      deliveryReceiptController,
    ),
  });

  // GET /v1/delivery-receipts/:id/items
  fastify.route({
    method: 'GET',
    url: '/:id/items',
    schema: {
      params: entities.deliveryReceipt.getDeliveryReceiptByIdSchema,
      query: entities.deliveryReceipt.getDeliveryReceiptItemsQuerySchema,
    },
    handler: deliveryReceiptController.getDeliveryReceiptItems.bind(
      deliveryReceiptController,
    ),
  });

  // GET /v1/delivery-receipts/:id/items/:itemId/history
  fastify.route({
    method: 'GET',
    url: '/:id/items/:itemId/history',
    handler: deliveryReceiptController.getDeliveryReceiptItemHistory.bind(
      deliveryReceiptController,
    ),
  });

  /**
   * Schemas
   */
  fastify.addSchema({
    $id: 'IdNameObject',
    type: 'object',
    properties: {
      id: { type: 'number' },
      name: { type: 'string' },
    },
  });

  fastify.addSchema({
    $id: 'GetDeliveryReceiptByIdResponse',
    type: 'object',
    properties: {
      id: { type: 'number' },
      drNumber: { type: 'string' },
      rsNumber: { type: 'string' },
      isDraft: { type: 'boolean' },
      poNumber: {
        $ref: 'IdNameObject#',
      },
      supplier: {
        $ref: 'IdNameObject#',
      },
      note: { type: 'string' },
      items: {
        type: 'array',
        items: {
          $ref: 'GetDeliveryReceiptItemsResponse#',
        },
      },
    },
  });

  fastify.addSchema({
    $id: 'GetDeliveryReceiptItemsResponse',
    type: 'object',
    properties: {
      id: { type: 'number' },
      itemCd: { type: 'string' },
      itemDes: { type: 'string' },
      qtyOrdered: { type: 'number' },
      qtyDelivered: { type: 'number' },
      dateDelivered: { type: 'string', format: 'date' },
      unit: { type: 'string' },
      deliveryStatus: { type: 'string' },
      notes: { type: 'string' },
    },
  });
}

module.exports = deliveryReceiptRoutes;
