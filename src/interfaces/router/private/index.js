const userRoutes = require('./userRoute');
const supplierRoutes = require('./supplierRoute');
const companyRoutes = require('./companyRoute');
const projectRoutes = require('./projectRoute');
const departmentRoutes = require('./departmentRoute');
const itemRoutes = require('./itemRoute');
const notificationRoutes = require('./notificationRoute');
const auditLogRoutes = require('./auditLogRoute');
const requisitionRoutes = require('./requisitionRoutes');
const approvalRoutes = require('./approvalRoute');
const canvassRoutes = require('./canvassRoute');
const deliveryReceiptRoutes = require('./deliveryReceiptRoute');
const mockPurchaseOrderRoutes = require('./mockPurchaseOrderRoute');
const attachmentRoutes = require('./attachmentRoute');
const deliveryReceiptItemRoutes = require('./deliveryReceiptItemRoute');
const noteRoutes = require('./noteRoute');
const leaveRoute = require('./leaveRoute');
const steelbarsRoutes = require('./steelbarsRoute');
const purchaseOrderRoute = require('./purchaseOrderRoute');
const rsPaymentRequestRoutes = require('./rsPaymentRequestRoute');
const downloadRoutes = require('./downloadRoutes');
const requestHistoryRoutes = require('./requestHistoryRoute');
const nonRequisitionRoutes = require('./nonRequisitionRoute');
const requisitionRoutesV2 = require('./requisitionRoutesV2');
const invoiceReportRoutes = require('./invoiceReportRoute');
const templateRoute = require('./templateRoute');
const transactionLogRoute = require('./transactionLogRoute');
const forceCloseRoutes = require('./forceCloseRoutes');
const syncRoutes = require('./syncRoute');

const privateRoutes = async (serverContext) => {
  /* All routes registered in this section are required for user authentication (jwt) */
  serverContext.register((instance, _opts, next) => {
    instance.addHook('onRequest', serverContext.authenticate);

    serverContext.register(transactionLogRoute, {
      prefix: 'v1/transaction-logs',
    });

    serverContext.register(userRoutes, {
      prefix: 'v1/users',
    });

    serverContext.register(supplierRoutes, {
      prefix: 'v1/suppliers',
    });

    serverContext.register(companyRoutes, {
      prefix: 'v1/companies',
    });

    serverContext.register(projectRoutes, {
      prefix: 'v1/projects',
    });

    serverContext.register(departmentRoutes, {
      prefix: 'v1/departments',
    });

    serverContext.register(itemRoutes, {
      prefix: 'v1/items',
    });

    serverContext.register(notificationRoutes, {
      prefix: 'v1/notifications',
    });

    serverContext.register(auditLogRoutes, {
      prefix: 'v1/auditlogs',
    });

    serverContext.register(requisitionRoutes, {
      prefix: 'v1/requisitions',
    });

    serverContext.register(approvalRoutes, {
      prefix: 'v1/approvals',
    });

    serverContext.register(canvassRoutes, {
      prefix: 'v1/canvass',
    });

    serverContext.register(deliveryReceiptRoutes, {
      prefix: 'v1/delivery-receipts',
    });

    serverContext.register(mockPurchaseOrderRoutes, {
      prefix: 'v1/mock-purchase-orders',
    });

    serverContext.register(attachmentRoutes, {
      prefix: 'v1/attachments',
    });

    serverContext.register(deliveryReceiptItemRoutes, {
      prefix: 'v1/delivery-receipt-items',
    });

    serverContext.register(noteRoutes, {
      prefix: 'v1/notes',
    });

    serverContext.register(leaveRoute, {
      prefix: 'v1/leaves',
    });

    serverContext.register(steelbarsRoutes, {
      prefix: 'v1/steelbars',
    });

    serverContext.register(purchaseOrderRoute, {
      prefix: 'v1/purchase-orders',
    });

    serverContext.register(rsPaymentRequestRoutes, {
      prefix: 'v1/rs-payment-request',
    });

    serverContext.register(downloadRoutes, {
      prefix: 'v1/download',
    });

    serverContext.register(requestHistoryRoutes, {
      prefix: 'v1/request-history',
    });

    serverContext.register(nonRequisitionRoutes, {
      prefix: 'v1/non-requisitions',
    });

    serverContext.register(requisitionRoutesV2, {
      prefix: 'v2/requisitions',
    });

    serverContext.register(invoiceReportRoutes, {
      prefix: 'v1/invoice-reports',
    });

    serverContext.register(templateRoute, {
      prefix: 'v1/generate-template',
    });

    serverContext.register(forceCloseRoutes, {
      prefix: 'v1/requisitions',
    });

    serverContext.register(syncRoutes, {
      prefix: 'v1/sync',
    });

    next();
  });
};

module.exports = privateRoutes;
