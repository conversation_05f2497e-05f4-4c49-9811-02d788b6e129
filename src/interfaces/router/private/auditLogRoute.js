async function auditLogRoutes(fastify) {
  const entities = fastify.diScope.resolve('entities');
  const auditLogController = fastify.diScope.resolve('auditLogController');
  const { PERMISSIONS } = fastify.diScope.resolve('constants').permission;

  fastify.route({
    method: 'GET',
    url: '/',
    schema: {
      query: entities.auditLog.getAllAuditLogsSchema,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.GET_AUDIT_LOGS)]),
    handler: auditLogController.getAllAuditLogs.bind(auditLogController),
  });

  fastify.route({
    method: 'GET',
    url: '/:id',
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.GET_AUDIT_LOGS)]),
    handler: auditLogController.getAuditLogById.bind(auditLogController),
  });
}

module.exports = auditLogRoutes;
