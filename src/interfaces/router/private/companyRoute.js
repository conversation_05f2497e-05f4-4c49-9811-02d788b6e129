async function companyRoutes(fastify) {
  const entities = fastify.diScope.resolve('entities');
  const companyController = fastify.diScope.resolve('companyController');
  const { PERMISSIONS } = fastify.diScope.resolve('constants').permission;

  fastify.route({
    method: 'GET',
    url: '/',
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.GET_COMPANIES)]),
    handler: companyController.getAllCompanies.bind(companyController),
  });

  fastify.route({
    method: 'GET',
    url: '/:companyId',
    schema: {
      params: entities.company.getCompanyParams,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.GET_COMPANIES)]),
    handler: companyController.getCompany.bind(companyController),
  });

  fastify.route({
    method: 'POST',
    url: '/',
    schema: {
      body: entities.company.createCompanySchema,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.CREATE_COMPANIES)]),
    handler: companyController.createCompany.bind(companyController),
  });

  fastify.route({
    method: 'POST',
    url: '/sync',
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.SYNC_COMPANIES)]),
    handler: companyController.syncCompanies.bind(companyController),
  });

  fastify.route({
    method: 'PUT',
    url: '/:companyId',
    schema: {
      params: entities.company.getCompanyParams,
      body: entities.company.updateCompanySchema,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.UPDATE_COMPANIES)]),
    handler: companyController.updateCompany.bind(companyController),
  });

  fastify.route({
    method: 'DELETE',
    url: '/:companyId',
    schema: {
      params: entities.company.getCompanyParams,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.DELETE_COMPANIES)]),
    handler: companyController.deleteCompany.bind(companyController),
  });

  fastify.route({
    method: 'GET',
    url: '/:companyId/tagged-projects',
    schema: {
      params: entities.company.getCompanyParams,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.GET_COMPANIES)]),
    handler: companyController.getCompanyTaggedProjects.bind(companyController),
  });

  fastify.route({
    method: 'GET',
    url: '/untagged-projects',
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.GET_COMPANIES)]),
    handler:
      companyController.getCompanyUntaggedProjects.bind(companyController),
  });

  fastify.route({
    method: 'GET',
    url: '/:companyId/untagged-projects',
    schema: {
      params: entities.company.getCompanyParams,
    },
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.GET_COMPANIES)]),
    handler:
      companyController.getCompanyUntaggedProjects.bind(companyController),
  });

  fastify.route({
    method: 'DELETE',
    url: '/:companyId/remove-tag/:projectId',
    preHandler: fastify.auth([fastify.authorize(PERMISSIONS.GET_COMPANIES)]),
    handler: companyController.removeCompanyProjectTag.bind(companyController),
  });
}

module.exports = companyRoutes;
