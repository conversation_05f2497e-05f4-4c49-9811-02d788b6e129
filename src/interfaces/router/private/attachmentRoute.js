async function attachmentRoutes(fastify) {
  const entities = fastify.diScope.resolve('entities');
  const attachmentController = fastify.diScope.resolve('attachmentController');

  // POST /attachments
  fastify.route({
    method: 'POST',
    url: '/',
    preHandler: [fastify.uploadFile],
    handler: attachmentController.createAttachment.bind(attachmentController),
  });

  fastify.route({
    method: 'POST',
    url: '/assign',
    schema: {
      body: entities.attachment.assignModelIdToAttachmentsSchema,
    },
    handler: attachmentController.assignAttachments.bind(attachmentController),
  });

  // GET /attachments/:model/:modelId
  fastify.route({
    method: 'GET',
    url: '/:model/:modelId',
    schema: {
      params: entities.attachment.getAttachmentsParamsSchema,
      query: entities.attachment.getAttachmentsQuerySchema,
    },
    handler: attachmentController.getAttachments.bind(attachmentController),
  });

  // DELETE /attachments
  fastify.route({
    method: 'DELETE',
    url: '/',
    schema: {
      body: entities.attachment.deleteAttachmentsSchema,
    },
    handler: attachmentController.removeAttachments.bind(attachmentController),
  });

  fastify.route({
    method: 'DELETE',
    url: '/:id',
    schema: {
      param: entities.attachment.deleteAttachmentParams,
    },
    handler:
      attachmentController.removeAttachmentById.bind(attachmentController),
  });

  // PUT /attachments/:model/:modelId/seen
  fastify.route({
    method: 'PUT',
    url: '/:model/:modelId/seen',
    schema: {
      params: entities.attachment.getAttachmentsParamsSchema,
    },
    handler:
      attachmentController.markAttachmentAsSeen.bind(attachmentController),
  });
}

module.exports = attachmentRoutes;
