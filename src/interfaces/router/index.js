const privateRoutes = require('./private');
const publicRoutes = require('./public');
const healthRoutes = require('./health');

const routesDefinitions = async (serverContext) => {
  const utils = serverContext.diScope.resolve('utils');

  /* Add validation middleware to all routes */
  serverContext.addHook('onRoute', (routeOptions) => {
    routeOptions.validatorCompiler = (data) =>
      utils.validatorCompiler(data.schema);
  });

  // serverContext.addHook('onSend', serverContext.auditLogs);
  serverContext.addHook('onSend', serverContext.transactionLogs);

  await healthRoutes(serverContext);
  await publicRoutes(serverContext);
  await privateRoutes(serverContext);
};

module.exports = routesDefinitions;
