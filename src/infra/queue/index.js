const { Queue } = require('bullmq');
const redisConfig = require('./redis');

const isProduction = process.env.NODE_ENV === 'production';

function createSyncQueue() {
  // Log configuration (without sensitive data) for debugging
  const logConfig = {
    host: redisConfig.host,
    port: redisConfig.port,
    hasPassword: !!redisConfig.password,
    isProduction,
    message: 'createSyncQueue',
  };

  if (!isProduction || process.env.LOG_LEVEL === 'debug') {
    console.log('Creating sync queue with config:', logConfig);
  }

  try {
    const queue = new Queue('prs-data-sync', {
      connection: redisConfig,
      defaultJobOptions: {
        // Retry configuration for long-running jobs
        attempts: parseInt(process.env.REDIS_JOB_ATTEMPTS) || (isProduction ? 5 : 3),
        backoff: {
          type: 'exponential',
          delay: parseInt(process.env.REDIS_JOB_BACKOFF_DELAY) || (isProduction ? 5000 : 5000),
        },
        // Long job configuration
        removeOnComplete: parseInt(process.env.REDIS_COMPLETED_JOB_RETENTION) || 10,
        removeOnFail: parseInt(process.env.REDIS_FAILED_JOB_RETENTION) || 50,
        // Job TTL for long-running operations (2 hours default)
        ttl: parseInt(process.env.REDIS_JOB_TTL) || 7200000, // 2 hours in milliseconds

        // Job cleanup configuration
        removeOnComplete: isProduction ? 50 : 100, // Keep fewer completed jobs in production
        removeOnFail: isProduction ? 100 : 200, // Keep more failed jobs for debugging

        // Job timeout configuration
        delay: 0, // No delay by default

        // Production-specific job options
        ...(isProduction && {
          // Job timeout (30 minutes for long-running sync operations)
          jobTimeout: 30 * 60 * 1000,

          // Priority settings
          priority: 0, // Default priority

          // Note: Repeat configuration can be added per job when needed
          // repeat: { pattern: '0 */6 * * *' } // Example: Every 6 hours
        }),
      },

      // Queue-level settings
      settings: {
        // Stalled job handling
        stalledInterval: 5 * 60 * 1000, // Check for stalled jobs every 5 minutes (longer for sync operations)
        maxStalledCount: 3, // Allow jobs to be stalled up to 3 times before failing

        // Production-specific settings
        ...(isProduction && {
          // Retry failed jobs more aggressively in production
          retryProcessDelay: 5000, // 5 seconds between retry attempts
        }),
      },
    });

    // Add error event listeners for production monitoring
    if (isProduction) {
      queue.on('error', (error) => {
        console.error('Queue error:', {
          error: error.message,
          stack: error.stack,
          timestamp: new Date().toISOString(),
        });
      });

      queue.on('waiting', (jobId) => {
        console.log(`Job ${jobId} is waiting to be processed`);
      });

      queue.on('stalled', (jobId) => {
        console.warn(`Job ${jobId} has stalled and will be retried`);
      });
    }

    return queue;
  } catch (error) {
    console.error('Failed to create sync queue:', {
      error: error.message,
      config: logConfig,
      timestamp: new Date().toISOString(),
    });
    throw error;
  }
}

// Create a function to test Redis connectivity
function testRedisConnection() {
  return new Promise((resolve, reject) => {
    try {
      const testQueue = new Queue('test-connection', {
        connection: redisConfig,
      });

      testQueue.on('ready', () => {
        console.log('Redis connection test successful');
        testQueue.close();
        resolve(true);
      });

      testQueue.on('error', (error) => {
        console.error('Redis connection test failed:', error.message);
        testQueue.close();
        reject(error);
      });

      // Timeout after 10 seconds
      setTimeout(() => {
        testQueue.close();
        reject(new Error('Redis connection test timeout'));
      }, 10000);
    } catch (error) {
      reject(error);
    }
  });
}

module.exports = {
  createSyncQueue,
  testRedisConnection,
};
