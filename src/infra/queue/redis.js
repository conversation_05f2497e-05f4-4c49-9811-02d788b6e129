const isProduction = process.env.NODE_ENV === 'production';

// Redis configuration optimized for production
const redisConfig = {
  host: process.env.REDIS_HOST || (isProduction ? 'redis' : 'localhost'),
  port: parseInt(process.env.REDIS_PORT || 6379),
  password: process.env.REDIS_PASSWORD,

  // Connection settings
  connectTimeout: 10000, // 10 seconds
  lazyConnect: true,
  maxRetriesPerRequest: null, // Required by BullMQ
  retryDelayOnFailover: 100,
  enableReadyCheck: true,
  maxLoadingTimeout: 5000,

  // Production-specific settings
  ...(isProduction && {
    // Connection pool settings for production
    family: 4, // Use IPv4
    keepAlive: true,

    // Retry configuration for production reliability
    retryDelayOnClusterDown: 300,
    retryDelayOnFailover: 100,
    maxRetriesPerRequest: null, // Required by BullMQ

    // Connection timeout settings
    connectTimeout: 10000,
    // commandTimeout removed - BullMQ needs long-polling commands

    // Enable connection monitoring
    enableOfflineQueue: false,

    // Production logging
    showFriendlyErrorStack: false,
  }),

  // Development-specific settings
  ...(!isProduction && {
    // More verbose logging in development
    showFriendlyErrorStack: true,
    enableOfflineQueue: true,
  }),

  // TLS configuration (disabled for now, ready for future implementation)
  tls: false,
  // TODO: Enable TLS for production when certificates are available
  // tls: isProduction ? {
  //   // TLS options for production
  //   rejectUnauthorized: false, // Set to true when using proper certificates
  //   requestCert: true,
  //   // cert: fs.readFileSync('/path/to/redis-client.crt'),
  //   // key: fs.readFileSync('/path/to/redis-client.key'),
  //   // ca: [fs.readFileSync('/path/to/redis-ca.crt')]
  // } : false,
};

// Log Redis configuration (without password) for debugging
if (process.env.LOG_LEVEL === 'debug' || !isProduction) {
  console.log('Redis Configuration:', {
    host: redisConfig.host,
    port: redisConfig.port,
    hasPassword: !!redisConfig.password,
    isProduction,
    connectTimeout: redisConfig.connectTimeout,
    maxRetriesPerRequest: redisConfig.maxRetriesPerRequest,
  });
}

module.exports = redisConfig;
