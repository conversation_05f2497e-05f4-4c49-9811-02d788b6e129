require('dotenv').config();
const path = require('path');
const logStream = require('./logStream');

const serializers = {
  req: (req) => {
    return {
      method: req.method,
      url: req.url,
      token: req.headers.authorization,
      query: req.query,
    };
  },
};

let logSettings = {
  transport: {
    targets: [
      {
        target: 'pino-pretty',
        level: 'info',
        options: {
          colorize: true,
        },
      },
    ],
  },
  serializers,
};

if (process.env.NODE_ENV !== 'local') {
  console.log('Setting up file logging for production environment');
  logSettings = {
    level: process.env.LOG_LEVEL || 'info',
    stream: logStream({
      baseFileName: 'app.log',
      size: process.env.LOG_MAX_SIZE || '10MB',
      interval: '1d'
    }),
    serializers,
  };
}

module.exports = logSettings;
