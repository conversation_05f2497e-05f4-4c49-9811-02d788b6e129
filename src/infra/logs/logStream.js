const path = require('path');
const rfs = require('rotating-file-stream');
const fs = require('fs');

const logStream = (options) => {
  const { size, interval, baseFileName } = options;

  // Ensure the logs directory exists
  const logDir = path.join(path.resolve(), 'logs');
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
  }

  console.log(`Creating log stream for ${baseFileName} in ${logDir}`);

  return rfs.createStream(baseFileName, {
    size: size || '10MB',
    interval: interval || '1d',
    path: logDir,
    compress: 'gzip', // Enable compression for rotated logs
  });
};

module.exports = logStream;
