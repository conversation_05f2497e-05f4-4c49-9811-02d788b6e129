const BaseRepository = require('./baseRepository');

class RequisitionApproverRepository extends BaseRepository {
  constructor({ db }) {
    super(db.requisitionApproverModel);
    this.db = db;
    this.Sequelize = db.Sequelize;
  }

  async bulkCreate(payload, transaction) {
    return await this.tableName.bulkCreate(payload, transaction);
  }

  async getAllRequisitionByUserId(request) {
    const {
      where = {},
      include = [],
      attributes = null,
      group = [],
      order = [['createdAt', 'DESC']],
      distinct = false,
      whereRequisition = {},
    } = request;

    const queryOptions = {
      where,
      include,
      attributes,
      paginate: false,
      order,
      distinct,
      group,
      attributes: [
        'id',
        'altApproverId',
        'approverId',
        'level',
        'requisitionId',
      ],
      include: [
        {
          model: this.db.requisitionModel,
          where: whereRequisition,
          attributes: [],
          as: 'requisition',
        },
        {
          model: this.db.userModel,
          attributes: ['id', 'firstName', 'lastName', 'username'],
          as: 'altApprover',
          include: [
            { model: this.db.roleModel, attributes: ['name'], as: 'role' },
          ],
        },
        {
          model: this.db.userModel,
          attributes: ['id', 'firstName', 'lastName', 'username'],
          as: 'approver',
          include: [
            { model: this.db.roleModel, attributes: ['name'], as: 'role' },
          ],
        },
      ],
    };

    const requisitions = await this.tableName.findAll(queryOptions);

    return requisitions;
  }

  async checkIfAllApprovedForRequisition(requisitionId) {
    const requisitionApprovers = await this.tableName.findAll({
      attributes: ['status'],
      where: { requisitionId },
    });

    return requisitionApprovers.every((item) => item.status === 'approved');
  }
}

module.exports = RequisitionApproverRepository;
