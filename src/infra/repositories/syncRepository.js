const BaseRepository = require('./baseRepository');

class SyncRepository extends BaseRepository {
  constructor({ db }) {
    super(db.syncModel);

    this.Sequelize = db.Sequelize;
  }

  async findByModel(model = '') {
    return await this.findOne({
      where: { model },
    });
  }

  async updateLastSynced(model) {
    const lastSyncedAt = new Date();

    await this.upsert({
      model,
      lastSyncedAt,
    });

    return lastSyncedAt;
  }
}

module.exports = SyncRepository;
