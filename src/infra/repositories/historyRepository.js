const BaseRepository = require('./baseRepository');

class HistoryRepository extends BaseRepository {
  constructor({ db }) {
    super(db.historyModel);
    this.db = db;
    this.Sequelize = db.Sequelize;
  }

  async getAllItemsHistory(payload) {
    const {
      limit = 10,
      filterBy = {},
      page = 1,
      order = [['updatedAt', 'DESC']],
      type,
      id,
      paginate = true,
    } = payload;

    let whereConditions = [];
    let replacements = {};

    // Base conditions
    if (id) {
      whereConditions.push('h.item_id = :itemId');
      replacements.itemId = id;
    }

    // Type filter - include both base type and tom variant
    if (type) {
      if (type === 'ofm' || type === 'ofm-tom') {
        whereConditions.push("h.type IN ('ofm', 'ofm-tom')");
      } else if (type === 'non-ofm' || type === 'non-ofm-tom') {
        whereConditions.push("h.type IN ('non-ofm', 'non-ofm-tom')");
      }
    }

    // RS Number filter
    if (filterBy?.rsNumber) {
      const searchTerm = filterBy.rsNumber.toUpperCase();
      whereConditions.push(`
        CONCAT('RS-', 
          CASE WHEN LENGTH(CAST(c.code AS VARCHAR)) = 1 
               THEN CONCAT('0', c.code) 
               ELSE CAST(c.code AS VARCHAR) 
          END, 
          h.rs_letter, 
          h.rs_number
        ) ILIKE :rsNumber
      `);
      replacements.rsNumber = `%${searchTerm}%`;
    }

    // Build ORDER BY clause
    let orderClause = 'ORDER BY ';
    const orderMappings = {
      project: 'p.name',
      company: 'c.name',
      department: 'd.name',
      updatedAt: 'h.updated_at',
      createdAt: 'h.created_at',
      dateRequested: 'h.date_requested',
      dateDelivered: 'h.date_delivered',
      quantityRequested: 'h.quantity_requested',
      quantityDelivered: 'h.quantity_delivered',
      itemId: 'h.item_id',
      type: 'h.type',
      price: 'h.price',
      rsNumber: `CONCAT('RS-', 
        CASE WHEN LENGTH(CAST(c.code AS VARCHAR)) = 1 
             THEN CONCAT('0', c.code) 
             ELSE CAST(c.code AS VARCHAR) 
        END, 
        h.rs_letter, 
        h.rs_number
      )`,
    };

    const orderParts = order.map(([field, direction]) => {
      const sqlField = orderMappings[field] || `h.${field}`;
      return `${sqlField} ${direction.toUpperCase()}`;
    });
    orderClause += orderParts.join(', ');

    // Build WHERE clause
    const whereClause =
      whereConditions.length > 0
        ? 'WHERE ' + whereConditions.join(' AND ')
        : '';

    // Pagination
    const offset = (page - 1) * limit;
    replacements.limit = limit;
    replacements.offset = offset;

    const baseQuery = `
      SELECT 
        h.id,
        h.type,
        h.item_id as "itemId",
        h.quantity_requested as "quantityRequested",
        h.price,
        h.quantity_delivered as "quantityDelivered",
        h.created_at as "createdAt",
        h.updated_at as "updatedAt",
        CONCAT('RS-', 
          CASE WHEN LENGTH(CAST(c.code AS VARCHAR)) = 1 
               THEN CONCAT('0', c.code) 
               ELSE CAST(c.code AS VARCHAR) 
          END, 
          h.rs_letter, 
          h.rs_number
        ) as "rsNumber",
        CONCAT(
        CASE WHEN LENGTH(CAST(c.code AS VARCHAR)) = 1 
                THEN CONCAT('0', c.code) 
                ELSE CAST(c.code AS VARCHAR) 
            END, 
            h.rs_letter, 
            h.rs_number
        ) AS "rsNumberRaw",
        TO_CHAR(h.date_requested, 'DD Mon YYYY') as "dateRequested",
        TO_CHAR(h.date_delivered, 'DD Mon YYYY') as "dateDelivered",
        c.id as "companies.id",
        c.name as "companies.name", 
        c.code as "companies.code",
        p.id as "projects.id",
        p.name as "projects.name",
        d.id as "departments.id", 
        d.name as "departments.name",
        i.id as "item.id",
        i.item_cd as "item.itemCd",
        i.itm_des as "item.itmDes",
        i.unit as "item.unit",
        i.acct_cd as "item.acctCd",
        i.gfq as "item.gfq",
        i.trade_code as "item.tradeCode",
        i.remaining_gfq as "item.remainingGfq",
        i.is_steelbars as "item.isSteelbars",
        i.created_at as "item.createdAt",
        i.updated_at as "item.updatedAt"
      FROM histories h
      LEFT JOIN companies c ON h.company_id = c.id
      LEFT JOIN projects p ON h.project_id = p.id  
      LEFT JOIN departments d ON h.department_id = d.id
      LEFT JOIN items i ON h.item_id = i.id
      ${whereClause}
      ${orderClause}
    `;

    if (paginate) {
      // Get total count
      const countQuery = `
        SELECT COUNT(*) as total
        FROM histories h
        LEFT JOIN companies c ON h.company_id = c.id
        LEFT JOIN projects p ON h.project_id = p.id
        LEFT JOIN departments d ON h.department_id = d.id  
        LEFT JOIN items i ON h.item_id = i.id
        ${whereClause}
      `;

      const [countResult] = await this.db.sequelize.query(countQuery, {
        replacements,
        type: this.db.Sequelize.QueryTypes.SELECT,
      });

      // Get paginated data
      const dataQuery = baseQuery + ' LIMIT :limit OFFSET :offset';
      const data = await this.db.sequelize.query(dataQuery, {
        replacements,
        type: this.db.Sequelize.QueryTypes.SELECT,
      });

      return {
        data,
        total: parseInt(countResult.total),
      };
    } else {
      const data = await this.db.sequelize.query(baseQuery, {
        replacements,
        type: this.db.Sequelize.QueryTypes.SELECT,
      });

      return {
        data,
        total: data.length,
      };
    }
  }
}

module.exports = HistoryRepository;
