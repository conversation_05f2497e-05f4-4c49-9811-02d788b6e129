class BaseRepository {
  constructor(model) {
    this.tableName = model;
  }

  async create(payload, options = {}) {
    const result = await this.tableName.create(payload, {
      ...options,
      userId: options.userId,
      payload,
    });

    return result.toJSON();
  }

  async update(where, payload, options = {}) {
    const result = await this.tableName.update(payload, {
      where,
      ...options,
      individualHooks: true, // Required for audit hooks to work
      userId: options.userId,
      payload,
    });

    return result;
  }

  async findOne(payload = {}) {
    const result = await this.tableName.findOne(payload);
    return result?.get({ plain: true });
  }

  async findAll(payload = {}) {
    const {
      where = {},
      include = [],
      attributes = null,
      paranoid = true,
      group = [],
      order = [['createdAt', 'DESC']],
      distinct = true,
      transaction,
      subQuery,
      replacements = [],
    } = payload;

    const paginate = payload.paginate !== 'false' && payload.paginate !== false;
    const queryOptions = {
      where,
      include,
      attributes,
      order,
      distinct,
      paranoid,
      group,
      transaction,
      subQuery,
      replacements,
    };

    if (paginate) {
      const page = parseInt(payload.page) || 1;
      const limit = parseInt(payload.limit) || 10;

      Object.assign(queryOptions, {
        offset: (page - 1) * limit,
        limit,
      });

      const { rows, count } =
        await this.tableName.findAndCountAll(queryOptions);

      return {
        data: rows.map((row) => row.get({ plain: true })),
        total: Array.isArray(count)
          ? count.reduce((sum, item) => sum + Number(item.count), 0)
          : count,
      };
    }

    const results = await this.tableName.findAll(queryOptions);
    return {
      data: results.map((row) => row.get({ plain: true })),
      total: results.length,
    };
  }

  async getById(id, options = {}) {
    const result = await this.tableName.findByPk(id, options);
    return result?.get({ plain: true });
  }

  async destroy(where = {}, options = {}) {
    const result = await this.tableName.destroy({
      where,
      ...options,
      individualHooks: true, // Required for audit hooks to work
      userId: options.userId,
      payload: { where },
    });

    return result;
  }

  async upsert(payload, options = {}) {
    return await this.tableName.upsert(payload, {
      ...options,
      userId: options.userId,
      payload,
    });
  }

  async bulkCreate(payload, options = {}) {
    return await this.tableName.bulkCreate(payload, {
      ...options,
      userId: options.userId,
      payload,
    });
  }

  async count(payload = {}) {
    return await this.tableName.count(payload);
  }
}

module.exports = BaseRepository;
