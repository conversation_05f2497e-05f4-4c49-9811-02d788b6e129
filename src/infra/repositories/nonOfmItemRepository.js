const BaseRepository = require('./baseRepository');

class NonOfmItemRepository extends BaseRepository {
  constructor({ db, clientErrors, fastify }) {
    super(db.nonOfmItemModel);
    this.db = db;
    this.Sequelize = db.Sequelize;
    this.clientErrors = clientErrors;
    this.fastify = fastify;
    this.Op = this.Sequelize.Op;
  }

  async generateAcctCd() {
    const currentYear = new Date().getFullYear().toString();

    const latestCode = await this.tableName.findOne({
      attributes: ['acct_cd'],
      where: {
        acct_cd: {
          [this.Sequelize.Op.like]: `${currentYear}%`,
        },
      },
      order: [['acct_cd', 'DESC']],
      raw: true,
    });

    this.fastify.log.info('Latest code found:', latestCode);

    if (!latestCode) {
      this.fastify.log.info(
        'No existing code found for current year, returning initial code',
      );
      return `${currentYear}AAA000001`;
    }

    const lastCode = latestCode.acct_cd;
    const letters = lastCode.substring(4, 7);
    const numbers = lastCode.substring(7);

    if (parseInt(numbers) < 999999) {
      const nextNumber = (parseInt(numbers) + 1).toString().padStart(6, '0');
      const newCode = `${currentYear}${letters}${nextNumber}`;
      this.fastify.log.info(`Generated new code: ${newCode}`);
      return newCode;
    }

    let nextLetters = '';
    for (let i = letters.length - 1; i >= 0; i--) {
      const currentChar = letters[i];
      if (currentChar !== 'Z') {
        nextLetters =
          letters.substring(0, i) +
          String.fromCharCode(letters.charCodeAt(i) + 1) +
          'A'.repeat(letters.length - i - 1);
        break;
      }
    }

    if (!nextLetters) {
      throw new Error('Maximum account codes reached for the year');
    }

    const newCode = `${currentYear}${nextLetters}000001`;
    this.fastify.log.info(
      `Generated new code with letter increment: ${newCode}`,
    );
    return newCode;
  }

  async validateAndTransformPayload(payload) {
    const acctCd = await this.generateAcctCd();

    if (acctCd.length > 20) {
      throw new Error('Generated acctCd exceeds 20 characters');
    }

    return {
      ...payload,
      acctCd,
    };
  }

  async create(payload, options = {}) {
    const transformedPayload = await this.validateAndTransformPayload(payload);

    const nonOfmItemCreated = await this.tableName.create(transformedPayload, {
      transaction: options.transaction,
    });

    const createdWithAssociations = await this.getNonOfmItemById(
      nonOfmItemCreated.id,
    );
    return createdWithAssociations;
  }

  async getAllNonOfmItems(payload) {
    const {
      search,
      type,
      limit,
      page,
      paginate,
      filterBy,
      whereClause = {},
      includeClause = [],
      order = [['item_name', 'ASC']],
    } = payload;

    const { itemName, itemType, unit } = filterBy;

    if (itemName) {
      whereClause.itemName = { [this.Sequelize.Op.iLike]: `%${itemName}%` };
    }

    if (itemType) {
      whereClause.itemType = { [this.Sequelize.Op.iLike]: `%${itemType}%` };
    }

    if (unit) {
      whereClause.unit = { [this.Sequelize.Op.eq]: `${unit}` };
    }

    // if (search) {
    //   whereClause[this.Sequelize.Op.or] = [
    //     { item_name: { [this.Sequelize.Op.iLike]: `%${search}%` } },
    //   ];
    // }

    // if (type) {
    //   whereClause[this.Sequelize.Op.or] = [
    //     { item_type: { [this.Sequelize.Op.iLike]: `%${type}%` } },
    //   ];
    // }

    const result = await this.findAll({
      limit,
      page,
      order,
      paginate,
      where: whereClause,
      attributes: [
        'id',
        ['item_name', 'itemName'],
        ['item_type', 'itemType'],
        'unit',
        ['acct_cd', 'acctCd'],
        'notes',
        ['created_at', 'createdAt'],
        ['updated_at', 'updatedAt'],
      ],
      raw: true,
      nest: true,
    });

    return result;
  }

  async getNonOfmItemById(id) {
    const nonOfmItem = await this.tableName.findByPk(id, {
      attributes: [
        'id',
        ['item_name', 'itemName'],
        ['item_type', 'itemType'],
        'unit',
        ['acct_cd', 'acctCd'],
        'notes',
        ['created_at', 'createdAt'],
        ['updated_at', 'updatedAt'],
      ],
      raw: true,
      nest: true,
    });

    if (!nonOfmItem) {
      throw this.clientErrors.NOT_FOUND({
        message: `NonOfmItem with id of ${id} not found`,
      });
    }

    return {
      ...nonOfmItem,
    };
  }

  async updateById(id, payload) {
    const {
      acctCd,
      id: payloadId,
      createdAt,
      updatedAt,
      ...updatePayload
    } = payload;

    const updatedNonOfmItem = await this.tableName.update(updatePayload, {
      where: { id },
      returning: true,
    });

    if (!updatedNonOfmItem[0]) {
      throw this.clientErrors.NOT_FOUND({
        message: 'NonOFMItem not found',
      });
    }

    return updatedNonOfmItem[1][0].get({ plain: true });
  }

  async getByName(itemName, options = {}) {
    const result = await this.tableName.findOne({
      where: { itemName: itemName },
      ...options,
    });
    return result?.get({ plain: true });
  }

  async getUniqueUnits() {
    return await this.findAll({
      where: {
        unit: {
          [this.db.Sequelize.Op.and]: [
            { [this.db.Sequelize.Op.ne]: '' },
            { [this.db.Sequelize.Op.not]: null },
          ],
        },
      },
      attributes: [
        [
          this.db.Sequelize.fn('DISTINCT', this.db.Sequelize.col('unit')),
          'unit',
        ],
      ],
      order: [['unit', 'ASC']],
      paginate: false,
    });
  }
}

module.exports = NonOfmItemRepository;
