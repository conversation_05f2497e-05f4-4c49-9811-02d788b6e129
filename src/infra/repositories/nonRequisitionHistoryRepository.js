const BaseRepository = require('./baseRepository');

class NonRequisitionHistoryRepository extends BaseRepository {
  constructor({ db }) {
    super(db.nonRequisitionHistoryModel);
    this.db = db;
  }

  async getAllHistories(payload, options = {}) {
    let whereClause = {};
    const {
      limit,
      page,
      paginate,
      filterBy,
      order = [['createdAt', 'DESC']],
    } = payload;

    const { approver } = filterBy;

    const orderClauses = order?.map(([field, direction]) => {
      if (field === 'approver') {
        return [
          this.db.Sequelize.literal('"approver"."first_name"'),
          direction,
        ];
      }

      return [field, direction];
    });

    if (approver) {
      whereClause[this.db.Sequelize.Op.or] = [
        {
          '$approver.first_name$': {
            [this.db.Sequelize.Op.iLike]: `%${approver}%`,
          },
        },
        {
          '$approver.last_name$': {
            [this.db.Sequelize.Op.iLike]: `%${approver}%`,
          },
        },
      ];
    }

    return await this.findAll({
      limit,
      page,
      order: orderClauses,
      paginate,
      include: [
        {
          association: 'approver',
          as: 'approver',
          attributes: [
            'id',
            ['first_name', 'firstName'],
            ['last_name', 'lastName'],
            [
              this.db.Sequelize.fn(
                'CONCAT',
                this.db.Sequelize.col('first_name'),
                ' ',
                this.db.Sequelize.col('last_name'),
              ),
              'fullName',
            ],
          ],
        },
      ],
      where: {
        ...whereClause,
      },
      ...options,
    });
  }
}
module.exports = NonRequisitionHistoryRepository;
