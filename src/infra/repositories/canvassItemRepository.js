const BaseRepository = require('./baseRepository');

class CanvassItemRepository extends BaseRepository {
  constructor({ db }) {
    super(db.canvassItemModel);
    this.db = db;
  }

  #createItemTypeAssociation(association, itemTypes) {
    let include = [];

    if (association === 'item') {
      include.push({
        required: false,
        association: 'steelbars',
        as: 'steelbars',
      });
    }

    return {
      association,
      as: association,
      required: false,
      include,
      where: {
        [this.db.Sequelize.Op.and]: [
          this.db.Sequelize.where(
            this.db.Sequelize.col('requisitionItem.item_type'),
            {
              [this.db.Sequelize.Op.in]: itemTypes,
            },
          ),
        ],
      },
    };
  }

  #createSupplierAssociation(association, supplierType) {
    return {
      association,
      required: false,
      attributes: ['name'],
      on: {
        [this.db.Sequelize.Op.and]: [
          {
            '$suppliers.supplier_id$': {
              [this.db.Sequelize.Op.eq]: this.db.Sequelize.col(
                `suppliers.${association}.id`,
              ),
            },
          },
          { '$suppliers.supplier_type$': supplierType },
        ],
      },
    };
  }

  async getAllCanvassItems(payload, options = {}) {
    const {
      page,
      limit,
      paginate,
      whereClause = {},
      order = [['createdAt', 'DESC']],
    } = payload;

    return await this.findAll({
      limit,
      page,
      order,
      paginate,
      where: whereClause,
      ...options,
      include: [
        {
          required: false,
          as: 'requisitionItem',
          association: 'requisitionItem',
          include: [
            this.#createItemTypeAssociation('item', ['ofm', 'ofm-tom']),
            this.#createItemTypeAssociation('nonOfmItem', [
              'non-ofm',
              'non-ofm-tom',
            ]),
          ],
          attributes: {
            exclude: ['createdAt', 'updatedAt', 'ofmListId', 'requisitionId'],
          },
        },
        {
          required: false,
          association: 'suppliers',
          as: 'suppliers',
          include: [
            this.#createSupplierAssociation('supplier', 'supplier'),
            this.#createSupplierAssociation('project', 'project'),
            this.#createSupplierAssociation('company', 'company'),
          ],
        },
      ],
    });
  }

  async getCanvassItemById(canvassItemId) {
    const canvassItem = await this.getById(canvassItemId, {
      include: [
        {
          association: 'canvass',
          as: 'canvass',
          attributes: ['requisitionId'],
          include: [
            {
              association: 'requisition',
              as: 'requisition',
              attributes: ['id', 'createdBy', 'assignedTo', 'type'],
            },
          ],
        },
      ],
    });

    return canvassItem;
  }
}

module.exports = CanvassItemRepository;
