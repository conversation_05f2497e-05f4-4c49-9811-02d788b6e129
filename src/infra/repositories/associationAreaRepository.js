const BaseRepository = require('./baseRepository');

class AssociationAreaRepository extends BaseRepository {
  constructor({ db }) {
    super(db.associationAreaModel);
  }

  async getAllAreaNames(code, options = {}) {
    const config = {
      paginate: false,
    };

    if (code) {
      config.where = {
        code,
      };
    }

    const areaNames = await this.findAll({
      ...config,
      ...options,
    });

    return areaNames.data.reduce((acc, area) => {
      acc[area.code] = {
        id: area.id,
        code: area.code,
        name: area.name,
      };
      return acc;
    }, {});
  }

  async getOneAreaName(code, options = {}) {
    return this.findOne({
      where: {
        code,
      },
      ...options,
    });
  }
}

module.exports = AssociationAreaRepository;
