const BaseRepository = require('./baseRepository');

class PurchaseOrderCancelledItemsRepository extends BaseRepository {
  constructor({ db, constants }) {
    super(db.purchaseOrderCancelledItemsModel);
    this.db = db;
    this.constants = constants;
  }

  #createItemTypeAssociation(association, itemTypes) {
    return {
      association,
      as: association,
      required: false,
      where: {
        [this.db.Sequelize.Op.and]: [
          this.db.Sequelize.where(
            this.db.Sequelize.col('requisitionItem.item_type'),
            {
              [this.db.Sequelize.Op.in]: itemTypes,
            },
          ),
        ],
      },
    };
  }

  async getAllCancelledItemsByPOIds(purchaseOrderIds) {
    const { PO_ITEM_STATUS } = this.constants.purchaseOrder;

    const existingPurchaseOrders = await this.findAll({
      where: {
        purchaseOrderId: {
          [this.db.Sequelize.Op.in]: purchaseOrderIds,
        },
        status: PO_ITEM_STATUS.NEW,
      },
      include: [
        {
          association: 'requisitionItem',
          attributes: ['id', 'itemType', 'itemId', 'quantity'],
          include: [
            this.#createItemTypeAssociation('item', ['ofm', 'ofm-tom']),
            this.#createItemTypeAssociation('nonOfmItem', [
              'non-ofm',
              'non-ofm-tom',
            ]),
          ],
        },
      ],
    });

    const uniqueItems = [
      ...new Map(
        existingPurchaseOrders.data
          .map((po) => po.requisitionItem)
          .filter((item) => item)
          .map((item) => [item.itemId, item]),
      ).values(),
    ];

    return uniqueItems;
  }

  async getAllRSPurchaseOrderCancelled(requisitionId) {
    const { PO_ITEM_STATUS } = this.constants.purchaseOrder;

    const purchaseOrders = await this.findAll({
      where: {
        requisitionId: requisitionId,
        status: PO_ITEM_STATUS.NEW,
      },
      attributes: ['purchaseOrderId'],
      group: ['purchaseOrderId', 'created_at'],
    });

    return purchaseOrders?.data?.map((po) => po.purchaseOrderId) || [];
  }

  async batchUpdateStatusByPurchaseOrderIds(purchaseOrderIds, options = {}) {
    const { PO_ITEM_STATUS } = this.constants.purchaseOrder;

    return await this.update(
      {
        purchaseOrderId: {
          [this.db.Sequelize.Op.in]: purchaseOrderIds,
        },
        status: PO_ITEM_STATUS.NEW,
      },
      {
        status: PO_ITEM_STATUS.FOR_APPROVAL,
      },
      options,
    );
  }
}

module.exports = PurchaseOrderCancelledItemsRepository;
