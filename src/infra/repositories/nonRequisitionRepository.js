const BaseRepository = require('./baseRepository');

class NonRequisitionRepository extends BaseRepository {
  constructor({ db, constants }) {
    super(db.nonRequisitionModel);
    this.db = db;
    this.constants = constants;
  }

  #createChargeToAssociation(association, itemTypes) {
    return {
      association,
      as: association,
      required: false,
      attributes: ['id', 'name'],
      where: {
        [this.db.Sequelize.Op.and]: [
          this.db.Sequelize.where(
            this.db.Sequelize.col('non_requisitions.charge_to'),
            {
              [this.db.Sequelize.Op.in]: itemTypes,
            },
          ),
        ],
      },
    };
  }

  async cancelNonRS(id, options = {}) {
    const { NON_RS_STATUS } = this.constants.nonRS;

    return this.update({ id }, { status: NON_RS_STATUS.CANCELLED }, options);
  }

  async getAllNonRs(filters = {}) {
    const { category, userId, filterBy = {}, ...restFilters } = filters;

    const whereClause = {};
    const whereRequestor = {};
    const { nonRsNumber } = filterBy;

    if (nonRsNumber) {
      const searchTerm = nonRsNumber.replace(/^NRS-(?:TMP-)?/i, '');

      whereClause[this.db.Sequelize.Op.or] = [
        this.db.Sequelize.where(
          this.db.Sequelize.literal(
            `CASE WHEN non_requisitions.status != 'draft' THEN CONCAT('NRS-', non_rs_letter, non_rs_number)
            ELSE CONCAT('NRS-TMP-', non_rs_letter, draft_non_rs_number) END`,
          ),
          { [this.db.Sequelize.Op.iLike]: `%${searchTerm}%` },
        ),
      ];
    }

    /**
     * TODO: For refactor if have time (required 2 calls to filter via approver level)
     * charge to is not recognize if we filter via includes and required true
     * it requires separate true to work / this impacts the filtering of approvers
     */
    if (category === 'assigned') {
      const nonRsWithApprovals =
        await this.db.nonRequisitionApproverModel.findAll({
          where: {
            [this.db.Sequelize.Op.or]: [{ userId }, { altApproverId: userId }],
          },
          attributes: ['nonRequisitionId'],
          paginate: false,
          raw: true,
        });

      whereClause.id = {
        [this.db.Sequelize.Op.in]: nonRsWithApprovals.map(
          (item) => item.nonRequisitionId,
        ),
      };
    } else if (category === 'request') {
      whereRequestor.id = userId;
    }

    const nonRsList = await this.findAll({
      ...restFilters,
      where: whereClause,
      include: [
        this.#createChargeToAssociation('chargeToProject', ['project']),
        this.#createChargeToAssociation('chargeToSupplier', ['supplier']),
        this.#createChargeToAssociation('chargeToCompany', [
          'company',
          'association',
        ]),
        {
          association: 'requestor',
          as: 'requestor',
          attributes: [
            'id',
            'first_name',
            'last_name',
            [
              this.db.Sequelize.fn(
                'CONCAT',
                this.db.Sequelize.col('first_name'),
                ' ',
                this.db.Sequelize.col('last_name'),
              ),
              'fullName',
            ],
          ],
          where: whereRequestor,
        },
      ],
    });

    return nonRsList;
  }
}
module.exports = NonRequisitionRepository;
