const BaseRepository = require('./baseRepository');

class RoleRepository extends BaseRepository {
  constructor({ db }) {
    super(db.roleModel);
  }

  async getAdminId() {
    const { dataValues } = await this.tableName.findOne({
      where: {
        name: 'Admin',
      },
    });

    return dataValues.id;
  }

  async getRootId() {
    const { dataValues } = await this.tableName.findOne({
      where: {
        name: 'Root User',
      },
    });

    return dataValues.id;
  }
}

module.exports = RoleRepository;
