const BaseRepository = require('./baseRepository');

class NonRequisitionApproverRepository extends BaseRepository {
  constructor({ db }) {
    super(db.nonRequisitionApproverModel);
    this.db = db;
  }

  getApprovers(id, options = {}) {
    return this.findAll({
      where: {
        nonRequisitionId: id,
      },
      order: [
        ['level', 'ASC'],
        ['isAdhoc', 'ASC'],
      ],
      paginate: false,
      include: [
        {
          association: 'approver',
          as: 'approver',
          attributes: [
            'id',
            ['first_name', 'firstName'],
            ['last_name', 'lastName'],
            [
              this.db.Sequelize.fn(
                'CONCAT',
                this.db.Sequelize.col('first_name'),
                ' ',
                this.db.Sequelize.col('last_name'),
              ),
              'fullName',
            ],
          ],
        },
        {
          association: 'role',
          as: 'role',
          attributes: ['id', 'name'],
        },
      ],
      ...options,
    });
  }
}
module.exports = NonRequisitionApproverRepository;
