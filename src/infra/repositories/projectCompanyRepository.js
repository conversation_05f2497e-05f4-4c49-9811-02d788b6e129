const BaseRepository = require('./baseRepository');

class ProjectCompanyRepository extends BaseRepository {
  constructor({ db }) {
    super(db.projectCompanyModel);
    this.db = db;
    this.Sequelize = db.Sequelize;
  }

  async findByProjectAndCompany(projectId, companyId) {
    return await this.findOne({
      where: { projectId, companyId },
    });
  }

  async removeTagging(projectId, companyId) {
    return await this.tableName.destroy({
      where: { projectId, companyId },
    });
  }

  async removeAllCompanyTags(companyId) {
    return await this.tableName.destroy({
      where: { companyId },
    });
  }

  async bulkCreateTags(entries) {
    return await this.tableName.bulkCreate(entries);
  }

  async getTaggedProjectIds(excludeCompanyId = null) {
    const whereClause = excludeCompanyId
      ? { companyId: { [this.Sequelize.Op.ne]: excludeCompanyId } }
      : {};

    const taggedProjects = await this.findAll({
      where: whereClause,
      attributes: ['projectId'],
    });

    return taggedProjects.data.length > 0
      ? taggedProjects.data.map((tag) => tag.projectId)
      : [];
  }
}

module.exports = ProjectCompanyRepository;
