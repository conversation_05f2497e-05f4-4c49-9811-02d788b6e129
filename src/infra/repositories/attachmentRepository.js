const { attachment } = require('../../domain/entities');
const BaseRepository = require('./baseRepository');
const fs = require('fs');
const path = require('path');
class AttachmentRepository extends BaseRepository {
  constructor({ db }) {
    super(db.attachmentModel);
    this.db = db;
    this.Sequelize = db.Sequelize;
  }

  async getAttachmentByModelId({ id, model }) {
    const whereClause = {
      modelId: id,
      model,
    };

    return this.findAll({ where: whereClause, paginate: false });
  }

  async getAllAttachments({ whereClause }) {
    return await this.findAll({
      where: whereClause,
      paginate: false,
      include: [
        {
          as: 'user',
          association: 'userAttachment',
          attributes: ['id', 'firstName', 'lastName'],
        },
        {
          as: 'attachmentBadge',
          association: 'badges',
        },
      ],
    });
  }

  async assignAttachmentsToModelId({
    model,
    modelId,
    userId,
    attachmentIds = [],
    transaction = null,
  }) {
    return await this.update(
      {
        id: {
          [this.Sequelize.Op.in]: attachmentIds,
        },
        model,
      },
      {
        modelId,
      },
      {
        userId,
        transaction,
      },
    );
  }

  async removeAttachments(
    attachmentIds,
    model,
    modelId,
    userId,
    transaction = null,
  ) {
    const attachments = await this.findAll({
      where: {
        id: {
          [this.Sequelize.Op.in]: attachmentIds.map(Number),
        },
        model,
        modelId,
      },
    });

    const results = await this.destroy(
      {
        id: {
          [this.Sequelize.Op.in]: attachmentIds.map(Number),
        },
        model,
        modelId,
      },
      {
        userId,
        transaction,
      },
    );

    attachments?.data?.forEach((attachment) => {
      const filePathToDelete = path.resolve('upload', `.${attachment.path}`);
      fs.unlinkSync(filePathToDelete);
    });

    return results;
  }
}

module.exports = AttachmentRepository;
