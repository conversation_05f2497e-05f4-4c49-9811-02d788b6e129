const BaseRepository = require('./baseRepository');

class DepartmentAssociationApprovalRepository extends BaseRepository {
  constructor({ db }) {
    super(db.departmentAssociationApprovalModel);
  }

  async getAllAssociationApprovals(payload) {
    const { where, limit, page, paginate } = payload;
    const allAssociationApprovals = await this.findAll({
      where,
      limit,
      page,
      paginate,
      order: [
        ['approvalTypeCode', 'ASC'],
        ['level', 'ASC'],
      ],
      include: [
        {
          association: 'approver',
          attributes: ['id', 'firstName', 'lastName'],
          include: [
            {
              association: 'role',
              attributes: ['id', 'name'],
            },
            // TODO: Include alternative approver here once ready
          ],
        },
      ],
      attributes: {
        exclude: ['approverId'],
      },
    });

    return allAssociationApprovals;
  }
}

module.exports = DepartmentAssociationApprovalRepository;
