class MockPurchaseOrderRepository {
  constructor({ clientErrors }) {
    this.clientErrors = clientErrors;
    this.data = [
      {
        id: 1,
        poNumber: 'PO-2024-001',
        supplier: {
          id: 1,
          name: 'Supplier 1',
        },
        items: [
          {
            id: 1,
            accountCode: '2018C650015308',
            itemDes: 'BI PIPES SCH 40 (1") 25 MM X 6M SUPREME',
            qtyPurchased: 10,
            unit: 'PCS.',
          },
          {
            id: 2,
            accountCode: '2018C650011050',
            itemDes: 'CEMENT IN BAG PORTLAND',
            qtyPurchased: 100,
            unit: 'BAGS',
          },
          {
            id: 3,
            accountCode: '2018C650013131',
            itemDes: '5.5MM2 BARE COPPER WIRE',
            qtyPurchased: 50,
            unit: 'MTS',
          },
          {
            id: 4,
            accountCode: '2018C650011753',
            itemDes: '3D LIGHTWEIGHT PANEL JOINT MESH 3D (0.4 N X 1.2M)',
            qtyPurchased: 30,
            unit: 'PCS',
          },
        ],
      },
      {
        id: 2,
        poNumber: 'PO-2024-002',
        supplier: {
          id: 2,
          name: 'Supplier 2',
        },
        items: [
          {
            id: 5,
            accountCode: '2018C650014478',
            itemDes: 'C.I. PIPE 8" SHXH ASA',
            qtyPurchased: 15,
            unit: 'PCS.',
          },
          {
            id: 6,
            accountCode: '2018C650011201',
            itemDes: 'CONCRETE READY MIXED 3000 PSI STANDARD',
            qtyPurchased: 85,
            unit: 'BAGS',
          },
          {
            id: 7,
            accountCode: '2018C650014484',
            itemDes: 'C.I. PIPE 6" DHXH ASA',
            qtyPurchased: 20,
            unit: 'PCS.',
          },
        ],
      },
    ];
  }
  getAll() {
    return this.data.map(({ id, poNumber }) => ({ id, poNumber }));
  }

  getById(id) {
    const item = this.data.find((x) => x.id === Number(id));

    if (!item) {
      throw this.clientErrors.NOT_FOUND({
        message: `Purchase order with id of ${id} not found.`,
      });
    }

    return item;
  }
}

module.exports = MockPurchaseOrderRepository;
