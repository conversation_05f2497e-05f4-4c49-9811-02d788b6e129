const BaseRepository = require('./baseRepository');

class RequisitionPaymentHistoryRepository extends BaseRepository {
  constructor({ db, clientErrors, utils }) {
    super(db.requisitionPaymentHistoryModel);
    this.clientErrors = clientErrors;
    this.Sequelize = db.Sequelize;
    this.db = db;
    this.utils = utils;
    this.requisitionItemListModel = db.requisitionItemListModel;
  }

  async getRequisitionHistory(payload) {
    const { requisitionId, sort, ...queries } = payload;
    return await this.findAll({
      where: { requisitionId },
      ...queries,
      order: sort,
    });
  }
}

module.exports = RequisitionPaymentHistoryRepository;
