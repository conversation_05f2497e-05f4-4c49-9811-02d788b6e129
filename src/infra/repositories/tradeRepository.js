const BaseRepository = require('./baseRepository');

class TradeRepository extends BaseRepository {
  constructor({ db }) {
    super(db.tradeModel);
  }

  async getAllTrades(payload) {
    const {
      search,
      whereClause = {},
      order = [['trade_name', 'ASC']],
    } = payload;

    if (search) {
      whereClause = {
        [this.Sequelize.Op.or]: [
          { trade_name: { [this.Sequelize.Op.iLike]: `%${search}%` } },
          { trade_code: search },
        ],
      };
    }

    return await this.findAll({
      where: whereClause,
      order,
    });
  }
}

module.exports = TradeRepository;
