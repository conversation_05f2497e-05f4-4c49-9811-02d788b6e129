const BaseRepository = require('./baseRepository');

class CanvassItemSupplierRepository extends BaseRepository {
  constructor({ db, canvassApproverRepository }) {
    super(db.canvassItemSupplierModel);
    this.db = db;
    this.canvassApproverRepository = canvassApproverRepository;
  }

  async getSelectedSupplierByCanvassId(canvassItemIds = []) {
    const selectedSuppliers = await this.findAll({
      where: {
        isSelected: true,
        canvassItemId: {
          [this.db.Sequelize.Op.in]: canvassItemIds,
        },
      },
      include: [
        {
          association: 'canvassItem',
          as: 'canvassItem',
        },
      ],
      paginate: false,
    });

    return selectedSuppliers;
  }

  async getCanvassIdsBySupplier(supplier) {
    const where = { supplierType: 'supplier' };
    if (supplier.id) {
      where.supplierId = supplier.id;
    } else {
      where.supplierName = supplier.name;
    }
    const canvassWithSuppliers = await this.findAll({
      where,
      attributes: ['id'],
      include: [
        {
          model: this.db.canvassItemModel,
          as: 'canvassItem',
          attributes: ['canvassRequisitionId'],
          required: true,
        },
      ],
    });

    const allCanvassRequisitionIds = canvassWithSuppliers.data
      .map((canvassRecord) => {
        return canvassRecord.canvassItem
          ? canvassRecord.canvassItem.canvassRequisitionId
          : null;
      })
      .filter((id) => id !== null);

    const distinctCanvassId = [...new Set(allCanvassRequisitionIds)];

    await this.canvassApproverRepository.update(
      { canvassRequisitionId: distinctCanvassId },
      { status: 'pending' },
    );
  }
}

module.exports = CanvassItemSupplierRepository;
