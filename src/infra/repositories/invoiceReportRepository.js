const BaseRepository = require('./baseRepository');

class InvoiceReportRepository extends BaseRepository {
  constructor({ db }) {
    super(db.invoiceReportModel);
  }

  async createInvoiceReport(data, options = {}) {
    const {
      deliveryReceiptIds,
      supplierInvoice,
      attachmentIds,
      ...invoiceReportData
    } = data;

    invoiceReportData.supplierInvoiceNo = supplierInvoice.number;
    invoiceReportData.invoiceAmount = supplierInvoice.amount;
    invoiceReportData.issuedInvoiceDate = supplierInvoice.invoiceDate;

    return await this.create(invoiceReportData, options);
  }

  async updateInvoiceReport(id, data, options = {}) {
    const {
      deliveryReceiptIds,
      supplierInvoice,
      attachmentIds,
      ...invoiceReportData
    } = data;

    invoiceReportData.supplierInvoiceNo = supplierInvoice.number;
    invoiceReportData.invoiceAmount = supplierInvoice.amount;
    invoiceReportData.issuedInvoiceDate = supplierInvoice.invoiceDate;

    return await this.update({ id }, invoiceReportData, options);
  }
}

module.exports = InvoiceReportRepository;
