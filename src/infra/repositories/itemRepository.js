const BaseRepository = require('./baseRepository');
const historyRepository = require('./historyRepository');

class ItemRepository extends BaseRepository {
  constructor({ historyRepository, db, clientErrors, fastify, constants }) {
    super(db.itemModel);
    this.constants = constants;
    this.historyRepository = historyRepository;
    this.Sequelize = db.Sequelize;
    this.clientErrors = clientErrors;
    this.fastify = fastify;
    this.db = db;
  }

  async bulkCreate(payload) {
    const payloadIds = payload.map((item) => item.acctCd);

    const existingRecords = await this.tableName.findAll({
      where: {
        itemCd: payloadIds,
      },
      attributes: ['itemCd'],
    });

    const existingItemCds = new Set(
      existingRecords.map((record) => record.itemCd),
    );

    const result = await this.tableName.bulkCreate(payload, {
      updateOnDuplicate: ['itemCd', 'itmDes', 'acctCd', 'gfq', 'remainingGfq'],
      returning: true,
    });

    const newlyCreatedRecords = result.filter(
      (record) => !existingItemCds.has(record.itemCd),
    );

    return newlyCreatedRecords;
  }

  async getAllItems(payload) {
    const {
      limit,
      page,
      order = [['itmDes', 'ASC']],
      whereClause,
      paginate,
    } = payload;

    return await this.findAll({
      limit,
      page,
      order,
      paginate,
      where: whereClause,
      include: [
        {
          model: this.db.tradeModel,
          as: 'trade',
          required: true,
          attributes: [
            ['trade_code', 'code'],
            ['trade_name', 'tradeName'],
          ],
        },
        {
          model: this.tableName.sequelize.model('steelbars'),
          as: 'steelbars',
          required: false,
        },
        {
          model: this.db.ofmListItemModel,
          as: 'listItems',
          required: false,
          include: [
            {
              model: this.db.ofmItemListModel,
              as: 'ofmList',
              required: false,
              include: [
                {
                  model: this.db.companyModel,
                  as: 'company',
                  required: false,
                  attributes: ['id', 'name'],
                },
                {
                  model: this.db.projectModel,
                  as: 'project',
                  required: false,
                  attributes: ['id', 'name'],
                },
              ],
            },
          ],
        },
      ],
    });
  }

  async getItemDetails(id) {
    try {
      const item = await this.tableName.findByPk(id, {
        include: [
          {
            model: this.tableName.sequelize.model('trades'),
            as: 'trade',
            attributes: [
              ['trade_code', 'code'],
              ['trade_name', 'name'],
            ],
            required: false,
          },
          {
            model: this.tableName.sequelize.model('steelbars'),
            as: 'steelbars',
            required: false,
          },
        ],
        raw: true,
        nest: true,
      });

      if (!item) {
        throw this.clientErrors.NOT_FOUND({
          message: `Item with id of ${id} not found`,
        });
      }

      return {
        ...item,
        trade: item.trade?.code ? item.trade : null,
        steelbars: item.steelbars || null,
      };
    } catch (error) {
      console.error('Error in getItemDetails:', error);
      throw error;
    }
  }

  async getAllOfmItemLists(payload) {
    let whereClause = {};
    const { search, limit, page, order = [['listName', 'ASC']] } = payload;

    if (search) {
      whereClause.listName = { [this.Sequelize.Op.iLike]: `%${search}%` };
    }

    return await this.findAll({
      limit,
      page,
      order,
      where: whereClause,
      include: [{ model: this.db.projectModel }, { model: this.db.tradeModel }],
    });
  }

  async updateItem(item) {
    return await this.update(
      { id: item.id },
      { remainingGfq: item.gfq_balance },
    );
  }

  async getAllHistory(request) {
    const { id } = request.params;
    const {
      search,
      limit,
      page,
      paginate,
      sortBy = [['createdAt', 'DESC']],
    } = request.query;

    let whereClause = {};

    if (search) {
      whereClause = {
        rsNumber: { [this.Sequelize.Op.iLike]: `%${search}%` },
      };
    } else {
      whereClause = {
        itemId: id,
      };
    }

    const result = await this.historyRepository.findAll({
      limit,
      page,
      order: sortBy,
      paginate,
      where: whereClause,
    });

    return result;
  }

  async upsertItem(payload) {
    this.fastify.log.info(`Creating/Updating an Item...`);
    let createdItem;

    const existingRecord = await this.tableName.findOne({
      where: { itemCd: payload.acctCd },
    });

    if (existingRecord) {
      payload.unit = existingRecord.unit;
    }

    const [record] = await this.tableName.upsert(
      { ...payload, itemCd: payload.acctCd },
      {
        fields: ['itemCd', 'itmDes', 'unit', 'acctCd', 'gfq', 'remainingGfq'],
      },
    );

    if (
      record.dataValues.createdAt.getTime() ===
      record.dataValues.updatedAt.getTime()
    ) {
      createdItem = record.dataValues;
    }

    this.fastify.log.info(`Successfully Created/Updated an Item`);

    return { ...record.dataValues, created: createdItem };
  }

  async getUniqueUnits() {
    return await this.findAll({
      where: {
        unit: {
          [this.db.Sequelize.Op.and]: [
            { [this.db.Sequelize.Op.ne]: '' },
            { [this.db.Sequelize.Op.not]: null },
          ],
        },
      },
      attributes: [
        [
          this.db.Sequelize.fn('DISTINCT', this.db.Sequelize.col('unit')),
          'unit',
        ],
      ],
      order: [['unit', 'ASC']],
      paginate: false,
    });
  }

  async bulkUpdateOfmItems(itemsArray) {
    this.fastify.log.info(`Bulk updating ${itemsArray.length} items...`);

    const results = [];
    const transaction = await this.db.sequelize.transaction();

    try {
      for (const item of itemsArray) {
        const { id, ...updateData } = item;

        const updateResult = await this.update({ id }, updateData, {
          transaction,
        });

        const updatedItem = await this.tableName.findByPk(id, {
          transaction,
          raw: true,
        });

        results.push(updatedItem);
      }

      await transaction.commit();
      this.fastify.log.info(
        `Successfully bulk updated ${results.length} items`,
      );
      return results;
    } catch (error) {
      await transaction.rollback();
      this.fastify.log.error(`Error in bulkUpdateOfmItems: ${error.message}`);
      throw error;
    }
  }

  async getItemPurchaseHistory(params) {
    const { id, order, page, limit, type } = params;

    // Default sorting: date purchased (latest first)
    const orderClauses =
      order && order.length > 0 ? order : [['datePurchased', 'DESC']];

    try {
      const parsedPage = parseInt(page) || 1;
      const parsedLimit = parseInt(limit) || 10;
      const offset = (parsedPage - 1) * parsedLimit;

      // Build the ORDER BY clause
      let orderByClause = '';
      const orderMappings = {
        rsNumber: 'r.rs_number',
        supplierName: 'supplier_name',
        pricePerUnit: 'unit_price',
        quantityPurchased: 'poi.quantity_purchased',
        datePurchased: 'po.created_at',
      };

      // Map the order clauses to SQL
      const orderSql = orderClauses
        .map(([field, direction]) => {
          const sqlField = orderMappings[field] || field;
          return `${sqlField} ${direction}`;
        })
        .join(', ');

      // Add secondary sort by date if not already included
      const hasDateSort = orderClauses.some(
        ([field]) => field === 'datePurchased',
      );
      const finalOrderSql = hasDateSort
        ? orderSql
        : `${orderSql}, po.created_at DESC`;

      orderByClause = `ORDER BY ${finalOrderSql}`;

      // Build the WHERE clause based on item type
      let whereClause = '';
      const replacements = { itemId: id };

      if (type === 'ofm') {
        whereClause =
          "AND ril.item_id = :itemId AND ril.item_type IN ('ofm', 'ofm-tom')";
      } else if (type === 'non-ofm') {
        whereClause =
          "AND ril.item_id = :itemId AND ril.item_type IN ('non-ofm', 'non-ofm-tom')";
      }

      // Add status filter for purchase orders
      const { PO_STATUS } = this.constants.purchaseOrder;
      whereClause += ` AND po.status IN ('${PO_STATUS.FOR_SENDING}', '${PO_STATUS.FOR_DELIVERY}', '${PO_STATUS.CLOSED_PO}')`;

      // Build the complete SQL query
      const countQuery = `
        SELECT COUNT(DISTINCT poi.id) as total
        FROM purchase_order_items poi
        JOIN purchase_orders po ON poi.purchase_order_id = po.id
        JOIN requisition_item_lists ril ON poi.requisition_item_list_id = ril.id
        JOIN requisitions r ON po.requisition_id = r.id
        LEFT JOIN suppliers s ON po.supplier_id = s.id AND po.supplier_type = 'supplier'
        LEFT JOIN projects p ON po.supplier_id = p.id AND po.supplier_type = 'project'
        LEFT JOIN companies c ON po.supplier_id = c.id AND po.supplier_type = 'company'
        LEFT JOIN canvass_item_suppliers cis ON poi.canvass_item_supplier_id = cis.id
        WHERE 1=1 ${whereClause}
      `;

      const dataQuery = `
        SELECT 
          poi.id,
          poi.quantity_purchased,
          po.id as purchase_order_id,
          po.created_at as date_purchased,
          r.id as requisition_id,
          r.rs_number,
          r.rs_letter,
          r.company_code,
          s.id as supplier_id,
          COALESCE(s.name, p.name, c.name) as supplier_name,
          COALESCE(cis.unit_price, 0) as unit_price,
          cis.discount_value,
          cis.discount_type
        FROM purchase_order_items poi
        JOIN purchase_orders po ON poi.purchase_order_id = po.id
        JOIN requisition_item_lists ril ON poi.requisition_item_list_id = ril.id
        JOIN requisitions r ON po.requisition_id = r.id
        LEFT JOIN suppliers s ON po.supplier_id = s.id AND po.supplier_type = 'supplier'
        LEFT JOIN projects p ON po.supplier_id = p.id AND po.supplier_type = 'project'
        LEFT JOIN companies c ON po.supplier_id = c.id AND po.supplier_type = 'company'
        LEFT JOIN canvass_item_suppliers cis ON poi.canvass_item_supplier_id = cis.id
        WHERE 1=1 ${whereClause}
        ${orderByClause}
        LIMIT :limit OFFSET :offset
      `;

      // Add pagination parameters
      replacements.limit = parsedLimit;
      replacements.offset = offset;

      // Execute the count query
      const [countResult] = await this.db.sequelize.query(countQuery, {
        replacements,
        type: this.db.Sequelize.QueryTypes.SELECT,
        raw: true,
      });

      const count = parseInt(countResult.total);

      // Execute the data query
      const rows = await this.db.sequelize.query(dataQuery, {
        replacements,
        type: this.db.Sequelize.QueryTypes.SELECT,
        raw: true,
      });

      // Format the response
      const formattedData = rows.map((row) => {
        let pricePerUnit = parseFloat(row.unit_price) || 0;

        // Apply discount based on discount type
        if (row.discount_value) {
          if (row.discount_type === 'percent') {
            pricePerUnit = pricePerUnit * (1 - row.discount_value / 100);
          } else if (row.discount_type === 'fixed') {
            pricePerUnit = pricePerUnit - row.discount_value;
          }
        }

        return {
          id: row.id,
          requisitionId: row.requisition_id,
          rsNumber: `RS-${row.company_code}${row.rs_letter}${row.rs_number}`,
          supplierName: row.supplier_name,
          pricePerUnit: parseFloat(pricePerUnit.toFixed(2)),
          quantityPurchased: row.quantity_purchased,
          datePurchased: row.date_purchased,
        };
      });

      return {
        rows: formattedData,
        count: count,
        page: parsedPage,
        limit: parsedLimit,
        totalPages: Math.ceil(count / parsedLimit),
      };
    } catch (error) {
      console.error('Error in getItemPurchaseHistory:', error);
      throw error;
    }
  }
}

module.exports = ItemRepository;
