const BaseRepository = require('./baseRepository');

class NonRequisitionItemRepository extends BaseRepository {
  constructor({ db }) {
    super(db.nonRequisitionItemModel);
    this.db = db;
  }

  async getAllItemListByNonRsId(id, options = {}) {
    return this.findAll({
      where: {
        nonRequisitionId: id,
      },
      order: [['name', 'ASC']],
      ...options,
    });
  }

  async getNonRSItemUnits() {
    return await this.findAll({
      where: {
        unit: {
          [this.db.Sequelize.Op.and]: [
            { [this.db.Sequelize.Op.ne]: '' },
            { [this.db.Sequelize.Op.not]: null },
          ],
        },
      },
      attributes: [
        [
          this.db.Sequelize.fn('DISTINCT', this.db.Sequelize.col('unit')),
          'unit',
        ],
      ],
      order: [['unit', 'ASC']],
      paginate: false,
    });
  }
}
module.exports = NonRequisitionItemRepository;
