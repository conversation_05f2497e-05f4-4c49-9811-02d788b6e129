const BaseRepository = require('./baseRepository');

class RequisitionItemListRepository extends BaseRepository {
  constructor({ db, fastify, clientErrors }) {
    super(db.requisitionItemListModel);
    this.db = db;
    this.Sequelize = db.Sequelize;
    this.fastify = fastify;
    this.clientErrors = clientErrors;
  }

  async destroyById(id) {
    return await this.tableName.destroy({
      where: { id },
    });
  }

  async destroyByRequisitionId(requisitionId, transaction = null) {
    return await this.tableName.destroy({
      where: { requisitionId },
      ...(transaction && { transaction }),
    });
  }

  async bulkCreate(data) {
    return await this.tableName.bulkCreate(data);
  }

  async getItemDetails(id) {
    const result = await this.tableName.findOne({
      where: { id },
    });

    if (result) {
      if (result.itemType === 'ofm' || result.itemType === 'ofm-tom') {
        const itemDetails = await this.db.itemModel.findOne({
          attributes: ['itmDes'],
          where: { id: result.itemId },
        });
        return itemDetails;
      } else {
        const itemDetails = await this.db.nonOfmItemModel.findOne({
          attributes: ['itemName'],
          where: { id: result.itemId },
        });
        return itemDetails;
      }
    }

    return null;
  }

  async getAllRequisitionItems(payload) {
    let whereClause = {};
    const {
      requisitionId,
      type,
      search,
      limit,
      page,
      steelbars,
      paginate,
      order = [['createdAt', 'DESC']],
    } = payload;

    let include;

    const isOFM = ['ofm', 'ofm-tom'].includes(type);
    const searchField = isOFM ? 'itmDes' : 'itemName';
    const modelConfig = {
      model: isOFM ? this.db.itemModel : this.db.nonOfmItemModel,
      as: isOFM ? 'item' : 'nonOfmItem',
    };

    if (search && steelbars) {
      whereClause[searchField] = { [this.Sequelize.Op.iLike]: `%${search}%` };
      whereClause.isSteelbars = true;
    }

    if (search && !steelbars && isOFM) {
      whereClause[searchField] = { [this.Sequelize.Op.iLike]: `%${search}%` };
      whereClause.isSteelbars = false;
    }

    if (search && !isOFM) {
      whereClause[searchField] = { [this.Sequelize.Op.iLike]: `%${search}%` };
    }

    include = [
      {
        ...modelConfig,
        where: whereClause,
      },
    ];

    return await this.findAll({
      limit,
      page,
      order,
      where: { requisitionId },
      include,
      paginate,
    });
  }

  async findRSItemByIds(payload = {}, options = {}) {
    const { requisitionId, rsItemIds = [] } = payload;
    const whereClause = {
      id: {
        [this.Sequelize.Op.in]: rsItemIds,
      },
    };

    if (requisitionId) {
      whereClause.requisitionId = requisitionId;
    }

    return this.findAll({
      where: whereClause,
      ...options,
    });
  }
  async getAllItemsByReqId(id) {
    this.fastify.log.info(
      `Getting All Requisitions Associated with Item ID of ${id}`,
    );

    const { data } = await this.findAll({
      pagination: false,
      where: {
        itemId: id,
        itemType: 'ofm',
      },
      attributes: ['id', 'itemId', 'itemType', 'ofmListId', 'created_at'],
      include: [
        {
          model: this.db.requisitionModel,
          attributes: [
            'id',
            'status',
            'created_by',
            'assigned_to',
            'created_at',
          ],
          as: 'requisition',
          where: {
            status: {
              [this.Sequelize.Op.notIn]: ['cancelled', 'closed'],
            },
          },
          include: [
            {
              model: this.db.requisitionApproverModel,
              as: 'requisitionApprovers',
              attributes: ['approverId'],
            },
            {
              model: this.db.canvassRequisitionModel,
              as: 'canvassRequisitions',
              attributes: ['id', 'status'],
            },
            {
              model: this.db.purchaseOrderModel,
              as: 'purchaseOrders',
              attributes: ['id', 'status'],
            },
            {
              model: this.db.deliveryReceiptModel,
              as: 'deliveryReceipt',
              attributes: ['id', 'latestDeliveryStatus'],
            },
            {
              model: this.db.rsPaymentRequestModel,
              as: 'rsPaymentRequest',
              attributes: ['status', 'id'],
            },
          ],
        },
      ],
    });

    return data;
  }

  async getRequisitionAddItems(requisitionId, queries = {}) {
    return await this.findAll({
      paginate: false,
      where: { requisitionId },
      include: [
        this.#createItemTypeAssociation('item', ['ofm', 'ofm-tom']),
        this.#createItemTypeAssociation('nonOfmItem', [
          'non-ofm',
          'non-ofm-tom',
        ]),
      ],
      ...queries,
    });
  }

  #createItemTypeAssociation(association, itemTypes) {
    return {
      association,
      as: association,
      required: false,
      where: {
        [this.db.Sequelize.Op.and]: [
          this.db.Sequelize.where(
            this.db.Sequelize.col('requisition_item_lists.item_type'),
            {
              [this.db.Sequelize.Op.in]: itemTypes,
            },
          ),
        ],
      },
    };
  }
}

module.exports = RequisitionItemListRepository;
