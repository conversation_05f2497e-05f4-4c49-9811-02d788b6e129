const BaseRepository = require('./baseRepository');

class PurchaseOrderApproverRepository extends BaseRepository {
  constructor({ db }) {
    super(db.purchaseOrderApproverModel);

    this.db = db;
    this.Sequelize = db.Sequelize;
  }

  async getAllPurchaseOrderByUserId(request) {
    const {
      where = {},
      include = [],
      group = [],
      order = [['createdAt', 'DESC']],
      distinct = false,
      whereRequisition = {},
      whereCanvass = {},
      wherePurchaseOrder = {},
    } = request;

    const queryOptions = {
      where,
      include,
      order,
      distinct,
      paginate: false,
      group,
      attributes: ['id', 'level', 'altApproverId', 'userId', 'purchaseOrderId'],
      include: [
        {
          model: this.db.purchaseOrderModel,
          where: wherePurchaseOrder,
          attributes: [],
          as: 'purchaseOrder',
          include: [
            {
              model: this.db.requisitionModel,
              where: whereRequisition,
              attributes: [],
              as: 'requisition',
            },
          ],
        },
        {
          model: this.db.userModel,
          attributes: ['id', 'firstName', 'lastName', 'username'],
          as: 'altApprover',
          include: [
            { model: this.db.roleModel, attributes: ['name'], as: 'role' },
          ],
        },
        {
          model: this.db.userModel,
          attributes: ['id', 'firstName', 'lastName', 'username'],
          as: 'approver',
          include: [
            { model: this.db.roleModel, attributes: ['name'], as: 'role' },
          ],
        },
      ],
    };

    const canvasses = await this.tableName.findAll(queryOptions);

    return canvasses;
  }
}

module.exports = PurchaseOrderApproverRepository;
