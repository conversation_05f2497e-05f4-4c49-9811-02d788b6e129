const BaseRepository = require('./baseRepository');

class DepartmentRepository extends BaseRepository {
  constructor({
    db,
    clientErrors,
    companyRepository,
    userRepository,
    auditLogRepository,
  }) {
    super(db.departmentModel);
    this.Sequelize = db.Sequelize;
    this.clientErrors = clientErrors;
    this.companyRepository = companyRepository;
    this.userRepository = userRepository;
    this.auditLogRepository = auditLogRepository;
  }

  async getAllDepartments(payload) {
    const {
      paginate,
      limit,
      page,
      whereClause,
      order = [['name', 'ASC']],
    } = payload;

    // if (whereClause.name) {
    //   whereClause.name = { [this.Sequelize.Op.iLike]: `%${name}%` };
    // }

    // if (whereClause.code) {
    //   whereClause.code = code;
    // }

    return await this.findAll({
      limit,
      page,
      order,
      paginate,
      where: whereClause,
    });
  }

  async updateById(id, payload, userDetails) {
    const [updateCount, updatedDepartments] = await this.tableName.update(
      payload,
      {
        where: { id },
        returning: true,
      },
    );

    if (!updateCount) {
      throw this.clientErrors.NOT_FOUND({
        message: 'Department not found',
      });
    }

    await this.auditLogRepository.create({
      actionType: 'update',
      module: 'department',
      description: `Department with id of ${id} was updated by ${userDetails.userId}`,
      metadata: {
        userDetails,
        payload,
      },
    });

    return updatedDepartments[0].get({ plain: true });
  }

  async getDepartmentDetails(id) {
    const department = await this.tableName.findByPk(id);

    if (!department) {
      throw this.clientErrors.NOT_FOUND({
        message: `Department with id of ${id} not found`,
      });
    }

    return department.get({ plain: true });
  }

  async syncDepartments(departments, userDetails) {
    const result = await this.tableName.bulkCreate(departments, {
      updateOnDuplicate: ['name', 'code'],
    });

    await this.auditLogRepository.create({
      actionType: 'bulk_insert',
      module: 'department',
      description: 'Synced departments',
      metadata: {
        userDetails,
        payload: departments,
        count: departments.length,
      },
    });

    return result;
  }
}

module.exports = DepartmentRepository;
