const BaseRepository = require('./baseRepository');

class CommentRepository extends BaseRepository {
  constructor({ db }) {
    super(db.commentModel);
  }

  async bulkCreate(payload, transaction) {
    return await this.tableName.bulkCreate(payload, transaction);
  }

  async getComments(payload = {}) {
    const { whereClause = {}, filters = {} } = payload;
    const comments = await this.findAll({
      where: whereClause,
      ...filters,
      include: [
        {
          association: 'userComment',
          as: 'userComment',
          attributes: ['id', 'firstName', 'lastName'],
          include: [
            {
              association: 'role',
              as: 'role',
              attributes: ['name'],
            },
          ],
        },
      ],
    });

    return comments;
  }
}

module.exports = CommentRepository;
