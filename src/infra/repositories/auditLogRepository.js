const BaseRepository = require('./baseRepository');

class AuditLogRepository extends BaseRepository {
  constructor({ db, clientErrors }) {
    super(db.auditLogModel);
    this.Sequelize = db.Sequelize;
    this.clientErrors = clientErrors;
  }

  async getAllAuditLogs(payload) {
    const {
      actionType,
      module,
      startDate,
      endDate,
      paginate,
      limit,
      page,
      order = [['created_at', 'DESC']],
    } = payload;

    let whereClause = {};

    if (actionType) {
      whereClause.action_type = actionType;
    }

    if (module) {
      whereClause.module = module;
    }

    if (startDate && endDate) {
      whereClause.created_at = {
        [this.Sequelize.Op.between]: [startDate, endDate],
      };
    }

    return await this.findAll({
      limit,
      page,
      order,
      paginate,
      where: whereClause,
    });
  }

  async getAuditLogDetailsById(id) {
    const auditLog = await this.tableName.findByPk(id);

    if (!auditLog) {
      throw this.clientErrors.NOT_FOUND({
        message: `Audit log with id of ${id} not found`,
      });
    }

    const result = auditLog.get({ plain: true });

    if (result.createdAt) {
      result.createdAt = new Date(result.createdAt).toISOString();
    }

    return result;
  }

  async create(data) {
    return await super.create({
      actionType: data.actionType,
      module: data.module,
      description: data.description,
      metadata: data.metadata,
    });
  }
}

module.exports = AuditLogRepository;
