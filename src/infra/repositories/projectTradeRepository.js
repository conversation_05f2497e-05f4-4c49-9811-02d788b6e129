const BaseRepository = require('./baseRepository');

class ProjectTradeRepository extends BaseRepository {
  constructor({ db }) {
    super(db.projectTradeModel);
  }

  async getProjectEngineers(projectId, tradeId) {
    const engineers = await this.findAll({
      where: { projectId, tradeId },
      attributes: {
        exclude: ['projectId', 'tradeId', 'engineerId'],
      },
      include: [
        {
          association: 'engineer',
          attributes: ['id', 'firstName', 'lastName'],
        },
      ],
    });

    return engineers;
  }
}

module.exports = ProjectTradeRepository;
