const BaseRepository = require('./baseRepository');

class TransactionLogRepository extends BaseRepository {
  constructor({ db, clientErrors, utils }) {
    super(db.transactionLogModel);
    this.Sequelize = db.Sequelize;
    this.clientErrors = clientErrors;
  }

  async getAllRequisitionIdJourney(rsId) {
    const queryOptions = {
      where: { rsId },
      order: [['time', 'ASC']],
      paginate: false,
    };

    const results = await this.findAll(queryOptions);

    return results;
  }
}

module.exports = TransactionLogRepository;
