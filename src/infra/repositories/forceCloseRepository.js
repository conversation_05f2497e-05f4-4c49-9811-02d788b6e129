const BaseRepository = require('./baseRepository');

class ForceCloseRepository extends BaseRepository {
  constructor({
    db,
    clientErrors,
    fastify,
    requisitionRepository,
    purchaseOrderRepository,
    canvassRequisitionRepository,
    deliveryReceiptRepository,
    invoiceReportRepository,
    rsPaymentRequestRepository,
    itemRepository,
    historyRepository,
    requisitionItemListRepository,
  }) {
    // Force close repository doesn't have its own model, it orchestrates other repositories
    super(null);

    this.db = db;
    this.Sequelize = db.Sequelize;
    this.clientErrors = clientErrors;
    this.fastify = fastify;

    // Inject all required repositories for force close operations
    this.requisitionRepository = requisitionRepository;
    this.purchaseOrderRepository = purchaseOrderRepository;
    this.canvassRequisitionRepository = canvassRequisitionRepository;
    this.deliveryReceiptRepository = deliveryReceiptRepository;
    this.invoiceReportRepository = invoiceReportRepository;
    this.rsPaymentRequestRepository = rsPaymentRequestRepository;
    this.itemRepository = itemRepository;
    this.historyRepository = historyRepository;
    this.requisitionItemRepository = requisitionItemListRepository;
  }

  /**
   * Execute force close workflow with transaction safety
   * Centralizes all database operations for force close
   */
  async executeForceCloseWorkflow({
    requisitionId,
    userId,
    scenario,
    notes,
    transaction,
  }) {
    this.fastify.log.info(
      `Executing force close workflow for RS: ${requisitionId}, Scenario: ${scenario}`,
    );

    const result = {
      requisitionId,
      scenario,
      documentsUpdated: [],
      quantitiesReturned: {},
      poAdjustments: [],
    };

    try {
      // Update requisition status to CLOSED
      await this._updateRequisitionStatus(
        requisitionId,
        'CLOSED',
        userId,
        transaction,
      );
      result.documentsUpdated.push({
        type: 'requisition',
        id: requisitionId,
        status: 'CLOSED',
      });

      // Add force close notes to RS (user-provided notes)
      await this._addForceCloseNotesToRS(
        requisitionId,
        userId,
        notes,
        transaction,
      );

      // Determine validation path based on scenario
      let validationPath = 'CLOSED_PO_PATH'; // Default for most scenarios
      if (scenario === 'ACTIVE_PO_PARTIAL_DELIVERY') {
        validationPath = 'ACTIVE_PO_PATH';
      }

      // Execute scenario-specific operations
      switch (scenario) {
        case 'ACTIVE_PO_PARTIAL_DELIVERY':
          await this._handleActivePOPartialDeliveryScenario(
            requisitionId,
            userId,
            transaction,
            result,
          );
          break;
        case 'CLOSED_PO_WITH_REMAINING_CANVASS_QTY':
          await this._handleClosedPORemainingQtyScenario(
            requisitionId,
            userId,
            transaction,
            result,
          );
          break;
        case 'CLOSED_PO_PENDING_CS':
          await this._handleClosedPOPendingCSScenario(
            requisitionId,
            userId,
            transaction,
            result,
          );
          break;
        default:
          throw new Error(`Unknown force close scenario: ${scenario}`);
      }

      // Cancel all draft/pending related documents
      await this._cancelDraftDocuments(
        requisitionId,
        userId,
        transaction,
        result,
      );

      // Add force close notes and audit trail
      await this._addForceCloseAuditTrail(
        requisitionId,
        userId,
        scenario,
        notes,
        transaction,
        {
          validationPath,
          quantitiesAffected: result.quantitiesReturned,
          documentsCancelled: result.documentsUpdated,
          poAdjustments: result.poAdjustments,
        },
      );

      this.fastify.log.info(
        `Force close workflow completed successfully for RS: ${requisitionId}`,
      );
      return result;
    } catch (error) {
      this.fastify.log.error(
        `Force close workflow failed for RS: ${requisitionId} - ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Update requisition status with audit trail
   */
  async _updateRequisitionStatus(requisitionId, status, userId, transaction) {
    await this.requisitionRepository.update(
      { id: requisitionId },
      { status },
      { transaction, userId },
    );
  }

  /**
   * Add force close notes to RS (user-provided notes)
   * Stores the user-provided force close reason in the notes table for "Check Notes" functionality
   */
  async _addForceCloseNotesToRS(requisitionId, userId, notes, transaction) {
    try {
      // Get user information for the note
      const user = await this.db.userModel.findByPk(userId, { transaction });
      const userName = user ? `${user.firstName} ${user.lastName}` : 'System';

      // Add force close notes to notes table (for "Check Notes" functionality)
      await this.db.noteModel.create(
        {
          model: 'requisition',
          modelId: requisitionId,
          userName: userName,
          userType: 'System',
          commentType: 'Note',
          note: `Force Close Notes: ${notes}`,
        },
        { transaction },
      );

      this.fastify.log.info(`Added force close notes to RS ${requisitionId}`);
    } catch (error) {
      this.fastify.log.error(
        `Failed to add force close notes to RS ${requisitionId}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Handle Scenario 1: Active Purchase Orders with Partial Deliveries
   * Update PO amounts and quantities to reflect delivered quantities only
   */
  async _handleActivePOPartialDeliveryScenario(
    requisitionId,
    userId,
    transaction,
    result,
  ) {
    this.fastify.log.info(
      `Handling active PO partial delivery scenario for RS: ${requisitionId}`,
    );

    // Get purchase orders for this requisition
    const purchaseOrders = await this._getPurchaseOrdersForRequisition(
      requisitionId,
      transaction,
    );

    for (const po of purchaseOrders) {
      // Get delivered quantities for this PO
      const deliveredData = await this._getDeliveredQuantitiesForPO(
        po.id,
        transaction,
      );

      if (deliveredData.totalDelivered > 0) {
        // Update PO amount and quantity to reflect only delivered quantities
        const originalAmount = po.total_amount || po.totalAmount;
        const originalQuantity = await this._calculateTotalPOQuantity(
          po.id,
          transaction,
        );
        const newAmount = deliveredData.totalAmount;
        const newQuantity = deliveredData.totalQuantity;

        await this._updatePOAmountAndQuantity(
          po.id,
          newAmount,
          newQuantity,
          userId,
          transaction,
        );

        // Generate system notes for the PO modification
        const systemNotes = `Force Close: PO updated to reflect delivered quantities only. Original Amount: ${originalAmount}, New Amount: ${newAmount}. Original Quantity: ${originalQuantity}, New Quantity: ${newQuantity}.`;
        await this._addPOSystemNotes(po.id, systemNotes, transaction);

        result.poAdjustments.push({
          poId: po.id,
          originalAmount,
          newAmount,
          originalQuantity,
          newQuantity,
          systemNotes,
        });

        // Update PO status to CLOSED
        await this._updatePOStatus(po.id, 'closed_po', userId, transaction);
        result.documentsUpdated.push({
          type: 'purchase_order',
          id: po.id,
          status: 'closed_po',
        });
      }
    }

    // Return unfulfilled quantities to GFQ for OFM items (Scenario 1 specific)
    await this._returnUnfulfilledQuantitiesFromPOsToGFQ(
      requisitionId,
      purchaseOrders,
      transaction,
      result,
    );
  }

  /**
   * Return unfulfilled quantities from POs to GFQ for OFM items (Scenario 1)
   * This calculates unfulfilled quantities from existing POs, not remaining quantities to canvass
   */
  async _returnUnfulfilledQuantitiesFromPOsToGFQ(
    requisitionId,
    purchaseOrders,
    transaction,
    result,
  ) {
    try {
      // Get requisition type to determine if items are OFM
      const requisition = await this.db.requisitionModel.findByPk(
        requisitionId,
        {
          attributes: ['id', 'type'],
          transaction,
        },
      );

      if (!requisition || !['ofm', 'ofm-tom'].includes(requisition.type)) {
        this.fastify.log.info(
          `Requisition ${requisitionId} is not OFM type, skipping GFQ return`,
        );
        return;
      }

      for (const po of purchaseOrders) {
        // Get PO items with their delivered quantities
        const poItems = await this.db.purchaseOrderItemModel.findAll({
          where: { purchase_order_id: po.id },
          include: [
            {
              model: this.db.requisitionItemListModel,
              as: 'requisitionItemList',
              include: [
                {
                  model: this.db.itemModel,
                  as: 'item',
                  attributes: ['id', 'itemCd', 'itmDes', 'remainingGfq'],
                },
              ],
            },
          ],
          transaction,
        });

        for (const poItem of poItems) {
          // Calculate unfulfilled quantity for this PO item
          const poQuantity = parseFloat(poItem.quantityPurchased) || 0;
          const deliveredQuantity = await this._getDeliveredQuantityForPOItem(
            poItem.id,
            transaction,
          );
          const unfulfilledQuantity = Math.max(
            0,
            poQuantity - deliveredQuantity,
          );

          if (unfulfilledQuantity > 0 && poItem.requisitionItemList?.item) {
            const item = poItem.requisitionItemList.item;

            // Return unfulfilled quantity to GFQ
            await this._returnQuantityToGFQ(
              {
                id: poItem.requisitionItemList.id,
                itemId: item.id,
                remainingQty: unfulfilledQuantity,
              },
              transaction,
            );

            // Log the return
            result.quantitiesReturned[poItem.requisitionItemList.id] = {
              itemName: item.itmDes,
              returnedQty: unfulfilledQuantity,
              type: 'OFM',
            };

            this.fastify.log.info(
              `Returned ${unfulfilledQuantity} units of item ${item.itemCd} to GFQ ` +
                `(PO quantity: ${poQuantity}, Delivered: ${deliveredQuantity})`,
            );
          }
        }
      }
    } catch (error) {
      this.fastify.log.error(
        `Failed to return unfulfilled quantities from POs to GFQ: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Return unfulfilled quantities to GFQ for OFM items (Scenarios 2 & 3)
   * This uses remaining quantities to canvass for scenarios where POs are already closed
   */
  async _returnUnfulfilledQuantitiesToGFQ(requisitionId, transaction, result) {
    // Get remaining quantities that need to be returned to GFQ
    const remainingItems = await this._getRemainingQuantitiesToCanvass(
      requisitionId,
      transaction,
    );

    for (const item of remainingItems) {
      if (
        item.remainingQty > 0 &&
        (item.type === 'OFM' || item.type === 'OFM-TOM')
      ) {
        await this._returnQuantityToGFQ(item, transaction);
        result.quantitiesReturned[item.id] = {
          itemName: item.name,
          returnedQty: item.remainingQty,
          type: item.type,
        };
      }
    }
  }

  /**
   * Handle Scenario 2: All Purchase Orders Closed/Cancelled with Remaining Quantities
   * Zero out remaining quantities and return to GFQ for OFM items
   */
  async _handleClosedPORemainingQtyScenario(
    requisitionId,
    userId,
    transaction,
    result,
  ) {
    this.fastify.log.info(
      `Handling closed PO remaining quantity scenario for RS: ${requisitionId}`,
    );

    // Zero out all remaining quantities for canvassing
    const remainingItems = await this._getRemainingQuantitiesToCanvass(
      requisitionId,
      transaction,
    );

    for (const item of remainingItems) {
      if (item.remainingQty > 0) {
        // Return quantities to GFQ for OFM and OFM-TOM items only
        if (item.type === 'OFM' || item.type === 'OFM-TOM') {
          await this._returnQuantityToGFQ(item, transaction);
          result.quantitiesReturned[item.id] = {
            itemName: item.name,
            returnedQty: item.remainingQty,
            type: item.type,
          };
        }

        // Zero out the remaining quantity
        await this._zeroOutRemainingQuantity(item.id, userId, transaction);
      }
    }
  }

  /**
   * Handle Scenario 3: Closed Purchase Orders with Pending Canvass Sheet Approvals
   * Cancel pending canvass sheets, zero out quantities, and return to GFQ
   */
  async _handleClosedPOPendingCSScenario(
    requisitionId,
    userId,
    transaction,
    result,
  ) {
    this.fastify.log.info(
      `Handling closed PO pending CS scenario for RS: ${requisitionId}`,
    );

    // Get pending canvass sheets
    const pendingCanvassSheets = await this._getPendingCanvassSheets(
      requisitionId,
      transaction,
    );

    // Cancel pending canvass sheets
    for (const cs of pendingCanvassSheets) {
      await this._cancelCanvassSheet(cs.id, userId, transaction);
      result.documentsUpdated.push({
        type: 'canvass_sheet',
        id: cs.id,
        status: 'CANCELLED',
      });
    }

    // Zero out remaining quantities and return to GFQ (similar to scenario 2)
    await this._handleClosedPORemainingQtyScenario(
      requisitionId,
      userId,
      transaction,
      result,
    );
  }

  /**
   * Cancel all draft/pending documents related to the requisition
   * Updates document statuses to cancelled with proper tracking
   */
  async _cancelDraftDocuments(requisitionId, userId, transaction, result) {
    try {
      this.fastify.log.info(
        `🚀 STARTING: Cancelling draft documents for RS: ${requisitionId}`,
      );

      // Cancel draft and pending canvass sheets
      const draftCanvassSheets = await this._getDraftCanvassSheets(
        requisitionId,
        transaction,
      );
      this.fastify.log.info(
        `🚀 FOUND ${draftCanvassSheets.length} draft canvass sheets for RS: ${requisitionId}`,
      );
      for (const cs of draftCanvassSheets) {
        this.fastify.log.info(
          `🚀 CANCELLING canvass sheet ${cs.id} with status: ${cs.status}`,
        );
        await this._cancelCanvassSheet(cs.id, userId, transaction);
        result.documentsUpdated.push({
          type: 'canvass_sheet',
          id: cs.id,
          status: 'cs_cancelled',
        });
      }

      // Cancel draft delivery receipts
      const draftDeliveryReceipts = await this._getDraftDeliveryReceipts(
        requisitionId,
        transaction,
      );
      this.fastify.log.info(
        `🚀 FOUND ${draftDeliveryReceipts.length} draft delivery receipts for RS: ${requisitionId}`,
      );
      for (const dr of draftDeliveryReceipts) {
        this.fastify.log.info(
          `🚀 CANCELLING delivery receipt ${dr.id} with status: ${dr.status}`,
        );
        await this._cancelDeliveryReceipt(dr.id, userId, transaction);
        result.documentsUpdated.push({
          type: 'delivery_receipt',
          id: dr.id,
          status: 'rr_cancelled',
        });
      }

      // Cancel draft invoice reports
      const draftInvoiceReports = await this._getDraftInvoiceReports(
        requisitionId,
        transaction,
      );
      this.fastify.log.info(
        `🚀 FOUND ${draftInvoiceReports.length} draft invoice reports for RS: ${requisitionId}`,
      );
      for (const ir of draftInvoiceReports) {
        this.fastify.log.info(
          `🚀 CANCELLING invoice report ${ir.id} with status: ${ir.status}`,
        );
        await this._cancelInvoiceReport(ir.id, userId, transaction);
        result.documentsUpdated.push({
          type: 'invoice_report',
          id: ir.id,
          status: 'ir_cancelled',
        });
      }

      // Cancel draft payment requests
      const draftPaymentRequests = await this._getDraftPaymentRequests(
        requisitionId,
        transaction,
      );
      this.fastify.log.info(
        `🚀 FOUND ${draftPaymentRequests.length} draft payment requests for RS: ${requisitionId}`,
      );
      for (const pr of draftPaymentRequests) {
        this.fastify.log.info(
          `🚀 CANCELLING payment request ${pr.id} with status: ${pr.status}`,
        );
        await this._cancelPaymentRequest(pr.id, userId, transaction);
        result.documentsUpdated.push({
          type: 'payment_request',
          id: pr.id,
          status: 'pr_cancelled',
        });
      }

      this.fastify.log.info(
        `🚀 SUCCESS: Cancelled draft documents for RS: ${requisitionId}`,
      );
    } catch (error) {
      this.fastify.log.error(
        `🚨 ERROR: Failed to cancel draft documents for RS: ${requisitionId} - ${error.message}`,
      );
      this.fastify.log.error(`🚨 ERROR STACK: ${error.stack}`);
      throw error;
    }
  }

  /**
   * Add comprehensive audit trail for force close operation
   * Creates a detailed log entry in the force_close_logs table
   */
  async _addForceCloseAuditTrail(
    requisitionId,
    userId,
    scenario,
    notes,
    transaction,
    additionalData = {},
  ) {
    try {
      // Create comprehensive force close log entry
      const forceCloseLog = await this.db.forceCloseLogModel.create(
        {
          requisitionId,
          userId,
          scenarioType: scenario,
          validationPath: additionalData.validationPath || 'UNKNOWN_PATH',
          quantitiesAffected: additionalData.quantitiesAffected || null,
          documentsCancelled: additionalData.documentsCancelled || null,
          poAdjustments: additionalData.poAdjustments || null,
          forceCloseNotes: notes,
        },
        { transaction },
      );

      this.fastify.log.info(
        `Force close audit trail created with ID: ${forceCloseLog.id} for RS: ${requisitionId}`,
      );

      return forceCloseLog;
    } catch (error) {
      this.fastify.log.error(
        `Failed to create force close audit trail for RS: ${requisitionId} - ${error.message}`,
      );
      throw error;
    }
  }

  // Placeholder methods for specific database operations
  // These will be implemented in subsequent tasks when database schema is updated

  async _getRemainingQuantitiesToCanvass(requisitionId, transaction) {
    try {
      // Get requisition with items and related canvass data
      const requisition = await this.db.requisitionModel.findByPk(
        requisitionId,
        {
          include: [
            {
              model: this.db.requisitionItemListModel,
              as: 'requisitionItemLists',
              include: [
                {
                  model: this.db.itemModel,
                  as: 'item',
                  attributes: ['id', 'itmDes', 'itemCd', 'unit'],
                },
                {
                  model: this.db.nonOfmItemModel,
                  as: 'nonOfmItem',
                  attributes: ['id', 'itemName', 'itemType', 'unit'],
                },
              ],
            },
            {
              model: this.db.canvassRequisitionModel,
              as: 'canvassRequisitions',
              where: { status: ['cs_approved'] }, // Using correct database status value
              required: false,
              include: [
                {
                  model: this.db.canvassItemModel,
                  as: 'canvassItems',
                  include: [
                    {
                      model: this.db.canvassItemSupplierModel,
                      as: 'suppliers', // Correct association name from model
                      where: { is_selected: true }, // Only count selected suppliers
                      required: false,
                    },
                  ],
                },
              ],
            },
          ],
          transaction,
        },
      );

      if (!requisition || !requisition.requisitionItemLists) {
        return [];
      }

      const remainingItems = [];

      for (const reqItem of requisition.requisitionItemLists) {
        const requestedQty = parseFloat(reqItem.quantity) || 0;
        let totalCanvassedQty = 0;

        // Calculate total canvassed quantity for this item from approved canvass sheets
        for (const canvass of requisition.canvassRequisitions || []) {
          const canvassItems =
            canvass.canvassItems?.filter(
              (ci) => ci.requisitionItemListId === reqItem.id,
            ) || [];

          for (const canvassItem of canvassItems) {
            // Sum quantities from selected suppliers only
            const selectedSuppliers =
              canvassItem.suppliers?.filter((cis) => cis.isSelected) || [];
            for (const supplier of selectedSuppliers) {
              totalCanvassedQty += parseFloat(supplier.quantity) || 0;
            }
          }
        }

        const remainingQty = Math.max(0, requestedQty - totalCanvassedQty);

        if (remainingQty > 0) {
          // Get item name based on item type
          let itemName = 'Unknown Item';
          let itemType = reqItem.itemType || 'UNKNOWN';

          if (reqItem.itemType === 'ofm' || reqItem.itemType === 'ofm-tom') {
            itemName = reqItem.item?.itmDes || 'Unknown OFM Item';
          } else if (
            reqItem.itemType === 'non-ofm' ||
            reqItem.itemType === 'non-ofm-tom'
          ) {
            itemName = reqItem.nonOfmItem?.itemName || 'Unknown Non-OFM Item';
          }

          remainingItems.push({
            id: reqItem.id,
            itemId: reqItem.itemId,
            name: itemName,
            type: itemType.toUpperCase(),
            requestedQty,
            canvassedQty: totalCanvassedQty,
            remainingQty,
            unitPrice: 0, // Will be calculated from canvass items if needed
          });
        }
      }

      return remainingItems;
    } catch (error) {
      this.fastify.log.error(
        `Failed to get remaining quantities for requisition ${requisitionId}: ${error.message}`,
      );
      return [];
    }
  }

  async _returnQuantityToGFQ(item, transaction) {
    try {
      // Validate input
      const itemId = item.itemId || item.id;
      if (!itemId) {
        this.fastify.log.warn(`No item ID provided for GFQ return, skipping`);
        return;
      }

      // Get the item record to update remaining GFQ
      const itemRecord = await this.itemRepository.findOne({
        where: { id: itemId },
        attributes: ['id', 'itemCd', 'itmDes', 'remainingGfq'],
        transaction,
      });

      if (!itemRecord) {
        this.fastify.log.warn(
          `Could not find item record for item ${itemId}, skipping GFQ return`,
        );
        return;
      }

      // Update the item's remaining GFQ by adding back the quantity
      const currentRemainingGfq = parseFloat(itemRecord.remainingGfq) || 0;
      const returnQuantity =
        parseFloat(item.remainingQty || item.quantity) || 0;

      if (returnQuantity <= 0) {
        this.fastify.log.warn(
          `Invalid return quantity ${returnQuantity} for item ${itemId}, skipping GFQ return`,
        );
        return;
      }

      const newRemainingGfq = currentRemainingGfq + returnQuantity;

      await this.itemRepository.update(
        { id: itemRecord.id },
        { remainingGfq: newRemainingGfq },
        { transaction },
      );

      this.fastify.log.info(
        `Returned ${returnQuantity} units to GFQ for item ${itemRecord.itemCd} (${itemRecord.itmDes}). ` +
          `Previous GFQ: ${currentRemainingGfq}, New GFQ: ${newRemainingGfq}`,
      );
    } catch (error) {
      this.fastify.log.error(
        `Failed to return quantity to GFQ for item ${item.itemId || item.id}: ${error.message}`,
        {
          error: error.stack,
          item: item,
          itemId: item.itemId || item.id,
        },
      );
      throw error;
    }
  }

  async _zeroOutRemainingQuantity(itemId, userId, transaction) {
    try {
      // Validate that the requisition item exists
      const existingItem = await this.requisitionItemRepository.findOne({
        where: { id: itemId },
        transaction,
      });

      if (!existingItem) {
        this.fastify.log.warn(
          `Requisition item ${itemId} not found, skipping zero out operation`,
        );
        return;
      }

      // Update requisition item to zero out remaining quantity for canvassing
      await this.requisitionItemRepository.update(
        { id: itemId },
        {
          quantityForCanvassing: 0,
          forceCloseRemainingQty: 0, // Store that this was force closed
          updatedBy: userId,
        },
        { transaction },
      );

      this.fastify.log.info(
        `Zeroed out remaining quantity for requisition item ${itemId}`,
      );
    } catch (error) {
      this.fastify.log.error(
        `Failed to zero out remaining quantity for item ${itemId}: ${error.message}`,
        {
          error: error.stack,
          itemId,
          userId,
        },
      );
      throw error;
    }
  }

  async _getPendingCanvassSheets(requisitionId, transaction) {
    try {
      return await this.db.canvassRequisitionModel.findAll({
        where: {
          requisitionId,
          status: ['for_cs_approval', 'cs_draft'], // Using correct database status values
        },
        include: [
          {
            association: 'canvassItems',
          },
        ],
        transaction,
      });
    } catch (error) {
      this.fastify.log.error(
        `Failed to get pending canvass sheets for requisition ${requisitionId}: ${error.message}`,
      );
      return [];
    }
  }

  /**
   * Get draft and pending canvass sheets for a requisition
   */
  async _getDraftCanvassSheets(requisitionId, transaction) {
    try {
      return await this.db.canvassRequisitionModel.findAll({
        where: {
          requisitionId,
          status: ['cs_draft', 'for_cs_approval'], // Using correct database status values
        },
        transaction,
      });
    } catch (error) {
      this.fastify.log.error(
        `Failed to get draft canvass sheets for requisition ${requisitionId}: ${error.message}`,
      );
      return [];
    }
  }

  /**
   * Get draft delivery receipts for a requisition
   */
  async _getDraftDeliveryReceipts(requisitionId, transaction) {
    try {
      return await this.db.deliveryReceiptModel.findAll({
        where: {
          requisitionId,
          status: 'Draft', // Using actual database status value
        },
        transaction,
      });
    } catch (error) {
      this.fastify.log.error(
        `Failed to get draft delivery receipts for RS ${requisitionId}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Get draft invoice reports for a requisition
   */
  async _getDraftInvoiceReports(requisitionId, transaction) {
    try {
      return await this.db.invoiceReportModel.findAll({
        where: {
          requisitionId,
          status: 'IR Draft', // Correct database status value from constants
        },
        transaction,
      });
    } catch (error) {
      this.fastify.log.error(
        `Failed to get draft invoice reports for RS ${requisitionId}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Get draft payment requests for a requisition
   */
  async _getDraftPaymentRequests(requisitionId, transaction) {
    try {
      return await this.db.rsPaymentRequestModel.findAll({
        where: {
          requisitionId,
          status: ['PR Draft', 'For PR Approval'], // Using actual database status values
        },
        transaction,
      });
    } catch (error) {
      this.fastify.log.error(
        `Failed to get draft payment requests for RS ${requisitionId}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Cancel a canvass sheet
   */
  async _cancelCanvassSheet(csId, userId, transaction) {
    try {
      await this.db.canvassRequisitionModel.update(
        {
          status: 'cs_cancelled', // Using correct cancelled status from constants
          cancelledAt: new Date(),
          cancelledBy: userId,
          cancellationReason: 'Force close operation - canvass sheet cancelled',
        },
        {
          where: { id: csId },
          transaction,
        },
      );

      this.fastify.log.info(
        `Cancelled canvass sheet ${csId} for force close operation`,
      );
    } catch (error) {
      this.fastify.log.error(
        `Failed to cancel canvass sheet ${csId}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Cancel a delivery receipt
   */
  async _cancelDeliveryReceipt(drId, userId, transaction) {
    try {
      await this.db.deliveryReceiptModel.update(
        {
          status: 'rr_cancelled', // Using correct cancelled status from constants
          latestDeliveryStatus: 'RR Cancelled', // Update dashboard status display
          cancelledAt: new Date(),
          cancelledBy: userId,
          cancellationReason:
            'Force close operation - draft document cancelled',
        },
        {
          where: { id: drId },
          transaction,
        },
      );

      this.fastify.log.info(
        `Cancelled delivery receipt ${drId} for force close operation`,
      );
    } catch (error) {
      this.fastify.log.error(
        `Failed to cancel delivery receipt ${drId}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Cancel an invoice report
   */
  async _cancelInvoiceReport(irId, userId, transaction) {
    try {
      await this.db.invoiceReportModel.update(
        {
          status: 'ir_cancelled', // Using correct cancelled status from constants
          cancelledAt: new Date(),
          cancelledBy: userId,
          cancellationReason:
            'Force close operation - draft document cancelled',
        },
        {
          where: { id: irId },
          transaction,
        },
      );

      this.fastify.log.info(
        `Cancelled invoice report ${irId} for force close operation`,
      );
    } catch (error) {
      this.fastify.log.error(
        `Failed to cancel invoice report ${irId}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Cancel a payment request
   */
  async _cancelPaymentRequest(prId, userId, transaction) {
    try {
      await this.db.rsPaymentRequestModel.update(
        {
          status: 'pr_cancelled', // Using correct cancelled status from constants
          cancelledAt: new Date(),
          cancelledBy: userId,
          cancellationReason:
            'Force close operation - draft document cancelled',
        },
        {
          where: { id: prId },
          transaction,
        },
      );

      this.fastify.log.info(
        `Cancelled payment request ${prId} for force close operation`,
      );
    } catch (error) {
      this.fastify.log.error(
        `Failed to cancel payment request ${prId}: ${error.message}`,
      );
      throw error;
    }
  }

  async _getPurchaseOrdersForRequisition(requisitionId, transaction) {
    try {
      const purchaseOrders = await this.db.purchaseOrderModel.findAll({
        where: { requisitionId },
        include: [
          {
            model: this.db.purchaseOrderItemModel,
            as: 'purchaseOrderItems',
          },
        ],
        transaction,
      });

      return purchaseOrders;
    } catch (error) {
      this.fastify.log.error(
        `Failed to get purchase orders for requisition ${requisitionId}: ${error.message}`,
      );
      throw error;
    }
  }

  async _getDeliveredQuantitiesForPO(poId, transaction) {
    try {
      // Get total approved Payment Request amount for this PO (as per requirement)
      const [paymentRequestResult] = await this.db.sequelize.query(
        `
        SELECT COALESCE(SUM(total_amount), 0) as total_approved_pr_amount
        FROM rs_payment_requests
        WHERE purchase_order_id = :poId AND status IN ('Approved', 'Closed')
      `,
        {
          replacements: { poId },
          type: this.db.sequelize.QueryTypes.SELECT,
          transaction,
        },
      );

      // Get total delivered Receiving Report quantity for this PO (as per requirement)
      const [deliveryResult] = await this.db.sequelize.query(
        `
        SELECT COALESCE(SUM(qty_delivered), 0) as total_delivered_rr_quantity
        FROM delivery_receipt_items dri
        JOIN delivery_receipts dr ON dr.id = dri.dr_id
        WHERE dr.po_id = :poId AND dr.status IN ('Delivered', 'rr_cancelled')
      `,
        {
          replacements: { poId },
          type: this.db.sequelize.QueryTypes.SELECT,
          transaction,
        },
      );

      const totalApprovedPRAmount =
        parseFloat(paymentRequestResult.total_approved_pr_amount) || 0;
      const totalDeliveredRRQuantity =
        parseFloat(deliveryResult.total_delivered_rr_quantity) || 0;

      return {
        totalDelivered: totalDeliveredRRQuantity,
        totalAmount: parseFloat(totalApprovedPRAmount.toFixed(2)), // Total approved PR amount
        totalQuantity: parseFloat(totalDeliveredRRQuantity.toFixed(2)), // Total delivered RR quantity
      };
    } catch (error) {
      this.fastify.log.error(
        `Failed to get approved PR amount and delivered RR quantity for PO ${poId}: ${error.message}`,
      );
      return { totalDelivered: 0, totalAmount: 0, totalQuantity: 0 };
    }
  }

  async _calculateTotalPOQuantity(poId, transaction) {
    try {
      const [result] = await this.db.sequelize.query(
        `
        SELECT COALESCE(SUM(quantity_purchased), 0) as total_quantity
        FROM purchase_order_items
        WHERE purchase_order_id = :poId
      `,
        {
          replacements: { poId },
          type: this.db.sequelize.QueryTypes.SELECT,
          transaction,
        },
      );

      return parseFloat(result.total_quantity) || 0;
    } catch (error) {
      this.fastify.log.error(
        `Failed to calculate total PO quantity for PO ${poId}: ${error.message}`,
      );
      return 0;
    }
  }

  async _updatePOAmountAndQuantity(
    poId,
    amount,
    quantity,
    userId,
    transaction,
  ) {
    try {
      // First, get the current PO to store original values
      const currentPO = await this.db.purchaseOrderModel.findByPk(poId, {
        transaction,
      });

      if (!currentPO) {
        throw new Error(`Purchase Order ${poId} not found`);
      }

      // Store original values if not already stored
      const updateData = {
        totalAmount: parseFloat(amount), // Use camelCase for Sequelize model
      };

      // Store original values if this is the first force close modification
      if (!currentPO.originalAmount) {
        updateData.originalAmount = currentPO.totalAmount;
      }
      if (!currentPO.originalQuantity) {
        updateData.originalQuantity = await this._calculateTotalPOQuantity(
          poId,
          transaction,
        );
      }

      await this.db.purchaseOrderModel.update(updateData, {
        where: { id: poId },
        transaction,
      });

      // Update PO item quantities to delivered quantities (as per requirement)
      await this._updatePOItemQuantitiesToDelivered(poId, userId, transaction);

      this.fastify.log.info(
        `Updated PO ${poId} - Amount: ${amount}, Quantity: ${quantity}`,
      );
    } catch (error) {
      this.fastify.log.error(
        `Failed to update PO amount and quantity for PO ${poId}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Update PO item quantities to match delivered RR quantities
   * This implements the requirement: "Pending POs: change PO Quantity to total delivered qty RR Quantity"
   */
  async _updatePOItemQuantitiesToDelivered(poId, userId, transaction) {
    try {
      // Get all PO items for this PO
      const poItems = await this.db.purchaseOrderItemModel.findAll({
        where: { purchase_order_id: poId },
        transaction,
      });

      for (const poItem of poItems) {
        // Calculate delivered quantity for this PO item
        const deliveredQty = await this._getDeliveredQuantityForPOItem(
          poItem.id,
          transaction,
        );

        // Update the PO item quantity to delivered quantity
        await this.db.purchaseOrderItemModel.update(
          {
            quantityPurchased: deliveredQty, // Use camelCase for Sequelize model
          },
          {
            where: { id: poItem.id },
            transaction,
          },
        );

        this.fastify.log.info(
          `Updated PO item ${poItem.id} quantity from ${poItem.quantity_purchased} to ${deliveredQty}`,
        );
      }
    } catch (error) {
      this.fastify.log.error(
        `Failed to update PO item quantities for PO ${poId}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Get delivered quantity for a specific PO item from delivery receipts
   */
  async _getDeliveredQuantityForPOItem(poItemId, transaction) {
    try {
      const [result] = await this.db.sequelize.query(
        `
        SELECT COALESCE(SUM(dri.qty_delivered), 0) as delivered_quantity
        FROM delivery_receipt_items dri
        JOIN delivery_receipts dr ON dr.id = dri.dr_id
        WHERE dri.po_item_id = :poItemId
          AND dr.status IN ('Delivered', 'rr_cancelled')
      `,
        {
          replacements: { poItemId },
          type: this.db.sequelize.QueryTypes.SELECT,
          transaction,
        },
      );

      return parseFloat(result.delivered_quantity) || 0;
    } catch (error) {
      this.fastify.log.error(
        `Failed to get delivered quantity for PO item ${poItemId}: ${error.message}`,
      );
      return 0;
    }
  }

  async _addPOSystemNotes(poId, notes, transaction) {
    try {
      // Get current PO to verify it exists
      const currentPO = await this.db.purchaseOrderModel.findByPk(poId, {
        transaction,
      });

      if (!currentPO) {
        throw new Error(`Purchase Order ${poId} not found`);
      }

      // Store system notes in the notes table for "Check Notes" functionality
      await this.db.noteModel.create(
        {
          model: 'purchase_order',
          modelId: poId,
          userName: 'System',
          userType: 'System',
          commentType: 'Force Close',
          note: notes,
        },
        { transaction },
      );

      // Also update the systemGeneratedNotes field for backward compatibility
      const existingNotes = currentPO.systemGeneratedNotes || '';
      const timestamp = new Date().toISOString();
      const newNotes = existingNotes
        ? `${existingNotes}\n[${timestamp}] ${notes}`
        : `[${timestamp}] ${notes}`;

      await this.db.purchaseOrderModel.update(
        {
          systemGeneratedNotes: newNotes,
        },
        {
          where: { id: poId },
          transaction,
        },
      );

      this.fastify.log.info(
        `Added system notes to PO ${poId} in both notes table and systemGeneratedNotes field`,
      );
    } catch (error) {
      this.fastify.log.error(
        `Failed to add system notes to PO ${poId}: ${error.message}`,
      );
      throw error;
    }
  }

  async _updatePOStatus(poId, status, userId, transaction) {
    try {
      await this.db.purchaseOrderModel.update(
        {
          status,
          updatedBy: userId,
          updatedAt: new Date(),
        },
        {
          where: { id: poId },
          transaction,
        },
      );

      this.fastify.log.info(`Updated PO ${poId} status to ${status}`);
    } catch (error) {
      this.fastify.log.error(
        `Failed to update PO status for PO ${poId}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Get force close history and audit trail for a requisition
   * Returns historical force close operations and related audit information
   */
  async getForceCloseHistory({ requisitionId, userId }) {
    this.fastify.log.info(
      `Retrieving force close history from repository for RS: ${requisitionId}`,
    );

    try {
      // Get force close logs from the database
      const forceCloseLogs = await this.db.forceCloseLogModel.findAll({
        where: { requisitionId },
        order: [['createdAt', 'DESC']],
        include: [
          {
            model: this.db.userModel,
            as: 'user',
            attributes: ['id', 'username', 'email'],
          },
          {
            model: this.db.requisitionModel,
            as: 'requisition',
            attributes: ['id', 'rsNumber', 'rsLetter', 'status'],
          },
        ],
      });

      // Get related audit trail from general history (simplified for now)
      const auditTrail = [];

      // Calculate summary statistics
      const summary = {
        totalForceCloses: forceCloseLogs.length,
        lastForceCloseDate:
          forceCloseLogs.length > 0 ? forceCloseLogs[0].createdAt : null,
        scenarios: [...new Set(forceCloseLogs.map((log) => log.scenarioType))],
        validationPaths: [
          ...new Set(forceCloseLogs.map((log) => log.validationPath)),
        ],
        totalDocumentsCancelled: forceCloseLogs.reduce((total, log) => {
          const docs = log.documentsCancelled;
          return total + (docs ? Object.keys(docs).length : 0);
        }, 0),
        totalQuantitiesAffected: forceCloseLogs.reduce((total, log) => {
          const quantities = log.quantitiesAffected;
          return total + (quantities ? Object.keys(quantities).length : 0);
        }, 0),
      };

      this.fastify.log.info(
        `Force close history retrieved from repository for RS: ${requisitionId} - Found ${forceCloseLogs.length} logs`,
      );

      return {
        history: forceCloseLogs,
        auditTrail,
        summary,
      };
    } catch (error) {
      this.fastify.log.error(
        `Force close history retrieval failed in repository for RS: ${requisitionId} - ${error.message}`,
      );
      throw error;
    }
  }
}

module.exports = ForceCloseRepository;
