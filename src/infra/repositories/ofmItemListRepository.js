const BaseRepository = require('./baseRepository');

class OfmItemListRepository extends BaseRepository {
  constructor({ db, clientErrors, fastify, utils }) {
    super(db.ofmItemListModel);
    this.db = db;
    this.Sequelize = db.Sequelize;
    this.clientErrors = clientErrors;
    this.fastify = fastify;
    this.utils = utils;
  }

  async bulkCreate(payload) {
    return await this.tableName.bulkCreate(payload, {
      updateOnDuplicate: [
        'listName',
        'departmentId',
        'projectId',
        'tradeId',
        'companyId',
      ],
    });
  }

  async findProjectWithCompany(projectCode) {
    try {
      const project = await this.db.projectModel.findOne({
        where: { code: projectCode },
        include: [
          {
            model: this.db.companyModel,
            as: 'company',
            attributes: ['code', 'name'],
          },
        ],
        raw: true,
        nest: true,
      });

      if (!project) {
        throw this.clientErrors.BAD_REQUEST({
          message: `Project with code ${projectCode} not found`,
        });
      }

      return project;
    } catch (error) {
      this.fastify.log.error(`Error finding project: ${JSON.stringify(error)}`);
      return null;
    }
  }

  async findTradeByCode(tradeCode) {
    try {
      const result = await this.db.tradeModel.findOne({
        where: { tradeCode },
        attributes: [
          ['trade_code', 'tradeCode'],
          ['trade_name', 'tradeName'],
        ],
        raw: true,
      });

      if (!result) {
        throw this.clientErrors.NOT_FOUND({
          message: `No trade found for code: ${tradeCode}`,
        });
      }

      return result;
    } catch (error) {
      this.fastify.log.error(`Error finding trade: ${JSON.stringify(error)}`);
      return null;
    }
  }

  async bulkCreateOfmLists(payload) {
    try {
      const result = await this.tableName.bulkCreate(payload, {
        ignoreDuplicates: true,
        fields: ['listName', 'companyCode', 'projectCode', 'tradeCode'],
        returning: true,
      });
      return result;
    } catch (error) {
      this.fastify.log.error(
        `Error in bulkCreateOfmLists: ${JSON.stringify(error)}`,
      );
      throw error;
    }
  }

  async findAllOfmLists() {
    try {
      return await this.tableName.findAll({
        raw: true,
      });
    } catch (error) {
      this.fastify.log.info(
        `Error finding OFM lists: ${JSON.stringify(error)}`,
      );
      return [];
    }
  }

  async findMatchingItemsByAcctCd(projectCode, tradeCode) {
    try {
      const items = await this.db.itemModel.findAll({
        attributes: ['id', 'itemCd', 'acctCd', 'remainingGfq', 'isSteelbars'],
        where: {
          [this.Sequelize.Op.and]: [
            this.Sequelize.literal(
              `SUBSTRING(acct_cd FROM 5 FOR 3) = '${projectCode}'`,
            ),
            this.Sequelize.literal(
              `SUBSTRING(acct_cd FROM 10 FOR 2) = '${tradeCode}'`,
            ),
          ],
        },
        raw: true,
      });

      return items;
    } catch (error) {
      this.fastify.log.error(
        `Error finding matching items: ${JSON.stringify(error)}`,
      );
      return [];
    }
  }

  async createOfmListItems(ofmListId, items) {
    try {
      if (!ofmListId) {
        throw this.clientErrors.BAD_REQUEST({
          message: `No ofmListId provided`,
        });
      }

      if (!items || !items.length) {
        throw this.clientErrors.NOT_FOUND({
          message: `No items to create list items for`,
        });
      }

      const existingItems = await this.db.ofmListItemModel.findAll({
        where: {
          ofmListId,
          ofmItemId: items.map((item) => item.id),
        },
      });

      const existingItemIds = new Set(
        existingItems.map((item) => item.ofmItemId),
      );

      const newItems = items
        .filter((item) => !existingItemIds.has(item.id))
        .map((item) => ({
          ofmListId,
          ofmItemId: item.id,
        }));

      if (newItems.length === 0) {
        return [];
      }

      const result = await this.db.ofmListItemModel.bulkCreate(newItems);

      return result;
    } catch (error) {
      this.fastify.log.error(
        `Error in createOfmListItems: ${JSON.stringify(error)}`,
      );
      throw error;
    }
  }

  async linkItemsToLists() {
    try {
      const ofmLists = await this.findAllOfmLists();

      for (const list of ofmLists) {
        if (!list.id) {
          this.fastify.log.info(`List missing ID: ${JSON.stringify(list)}`);
          continue;
        }

        const tradeCode = list.tradeCode.toString().padStart(2, '0');

        const matchingItems = await this.findMatchingItemsByAcctCd(
          list.projectCode,
          tradeCode,
        );

        if (matchingItems.length > 0) {
          await this.createOfmListItems(list.id, matchingItems);
        } else {
          this.fastify.log.info(
            `No matching items for list: ${JSON.stringify(list.id)}`,
          );
        }
      }
    } catch (error) {
      this.fastify.log.error(
        `Error in linkItemsToLists: ${JSON.stringify(error)}`,
      );
      throw error;
    }
  }

  async findAllOfmLists() {
    try {
      const lists = await this.tableName.findAll({
        raw: true,
      });

      return lists;
    } catch (error) {
      this.fastify.log.error(
        `Error finding OFM lists: ${JSON.stringify(error)}`,
      );
      return [];
    }
  }

  async getAllOfmItemLists(payload) {
    let whereClause = {};
    let whereTrade = {};
    let whereProject = {};
    let whereCompany = {};
    const {
      search,
      companyCode,
      projectCode,
      tradeCode,
      paginate,
      limit,
      filterBy,
      page,
      order = [['list_name', 'ASC']],
      tradeIds,
      projectIds,
    } = payload;

    const { listName, company, project, trade } =
      this.utils.buildFilterWhereClause(filterBy);

    if (search) {
      whereClause.list_name = { [this.Sequelize.Op.iLike]: `%${search}%` };
    }

    if (listName) {
      whereClause.listName = {
        [this.Sequelize.Op.iLike]: `%${listName}%`,
      };
    }

    if (company) {
      whereCompany.name = {
        [this.Sequelize.Op.iLike]: `%${company}%`,
      };
    }

    if (project) {
      whereProject.name = {
        [this.Sequelize.Op.iLike]: `%${project}%`,
      };
    }

    if (trade) {
      whereTrade.trade_name = {
        [this.Sequelize.Op.iLike]: `%${trade}%`,
      };
    }

    const includes = [
      {
        model: this.db.companyModel,
        as: 'company',
        where: whereCompany,
        attributes: ['code', 'name'],
      },
      {
        model: this.db.projectModel,
        as: 'project',
        where: whereProject,
        attributes: ['code', 'name'],
      },
      {
        model: this.db.tradeModel,
        as: 'trade',
        where: whereTrade,
        attributes: [
          ['trade_code', 'code'],
          ['trade_name', 'name'],
        ],
      },
    ];

    if (
      tradeIds &&
      tradeIds.length > 0 &&
      projectIds &&
      projectIds.length > 0
    ) {
      whereClause[this.Sequelize.Op.and] = [
        this.Sequelize.literal(`EXISTS (
          SELECT 1 FROM projects_trades pt 
          WHERE pt.project_id = project.id 
          AND pt.trade_id = trade.id 
          AND pt.trade_id IN (${tradeIds.join(',')})
          AND pt.project_id IN (${projectIds.join(',')})
        )`),
      ];
    }

    return await this.findAll({
      limit,
      page,
      order,
      paginate,
      where: whereClause,
      include: includes,
    });
  }

  async getAllOfmListItems(payload) {
    let whereClause = {};
    const {
      search,
      ofmListId,
      itemCd,
      companyCode,
      projectCode,
      tradeCode,
      paginate,
      limit,
      page,
      order = [['created_at', 'DESC']],
    } = payload;
    if (ofmListId) {
      whereClause.ofm_list_id = ofmListId;
    }
    return await this.db.ofmListItemModel
      .findAndCountAll({
        where: whereClause,
        paginate,
        limit: limit,
        offset: ((parseInt(page) || 1) - 1) * (parseInt(limit) || 10),
        order,
        include: [
          {
            model: this.db.ofmItemListModel,
            as: 'ofmList',
            include: [
              {
                model: this.db.companyModel,
                as: 'company',
                attributes: ['code', 'name'],
              },
              {
                model: this.db.projectModel,
                as: 'project',
                attributes: ['code', 'name'],
              },
              {
                model: this.db.tradeModel,
                as: 'trade',
                attributes: [
                  ['trade_code', 'code'],
                  ['trade_name', 'name'],
                ],
              },
            ],
          },
          {
            model: this.db.itemModel,
            as: 'item',
            attributes: [
              'id',
              'itemCd',
              'itmDes',
              'unit',
              'acctCd',
              'remainingGfq',
              'isSteelbars',
            ],
            include: [
              {
                model: this.db.steelbarsModel,
                as: 'steelbars',
                required: false,
              },
            ],
          },
        ],
      })
      .then((result) => ({
        data: result.rows.map((row) => row.get({ plain: true })),
        total: result.count,
      }));
  }

  async getOfmItemListDetails(id) {
    try {
      const ofmItemList = await this.tableName.findByPk(id, {
        include: [
          {
            model: this.db.companyModel,
            as: 'company',
            attributes: ['code', 'name'],
            required: false,
          },
          {
            model: this.tableName.sequelize.model('projects'),
            as: 'project',
            attributes: ['id', ['code', 'code'], ['name', 'name']],
            required: false,
          },
          {
            model: this.tableName.sequelize.model('trades'),
            as: 'trade',
            attributes: ['id', ['trade_code', 'code'], ['trade_name', 'name']],
            required: false,
          },
        ],
        raw: true,
        nest: true,
      });

      if (!ofmItemList) {
        throw this.clientErrors.NOT_FOUND({
          message: `OFM Item List with id of ${id} not found`,
        });
      }

      return {
        ...ofmItemList,
        project: ofmItemList.project?.code ? ofmItemList.project : null,
        trade: ofmItemList.trade?.code ? ofmItemList.trade : null,
      };
    } catch (error) {
      this.fastify.log.error(
        `Error in getOfmItemListDetails: ${JSON.stringify(error)}`,
      );
      throw error;
    }
  }

  async getOfmItemsByListIdDetails(id, payload) {
    let whereClause = {
      ofmListId: id,
    };

    const { search, limit = 10, page = 1, order, filterBy } = payload;

    const parsedLimit = parseInt(limit);
    const parsedPage = parseInt(page);
    const offset = (parsedPage - 1) * parsedLimit;

    let itemWhereClause = {};
    if (search) {
      itemWhereClause.itm_des = {
        [this.Sequelize.Op.iLike]: `%${search}%`,
      };
    }

    if (filterBy) {
      Object.entries(filterBy).forEach(([key, value]) => {
        if (['itmDes', 'acctCd', 'unit', 'gfq'].includes(key)) {
          itemWhereClause[key] = {
            [this.Sequelize.Op.iLike]: `%${value}%`,
          };
        } else {
          whereClause[key] = value;
        }
      });
    }

    const parsedOrder = Array.isArray(order)
      ? order.map(([field, direction]) => {
          if (['itmDes', 'acctCd', 'unit', 'gfq'].includes(field)) {
            return [{ model: this.db.itemModel, as: 'item' }, field, direction];
          }
          return [field, direction];
        })
      : [['createdAt', 'DESC']];

    const itemInclude = {
      model: this.db.itemModel,
      as: 'item',
      required: Object.keys(itemWhereClause).length > 0,
      where: Object.keys(itemWhereClause).length ? itemWhereClause : undefined,
      include: [
        {
          model: this.db.steelbarsModel,
          as: 'steelbars',
          required: false,
        },
      ],
    };

    return await this.db.ofmListItemModel
      .findAndCountAll({
        limit: parsedLimit,
        offset: offset,
        order: parsedOrder,
        where: whereClause,
        include: [itemInclude],
        subQuery: false,
      })
      .then((result) => ({
        data: result.rows.map((row) => row.get({ plain: true })),
        total: result.count,
      }));
  }
}

module.exports = OfmItemListRepository;
