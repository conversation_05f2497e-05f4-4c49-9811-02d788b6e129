const BaseRepository = require('./baseRepository');

class NoteRepository extends BaseRepository {
  constructor({ db, clientErrors }) {
    super(db.noteModel);
    this.db = db;
    this.clientErrors = clientErrors;
  }

  async getByModel(model, modelId, dateRange) {
    const whereQuery = {
      model,
      model_id: modelId,
    };

    if (dateRange.startDate && dateRange.endDate) {
      const startDate = new Date(dateRange.startDate);
      const endDate = new Date(dateRange.endDate);
      startDate.setUTCHours(0, 0, 0, 0);
      endDate.setUTCHours(23, 59, 59, 999);
      whereQuery.createdAt = {
        [this.db.Sequelize.Op.between]: [startDate, endDate],
      };
    }
    const notes = await this.findAll({
      where: whereQuery,
      order: [['createdAt', 'DESC']],
      include: [
        {
          association: 'badges',
          required: false,
        },
      ],
    });

    return notes.data;
  }
}

module.exports = NoteRepository;
