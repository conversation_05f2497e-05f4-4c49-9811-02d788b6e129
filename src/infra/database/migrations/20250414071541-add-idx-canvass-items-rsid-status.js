'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addIndex(
      'canvass_items',
      ['requisition_id', 'status'],
      {
        name: 'idx_canvass_items_req_id_status',
        using: 'BTREE',
        concurrently: true,
      },
    );
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeIndex(
      'canvass_items',
      'idx_canvass_items_req_id_status',
    );
  },
};
