'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('requisitions', 'type', {
      type: Sequelize.STRING,
      allowNull: false,
      defaultValue: 'ofm',
      before: 'created_at',
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('requisitions', 'type');
  },
};
