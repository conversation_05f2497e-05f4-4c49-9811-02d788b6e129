module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('users', 'department_id', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'departments',
        key: 'id',
      },
      validate: {
        isInt: true,
      },
      onDelete: 'RESTRICT',
      onUpdate: 'CASCADE',
    });
  },

  async down(queryInterface, _Sequelize) {
    await queryInterface.removeColumn('users', 'department_id');
  },
};
