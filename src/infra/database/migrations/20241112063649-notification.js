'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('notifications', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      title: {
        type: Sequelize.STRING(100),
        allowNull: false,
      },
      message: {
        type: Sequelize.TEXT,
        allowNull: false,
      },
      type: {
        type: Sequelize.STRING(50),
        allowNull: false,
      },
      recipientRoleId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'recipient_role_id',
        references: {
          model: 'roles',
          key: 'id',
        },
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
      },
      recipientUserIds: {
        type: Sequelize.ARRAY(Sequelize.INTEGER),
        allowNull: true,
        defaultValue: [],
        field: 'recipient_user_ids',
      },
      senderId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'sender_id',
        references: {
          model: 'users',
          key: 'id',
        },
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
      },
      viewedBy: {
        type: Sequelize.ARRAY(Sequelize.INTEGER),
        allowNull: false,
        defaultValue: [],
        field: 'viewed_by',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('now'),
        field: 'created_at',
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('now'),
        field: 'updated_at',
      },
      deletedAt: {
        type: Sequelize.DATE,
        allowNull: true,
        field: 'deleted_at',
      },
    });

    await queryInterface.addIndex('notifications', ['recipient_role_id'], {
      name: 'notifications_recipient_role_id_idx',
    });

    await queryInterface.addIndex('notifications', ['type'], {
      name: 'notifications_type_idx',
    });

    await queryInterface.addIndex('notifications', ['created_at'], {
      name: 'notifications_created_at_idx',
    });

    await queryInterface.addIndex('notifications', ['recipient_user_ids'], {
      name: 'notifications_recipient_user_ids_idx',
      using: 'GIN',
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeIndex('notifications', 'notifications_type_idx');
    await queryInterface.removeIndex(
      'notifications',
      'notifications_recipient_role_id_idx',
    );
    await queryInterface.removeIndex(
      'notifications',
      'notifications_created_at_idx',
    );

    await queryInterface.dropTable('notifications');
  },
};
