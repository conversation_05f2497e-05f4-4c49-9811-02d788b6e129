'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    console.log(
      'Starting migration to fix delivery receipt statuses linked to invoices...',
    );

    // Update delivery receipts that have invoice_id but still have Draft status
    await queryInterface.sequelize.query(`
      UPDATE delivery_receipts 
      SET status = 'Delivered'
      WHERE status = 'Draft' 
      AND invoice_id IS NOT NULL
    `);

    console.log(
      'Successfully updated delivery receipt statuses for records linked to invoices.',
    );
  },

  async down(queryInterface, Sequelize) {
    // Revert back to Draft status for records that were updated
    await queryInterface.sequelize.query(`
      UPDATE delivery_receipts 
      SET status = 'Draft'
      WHERE status = 'Delivered' 
      AND invoice_id IS NOT NULL
    `);
  },
};
