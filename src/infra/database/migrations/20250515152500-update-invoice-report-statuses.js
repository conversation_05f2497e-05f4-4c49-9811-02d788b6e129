'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Update existing records
    await queryInterface.sequelize.query(`
      UPDATE invoice_reports 
      SET status = CASE 
        WHEN is_draft = true THEN 'IR Draft' 
        ELSE 'Invoice Received' 
      END
    `);
  },

  async down(queryInterface, Sequelize) {
    // No need to revert as we're just updating statuses to match the model logic
  },
};
