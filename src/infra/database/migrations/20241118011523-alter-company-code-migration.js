'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.sequelize.query(
        'ALTER TABLE projects DROP CONSTRAINT IF EXISTS projects_company_code_fkey;',
      );
    } catch (error) {
      console.log('No constraint to remove');
    }

    await queryInterface.removeColumn('projects', 'company_code');
    await queryInterface.addColumn('projects', 'company_code', {
      type: Sequelize.INTEGER,
      allowNull: true,
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('projects', 'company_code');
    await queryInterface.addColumn('projects', 'company_code', {
      type: Sequelize.INTEGER,
      allowNull: true,
    });
  },
};
