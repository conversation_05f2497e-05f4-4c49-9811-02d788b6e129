'use strict';

const {
  CANVASS_APPROVER_STATUS,
} = require('../../../domain/constants/canvassConstants');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('canvass_approvers', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      canvassRequisitionId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'canvass_requisitions',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'canvass_requisition_id',
      },
      level: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'user_id',
      },
      status: {
        allowNull: false,
        type: Sequelize.STRING(255),
        defaultValue: CANVASS_APPROVER_STATUS.PENDING,
      },
      roleId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'roles',
          key: 'id',
        },
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
        field: 'role_id',
      },
      isAdhoc: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        field: 'is_adhoc',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('now'),
        field: 'created_at',
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('now'),
        field: 'updated_at',
      },
    });

    await queryInterface.addIndex('canvass_approvers', [
      'canvass_requisition_id',
    ]);
    await queryInterface.addIndex('canvass_approvers', ['user_id']);
    await queryInterface.addIndex('canvass_approvers', ['role_id']);
    await queryInterface.addIndex('canvass_approvers', ['level']);

    await queryInterface.addIndex(
      'canvass_approvers',
      ['canvass_requisition_id', 'user_id'],
      {
        name: 'canvass_approver',
        where: {
          user_id: {
            [Sequelize.Op.ne]: null,
          },
        },
      },
    );

    await queryInterface.addIndex(
      'canvass_approvers',
      ['canvass_requisition_id', 'role_id'],
      {
        name: 'unique_canvass_role',
        where: {
          role_id: {
            [Sequelize.Op.ne]: null,
          },
        },
      },
    );
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('canvass_approvers');
  },
};
