'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    const constraintsQuery = `
      SELECT conname
      FROM pg_constraint
      WHERE contype = 'f'
      AND conrelid = (
        SELECT oid
        FROM pg_class
        WHERE relname = 'attachment_badges'
      )
    `;
    const result = await queryInterface.sequelize.query(constraintsQuery);
    if (result && result[0].length) {
      const constraintNames = result[0].map(({ conname }) => conname);
      if (constraintNames.includes('attachment_badges_attachment_id_fkey')) {
        await queryInterface.removeConstraint(
          'attachment_badges',
          'attachment_badges_attachment_id_fkey',
        );
        await queryInterface.addConstraint('attachment_badges', {
          name: 'attachment_badges_attachment_id_fkey',
          type: 'FOREIGN KEY',
          fields: ['attachment_id'],
          references: {
            table: 'attachments',
            field: 'id',
          },
          onDelete: 'CASCADE',
        });
      }
    }
  },

  down: async (queryInterface, Sequelize) => {
    const constraintsQuery = `
      SELECT conname
      FROM pg_constraint
      WHERE contype = 'f'
      AND conrelid = (
        SELECT oid
        FROM pg_class
        WHERE relname = 'attachment_badges'
      )
    `;
    const result = await queryInterface.sequelize.query(constraintsQuery);
    if (result && result[0].length) {
      await queryInterface.removeConstraint(
        'attachment_badges',
        'attachment_badges_attachment_id_fkey',
      );
      await queryInterface.addConstraint('attachment_badges', {
        name: 'attachment_badges_attachment_id_fkey',
        type: 'FOREIGN KEY',
        fields: ['attachment_id'],
        references: {
          table: 'attachments',
          field: 'id',
        },
      });
    }
  },
};
