'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.removeColumn('histories', 'company');
    await queryInterface.removeColumn('histories', 'project');

    await queryInterface.addColumn('histories', 'company_id', {
      type: Sequelize.INTEGER,
      allowNull: true,
      defaultValue: null,
      field: 'company_id',
      references: {
        model: 'companies',
        key: 'id',
      },
    });

    await queryInterface.addIndex('histories', ['company_id']);

    await queryInterface.addColumn('histories', 'project_id', {
      type: Sequelize.INTEGER,
      allowNull: true,
      defaultValue: null,
      field: 'project_id',
      references: {
        model: 'projects',
        key: 'id',
      },
    });

    await queryInterface.addIndex('histories', ['project_id']);

    await queryInterface.addColumn('histories', 'department_id', {
      type: Sequelize.INTEGER,
      allowNull: true,
      defaultValue: null,
      field: 'department_id',
      references: {
        model: 'departments',
        key: 'id',
      },
    });

    await queryInterface.addIndex('histories', ['department_id']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('histories', 'company_id');
    await queryInterface.removeColumn('histories', 'project_id');
    await queryInterface.removeColumn('histories', 'department_id');
  },
};
