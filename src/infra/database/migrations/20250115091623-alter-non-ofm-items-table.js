'use strict';
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.removeColumn('non_ofm_items', 'company_code');
    await queryInterface.removeColumn('non_ofm_items', 'trade_code');
    await queryInterface.removeColumn('non_ofm_items', 'project_code');
    await queryInterface.renameColumn('non_ofm_items', 'description', 'notes');
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.addColumn('non_ofm_items', 'company_code', {
      type: Sequelize.INTEGER,
      allowNull: true,
    });
    await queryInterface.addColumn('non_ofm_items', 'trade_code', {
      type: Sequelize.INTEGER,
      allowNull: true,
    });
    await queryInterface.addColumn('non_ofm_items', 'project_code', {
      type: Sequelize.STRING(20),
      allowNull: true,
    });
    await queryInterface.renameColumn('non_ofm_items', 'notes', 'description');
  },
};
