'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('steelbar_matrix', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      grade: {
        type: Sequelize.INTEGER,
      },
      diameter: {
        type: Sequelize.INTEGER,
      },
      weight: {
        type: Sequelize.DECIMAL(10, 3),
      },
      length: {
        type: Sequelize.DECIMAL(10, 1),
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('now'),
        field: 'created_at',
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('now'),
        field: 'updated_at',
      },
    });

    await queryInterface.addIndex(
      'steelbar_matrix',
      ['grade', 'diameter', 'weight', 'length'],
      {
        type: 'UNIQUE',
        name: 'steelbar_matrix_compound_index',
      },
    );
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('steelbar_matrix');
  },
};
