'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.changeColumn('delivery_receipt_items', 'notes', {
      type: Sequelize.TEXT,
    });
    await queryInterface.changeColumn('delivery_receipts', 'note', {
      type: Sequelize.TEXT,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.changeColumn('delivery_receipt_items', 'notes', {
      type: Sequelize.STRING(255),
    });
    await queryInterface.changeColumn('delivery_receipts', 'note', {
      type: Sequelize.STRING(255),
    });
  },
};
