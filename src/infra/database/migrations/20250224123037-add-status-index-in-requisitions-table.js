'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const tableName = 'requisitions';
    const indexName = 'requisitions_status_index';
    const fields = ['status'];

    const indexExists = await queryInterface.sequelize.query(
      `SELECT 1 FROM pg_indexes WHERE tablename = '${tableName}' AND indexname = '${indexName}'`,
    );

    if (indexExists[0].length === 0) {
      await queryInterface.addIndex(tableName, fields, {
        name: indexName,
      });
    }
  },

  async down(queryInterface, Sequelize) {
    const tableName = 'requisitions';
    const indexName = 'requisitions_status_index';

    const indexExists = await queryInterface.sequelize.query(
      `SELECT 1 FROM pg_indexes WHERE tablename = '${tableName}' AND indexname = '${indexName}'`,
    );

    if (indexExists[0].length > 0) {
      await queryInterface.removeIndex(tableName, indexName);
    }
  },
};
