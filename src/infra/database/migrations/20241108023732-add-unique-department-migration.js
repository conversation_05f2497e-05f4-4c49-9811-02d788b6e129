'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addConstraint('departments', {
      fields: ['code'],
      type: 'unique',
      name: 'unique_department_code_per_department',
    });
  },

  async down(queryInterface, _Sequelize) {
    await queryInterface.removeConstraint(
      'departments',
      'unique_department_code_per_department',
    );
  },
};
