'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('purchase_order_items', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      purchaseOrderId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'purchase_orders',
          key: 'id',
        },
        onDelete: 'CASCADE',
        field: 'purchase_order_id',
      },
      canvassItemId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'canvass_items',
          key: 'id',
        },
        field: 'canvass_item_id',
      },
      requisitionItemListId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'requisition_item_list_id',
      },
      quantityPurchased: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
        field: 'quantity_purchased',
      },
      canvassItemSupplierId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'canvass_item_suppliers',
          key: 'id',
        },
        field: 'canvass_item_supplier_id',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('now'),
        field: 'created_at',
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('now'),
        field: 'updated_at',
      },
    });

    await queryInterface.addIndex('purchase_order_items', [
      'purchase_order_id',
    ]);
    await queryInterface.addIndex('purchase_order_items', ['canvass_item_id']);
    await queryInterface.addIndex('purchase_order_items', [
      'requisition_item_list_id',
    ]);
    await queryInterface.addIndex('purchase_order_items', [
      'canvass_item_supplier_id',
    ]);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeIndex('purchase_order_items', [
      'purchase_order_id',
    ]);
    await queryInterface.removeIndex('purchase_order_items', [
      'canvass_item_id',
    ]);
    await queryInterface.removeIndex('purchase_order_items', [
      'requisition_item_list_id',
    ]);
    await queryInterface.removeIndex('purchase_order_items', [
      'canvass_item_supplier_id',
    ]);

    await queryInterface.dropTable('purchase_order_items');
  },
};
