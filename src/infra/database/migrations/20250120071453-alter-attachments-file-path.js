'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.changeColumn('attachments', 'fileName', {
      type: Sequelize.TEXT,
      field: 'file_name',
      allowNull: false,
    });

    await queryInterface.changeColumn('attachments', 'path', {
      type: Sequelize.TEXT,
      field: 'path',
      allowNull: false,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.changeColumn('attachments', 'fileName', {
      type: Sequelize.STRING(199),
      allowNull: false,
      field: 'file_name',
    });

    await queryInterface.changeColumn('attachments', 'path', {
      type: Sequelize.STRING(100),
      allowNull: false,
      field: 'path',
    });
  },
};
