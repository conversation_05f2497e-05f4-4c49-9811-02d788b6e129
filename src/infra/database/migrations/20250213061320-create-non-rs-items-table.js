'use strict';

const {
  NON_RS_DISCOUNT_TYPE,
} = require('../../../domain/constants/nonRSConstants');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('non_requisition_items', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      nonRequisitionId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'non_requisitions',
          key: 'id',
        },
        validate: {
          isInt: true,
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'non_requisition_id',
      },
      name: {
        type: Sequelize.STRING(255),
        allowNull: false,
      },
      quantity: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      amount: {
        type: Sequelize.DOUBLE,
        allowNull: true,
      },
      discountValue: {
        type: Sequelize.DOUBLE,
        allowNull: false,
        field: 'discount_value',
      },
      discountType: {
        type: Sequelize.ENUM,
        allowNull: false,
        defaultValue: NON_RS_DISCOUNT_TYPE.FIXED,
        values: Object.values(NON_RS_DISCOUNT_TYPE),
        field: 'discount_type',
      },
      discountedPrice: {
        type: Sequelize.DOUBLE,
        allowNull: false,
        field: 'discounted_price',
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('now'),
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('now'),
        onUpdate: Sequelize.fn('now'),
      },
    });

    await queryInterface.addIndex(
      'non_requisition_items',
      ['non_requisition_id'],
      {
        name: 'non_requisition_items_non_requisition_id_index',
      },
    );

    await queryInterface.addIndex('non_requisition_items', ['name'], {
      name: 'non_requisition_items_name_index',
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('non_requisition_items');
  },
};
