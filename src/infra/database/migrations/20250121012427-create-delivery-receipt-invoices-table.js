'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('delivery_receipt_invoices', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      delivery_receipt_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'delivery_receipts',
          key: 'id',
        },
      },
      invoice_no: {
        type: Sequelize.STRING,
      },
      issued_invoice_date: {
        type: Sequelize.DATE,
      },
      total_sales: {
        type: Sequelize.DECIMAL(20, 2),
      },
      vat_amount: {
        type: Sequelize.DECIMAL(20, 2),
      },
      vat_exempted_amount: {
        type: Sequelize.DECIMAL(20, 2),
      },
      zero_rated_amount: {
        type: Sequelize.DECIMAL(20, 2),
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('now'),
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('now'),
        onUpdate: Sequelize.fn('now'),
      },
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('delivery_receipt_invoices');
  },
};
