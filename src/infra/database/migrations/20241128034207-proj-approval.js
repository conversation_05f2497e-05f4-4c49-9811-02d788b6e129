module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('project_approvals', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      projectId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'projects',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'project_id',
      },
      approvalTypeCode: {
        type: Sequelize.STRING(50),
        allowNull: false,
        references: {
          model: 'approval_types',
          key: 'code',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'approval_type_code',
      },
      level: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      isOptional: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        field: 'is_optional',
      },
      approverId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        field: 'approver_id',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('now'),
        field: 'created_at',
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('now'),
        field: 'updated_at',
      },
    });

    await queryInterface.addIndex('project_approvals', ['project_id']);
    await queryInterface.addIndex('project_approvals', ['approval_type_code']);
    await queryInterface.addIndex('project_approvals', ['approver_id']);
    await queryInterface.addIndex('project_approvals', ['level']);
    await queryInterface.addIndex(
      'project_approvals',
      ['project_id', 'approval_type_code'],
      {
        name: 'project_approvals_proj_type_idx',
      },
    );
  },

  async down(queryInterface) {
    await queryInterface.dropTable('project_approvals');
  },
};
