'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('companies', 'category', {
      type: Sequelize.ENUM('company', 'association'),
      allowNull: false,
      defaultValue: 'company',
    });

    await queryInterface.sequelize.query(`
      UPDATE companies 
      SET category = 'company' 
      WHERE category IS NULL
    `);
  },

  async down(queryInterface, Sequelize) {},
};
