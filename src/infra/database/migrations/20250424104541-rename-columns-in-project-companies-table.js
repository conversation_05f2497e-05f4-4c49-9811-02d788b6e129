'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const table = await queryInterface.describeTable('project_companies');

    if (table.projectId) {
      await queryInterface.renameColumn(
        'project_companies',
        'projectId',
        'project_id',
      );
    }

    if (table.companyId) {
      await queryInterface.renameColumn(
        'project_companies',
        'companyId',
        'company_id',
      );
    }
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.renameColumn(
      'project_companies',
      'project_id',
      'projectId',
    );
    await queryInterface.renameColumn(
      'project_companies',
      'company_id',
      'companyId',
    );
  },
};
