'use strict';

/*
 * Note: Lossy approach, since there's no more partial canvassing with the new statuses, the down migration will not return the original status for those records.
 * Old Statuses -> New Statuses Mapping:
 * 'draft'               -> 'cs_draft'
 * 'for_approval'        -> 'for_cs_approval'
 * 'partially_canvassed' -> 'for_cs_approval'
 * 'approved'            -> 'cs_approved'
 * 'rejected'            -> 'cs_rejected'
 */

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    try {
      console.log(
        'Starting migration to update canvass_requisitions statuses...',
      );
      await queryInterface.sequelize.query(`
        UPDATE canvass_requisitions
        SET "status" = CASE
          WHEN "status" = 'draft' then 'cs_draft'
          WHEN "status" = 'partially_canvassed' then 'for_cs_approval'
          WHEN "status" = 'for_approval' then 'for_cs_approval'
          WHEN "status" = 'approved' THEN 'cs_approved'
          WHEN "status" = 'rejected' THEN 'cs_rejected'
          ELSE "status"
        END
        WHERE "status" IN ('draft', 'for_approval', 'partially_canvassed', 'approved', 'rejected');
      `);
      console.log('Successfully updated canvass_requisitions statuses.');
    } catch (error) {
      console.error('Failed to migrate canvass_requisitions statuses:', error);
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    try {
      console.log('Reverting migration for canvass_requisitions statuses...');

      await queryInterface.sequelize.query(`
        UPDATE "canvass_requisitions"
        SET "status" = CASE
            WHEN "status" = 'cs_draft' THEN 'draft'
            WHEN "status" = 'for_cs_approval' THEN 'for_approval'
            WHEN "status" = 'cs_approved' THEN 'approved'
            WHEN "status" = 'cs_rejected' THEN 'rejected'
            ELSE "status"
        END
        WHERE "status" IN ('cs_draft', 'for_cs_approval', 'cs_approved', 'cs_rejected');
      `);
      console.log('Successfully reverted canvass_requisitions statuses.');
    } catch (error) {
      console.error('Failed to revert canvass_requisitions statuses:', error);
      throw error;
    }
  },
};
