'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Check existing columns in requisitions table
    const requisitionsTableInfo =
      await queryInterface.describeTable('requisitions');

    // Add force close tracking fields to requisitions table (only if they don't exist)
    if (!requisitionsTableInfo.force_closed_at) {
      await queryInterface.addColumn('requisitions', 'force_closed_at', {
        type: Sequelize.DATE,
        allowNull: true,
        field: 'force_closed_at',
      });
    }

    if (!requisitionsTableInfo.force_closed_by) {
      await queryInterface.addColumn('requisitions', 'force_closed_by', {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'force_closed_by',
        references: {
          model: 'users',
          key: 'id',
        },
      });
    }

    if (!requisitionsTableInfo.force_close_reason) {
      await queryInterface.addColumn('requisitions', 'force_close_reason', {
        type: Sequelize.TEXT,
        allowNull: true,
        field: 'force_close_reason',
      });
    }

    if (!requisitionsTableInfo.force_close_scenario) {
      await queryInterface.addColumn('requisitions', 'force_close_scenario', {
        type: Sequelize.STRING(50),
        allowNull: true,
        field: 'force_close_scenario',
      });
    }

    // Check existing columns in purchase_orders table
    const purchaseOrdersTableInfo =
      await queryInterface.describeTable('purchase_orders');

    // Add enhanced PO tracking fields to purchase_orders table (only if they don't exist)
    if (!purchaseOrdersTableInfo.system_generated_notes) {
      await queryInterface.addColumn(
        'purchase_orders',
        'system_generated_notes',
        {
          type: Sequelize.TEXT,
          allowNull: true,
          field: 'system_generated_notes',
        },
      );
    }

    if (!purchaseOrdersTableInfo.original_amount) {
      await queryInterface.addColumn('purchase_orders', 'original_amount', {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: true,
        field: 'original_amount',
      });
    }

    if (!purchaseOrdersTableInfo.original_quantity) {
      await queryInterface.addColumn('purchase_orders', 'original_quantity', {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'original_quantity',
      });
    }

    // Check existing columns in canvass_requisitions table
    const canvassRequisitionsTableInfo = await queryInterface.describeTable(
      'canvass_requisitions',
    );

    // Add cancellation tracking to canvass_requisitions table (canvass sheets) (only if they don't exist)
    if (!canvassRequisitionsTableInfo.cancelled_at) {
      await queryInterface.addColumn('canvass_requisitions', 'cancelled_at', {
        type: Sequelize.DATE,
        allowNull: true,
        field: 'cancelled_at',
      });
    }

    if (!canvassRequisitionsTableInfo.cancelled_by) {
      await queryInterface.addColumn('canvass_requisitions', 'cancelled_by', {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'cancelled_by',
        references: {
          model: 'users',
          key: 'id',
        },
      });
    }

    if (!canvassRequisitionsTableInfo.cancellation_reason) {
      await queryInterface.addColumn(
        'canvass_requisitions',
        'cancellation_reason',
        {
          type: Sequelize.STRING(100),
          allowNull: true,
          field: 'cancellation_reason',
        },
      );
    }

    // Check existing columns in invoice_reports table
    const invoiceReportsTableInfo =
      await queryInterface.describeTable('invoice_reports');

    // Add cancellation tracking to invoice_reports table (only if they don't exist)
    if (!invoiceReportsTableInfo.cancelled_at) {
      await queryInterface.addColumn('invoice_reports', 'cancelled_at', {
        type: Sequelize.DATE,
        allowNull: true,
        field: 'cancelled_at',
      });
    }

    if (!invoiceReportsTableInfo.cancelled_by) {
      await queryInterface.addColumn('invoice_reports', 'cancelled_by', {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'cancelled_by',
        references: {
          model: 'users',
          key: 'id',
        },
      });
    }

    if (!invoiceReportsTableInfo.cancellation_reason) {
      await queryInterface.addColumn('invoice_reports', 'cancellation_reason', {
        type: Sequelize.STRING(100),
        allowNull: true,
        field: 'cancellation_reason',
      });
    }

    // Check existing columns in delivery_receipts table
    const deliveryReceiptsTableInfo =
      await queryInterface.describeTable('delivery_receipts');

    // Add cancellation tracking to delivery_receipts table (only if they don't exist)
    if (!deliveryReceiptsTableInfo.cancelled_at) {
      await queryInterface.addColumn('delivery_receipts', 'cancelled_at', {
        type: Sequelize.DATE,
        allowNull: true,
        field: 'cancelled_at',
      });
    }

    if (!deliveryReceiptsTableInfo.cancelled_by) {
      await queryInterface.addColumn('delivery_receipts', 'cancelled_by', {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'cancelled_by',
        references: {
          model: 'users',
          key: 'id',
        },
      });
    }

    if (!deliveryReceiptsTableInfo.cancellation_reason) {
      await queryInterface.addColumn(
        'delivery_receipts',
        'cancellation_reason',
        {
          type: Sequelize.STRING(100),
          allowNull: true,
          field: 'cancellation_reason',
        },
      );
    }

    // Check existing columns in rs_payment_requests table
    const rsPaymentRequestsTableInfo = await queryInterface.describeTable(
      'rs_payment_requests',
    );

    // Add cancellation tracking to rs_payment_requests table (only if they don't exist)
    if (!rsPaymentRequestsTableInfo.cancelled_at) {
      await queryInterface.addColumn('rs_payment_requests', 'cancelled_at', {
        type: Sequelize.DATE,
        allowNull: true,
        field: 'cancelled_at',
      });
    }

    if (!rsPaymentRequestsTableInfo.cancelled_by) {
      await queryInterface.addColumn('rs_payment_requests', 'cancelled_by', {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'cancelled_by',
        references: {
          model: 'users',
          key: 'id',
        },
      });
    }

    if (!rsPaymentRequestsTableInfo.cancellation_reason) {
      await queryInterface.addColumn(
        'rs_payment_requests',
        'cancellation_reason',
        {
          type: Sequelize.STRING(100),
          allowNull: true,
          field: 'cancellation_reason',
        },
      );
    }

    // Create force_close_logs table for comprehensive audit trail (only if it doesn't exist)
    const tables = await queryInterface.showAllTables();
    if (!tables.includes('force_close_logs')) {
      await queryInterface.createTable('force_close_logs', {
        id: {
          type: Sequelize.INTEGER,
          primaryKey: true,
          autoIncrement: true,
        },
        requisitionId: {
          type: Sequelize.INTEGER,
          allowNull: false,
          field: 'requisition_id',
          references: {
            model: 'requisitions',
            key: 'id',
          },
        },
        userId: {
          type: Sequelize.INTEGER,
          allowNull: false,
          field: 'user_id',
          references: {
            model: 'users',
            key: 'id',
          },
        },
        scenarioType: {
          type: Sequelize.STRING(50),
          allowNull: false,
          field: 'scenario_type',
        },
        validationPath: {
          type: Sequelize.STRING(50),
          allowNull: false,
          field: 'validation_path',
        },
        quantitiesAffected: {
          type: Sequelize.JSON,
          allowNull: true,
          field: 'quantities_affected',
        },
        documentsCancelled: {
          type: Sequelize.JSON,
          allowNull: true,
          field: 'documents_cancelled',
        },
        poAdjustments: {
          type: Sequelize.JSON,
          allowNull: true,
          field: 'po_adjustments',
        },
        forceCloseNotes: {
          type: Sequelize.TEXT,
          allowNull: false,
          field: 'force_close_notes',
        },
        createdAt: {
          type: Sequelize.DATE,
          allowNull: false,
          field: 'created_at',
          defaultValue: Sequelize.fn('now'),
        },
      });
    }

    // Add indices for better query performance
    await queryInterface.addIndex('requisitions', ['force_closed_at'], {
      name: 'idx_requisitions_force_closed_at',
    });

    await queryInterface.addIndex('requisitions', ['force_closed_by'], {
      name: 'idx_requisitions_force_closed_by',
    });

    await queryInterface.addIndex('requisitions', ['force_close_scenario'], {
      name: 'idx_requisitions_force_close_scenario',
    });

    await queryInterface.addIndex('canvass_requisitions', ['cancelled_at'], {
      name: 'idx_canvass_requisitions_cancelled_at',
    });

    await queryInterface.addIndex('canvass_requisitions', ['cancelled_by'], {
      name: 'idx_canvass_requisitions_cancelled_by',
    });

    await queryInterface.addIndex('invoice_reports', ['cancelled_at'], {
      name: 'idx_invoice_reports_cancelled_at',
    });

    await queryInterface.addIndex('invoice_reports', ['cancelled_by'], {
      name: 'idx_invoice_reports_cancelled_by',
    });

    await queryInterface.addIndex('delivery_receipts', ['cancelled_at'], {
      name: 'idx_delivery_receipts_cancelled_at',
    });

    await queryInterface.addIndex('delivery_receipts', ['cancelled_by'], {
      name: 'idx_delivery_receipts_cancelled_by',
    });

    await queryInterface.addIndex('rs_payment_requests', ['cancelled_at'], {
      name: 'idx_rs_payment_requests_cancelled_at',
    });

    await queryInterface.addIndex('rs_payment_requests', ['cancelled_by'], {
      name: 'idx_rs_payment_requests_cancelled_by',
    });

    await queryInterface.addIndex('force_close_logs', ['requisition_id'], {
      name: 'idx_force_close_logs_requisition_id',
    });

    await queryInterface.addIndex('force_close_logs', ['user_id'], {
      name: 'idx_force_close_logs_user_id',
    });

    await queryInterface.addIndex('force_close_logs', ['scenario_type'], {
      name: 'idx_force_close_logs_scenario_type',
    });

    await queryInterface.addIndex('force_close_logs', ['created_at'], {
      name: 'idx_force_close_logs_created_at',
    });
  },

  async down(queryInterface, Sequelize) {
    // Remove indices
    await queryInterface.removeIndex(
      'force_close_logs',
      'idx_force_close_logs_created_at',
    );
    await queryInterface.removeIndex(
      'force_close_logs',
      'idx_force_close_logs_scenario_type',
    );
    await queryInterface.removeIndex(
      'force_close_logs',
      'idx_force_close_logs_user_id',
    );
    await queryInterface.removeIndex(
      'force_close_logs',
      'idx_force_close_logs_requisition_id',
    );
    await queryInterface.removeIndex(
      'rs_payment_requests',
      'idx_rs_payment_requests_cancelled_by',
    );
    await queryInterface.removeIndex(
      'rs_payment_requests',
      'idx_rs_payment_requests_cancelled_at',
    );
    await queryInterface.removeIndex(
      'delivery_receipts',
      'idx_delivery_receipts_cancelled_by',
    );
    await queryInterface.removeIndex(
      'delivery_receipts',
      'idx_delivery_receipts_cancelled_at',
    );
    await queryInterface.removeIndex(
      'invoice_reports',
      'idx_invoice_reports_cancelled_by',
    );
    await queryInterface.removeIndex(
      'invoice_reports',
      'idx_invoice_reports_cancelled_at',
    );
    await queryInterface.removeIndex(
      'canvass_requisitions',
      'idx_canvass_requisitions_cancelled_by',
    );
    await queryInterface.removeIndex(
      'canvass_requisitions',
      'idx_canvass_requisitions_cancelled_at',
    );
    await queryInterface.removeIndex(
      'requisitions',
      'idx_requisitions_force_close_scenario',
    );
    await queryInterface.removeIndex(
      'requisitions',
      'idx_requisitions_force_closed_by',
    );
    await queryInterface.removeIndex(
      'requisitions',
      'idx_requisitions_force_closed_at',
    );

    // Drop force_close_logs table
    await queryInterface.dropTable('force_close_logs');

    // Remove columns from rs_payment_requests table
    await queryInterface.removeColumn(
      'rs_payment_requests',
      'cancellation_reason',
    );
    await queryInterface.removeColumn('rs_payment_requests', 'cancelled_by');
    await queryInterface.removeColumn('rs_payment_requests', 'cancelled_at');

    // Remove columns from delivery_receipts table
    await queryInterface.removeColumn(
      'delivery_receipts',
      'cancellation_reason',
    );
    await queryInterface.removeColumn('delivery_receipts', 'cancelled_by');
    await queryInterface.removeColumn('delivery_receipts', 'cancelled_at');

    // Remove columns from invoice_reports table
    await queryInterface.removeColumn('invoice_reports', 'cancellation_reason');
    await queryInterface.removeColumn('invoice_reports', 'cancelled_by');
    await queryInterface.removeColumn('invoice_reports', 'cancelled_at');

    // Remove columns from canvass_requisitions table
    await queryInterface.removeColumn(
      'canvass_requisitions',
      'cancellation_reason',
    );
    await queryInterface.removeColumn('canvass_requisitions', 'cancelled_by');
    await queryInterface.removeColumn('canvass_requisitions', 'cancelled_at');

    // Remove columns from purchase_orders table
    await queryInterface.removeColumn('purchase_orders', 'original_quantity');
    await queryInterface.removeColumn('purchase_orders', 'original_amount');
    await queryInterface.removeColumn(
      'purchase_orders',
      'system_generated_notes',
    );

    // Remove columns from requisitions table
    await queryInterface.removeColumn('requisitions', 'force_close_scenario');
    await queryInterface.removeColumn('requisitions', 'force_close_reason');
    await queryInterface.removeColumn('requisitions', 'force_closed_by');
    await queryInterface.removeColumn('requisitions', 'force_closed_at');
  },
};
