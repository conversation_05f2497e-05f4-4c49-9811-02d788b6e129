'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // remove seen_at column from comment_badges and attachment_badges tables;
    await queryInterface.removeColumn('comment_badges', 'seen_at');
    await queryInterface.removeColumn('attachment_badges', 'seen_at');

    // add composite index to userId and commentId
    await queryInterface.addIndex('comment_badges', ['user_id', 'comment_id']);
    await queryInterface.addIndex('attachment_badges', [
      'user_id',
      'attachment_id',
    ]);

    // add unique constraint to userId and commentId for comment_badges and attachment_badges tables
    await queryInterface.addConstraint('comment_badges', {
      fields: ['user_id', 'comment_id'],
      type: 'unique',
      name: 'unique_user_comment_constraint',
    });
    await queryInterface.addConstraint('attachment_badges', {
      fields: ['user_id', 'attachment_id'],
      type: 'unique',
      name: 'unique_user_attachment_constraint',
    });
  },

  async down(queryInterface, Sequelize) {
    // add seen_at column to comment_badges and attachment_badges tables
    await queryInterface.addColumn('comment_badges', 'seen_at', {
      type: Sequelize.DATE,
      field: 'seen_at',
    });
    await queryInterface.addColumn('attachment_badges', 'seen_at', {
      type: Sequelize.DATE,
      field: 'seen_at',
    });

    // remove composite index
    await queryInterface.removeIndex('comment_badges', [
      'user_id',
      'comment_id',
    ]);
    await queryInterface.removeIndex('attachment_badges', [
      'user_id',
      'attachment_id',
    ]);

    // remove unique constraint
    await queryInterface.removeConstraint(
      'comment_badges',
      'unique_user_comment_constraint',
    );
    await queryInterface.removeConstraint(
      'attachment_badges',
      'unique_user_attachment_constraint',
    );
  },
};
