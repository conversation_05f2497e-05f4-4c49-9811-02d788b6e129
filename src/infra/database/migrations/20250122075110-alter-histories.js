'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.removeConstraint(
      'histories',
      'histories_item_id_fkey',
    );
    await queryInterface.addIndex('histories', ['item_id'], {
      name: 'histories_item_id_idx',
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeIndex('histories', 'histories_item_id_idx');
  },
};
