'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.changeColumn(
      'purchase_order_items',
      'quantity_purchased',
      {
        type: Sequelize.DECIMAL(13, 3),
        allowNull: false,
      },
    );
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.changeColumn(
      'purchase_order_items',
      'quantity_purchased',
      {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
    );
  },
};
