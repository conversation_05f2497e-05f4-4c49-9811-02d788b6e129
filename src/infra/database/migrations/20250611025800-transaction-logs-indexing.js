'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      const indexes = await queryInterface.showIndex('transaction_logs', {
        transaction,
      });

      const userIdIndexName = 'transaction_logs_user_id';
      const rsIdIndexName = 'transaction_logs_rs_id';

      if (!indexes.some((index) => index.name === userIdIndexName)) {
        await queryInterface.addIndex('transaction_logs', ['user_id'], {
          name: userIdIndexName,
          transaction,
        });
      }

      if (!indexes.some((index) => index.name === rsIdIndexName)) {
        await queryInterface.addIndex('transaction_logs', ['rs_id'], {
          name: rsIdIndexName,
          transaction,
        });
      }

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeIndex(
      'transaction_logs',
      'transaction_logs_user_id',
    );
    await queryInterface.removeIndex(
      'transaction_logs',
      'transaction_logs_rs_id',
    );
  },
};
