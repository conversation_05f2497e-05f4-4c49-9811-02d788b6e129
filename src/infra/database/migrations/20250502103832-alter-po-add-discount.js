'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const table = await queryInterface.describeTable('purchase_orders');
    if (!table.added_discount) {
      await queryInterface.addColumn('purchase_orders', 'added_discount', {
        type: Sequelize.DECIMAL(20, 2),
        allowNull: true,
        defaultValue: 0,
        field: 'added_discount',
      });
    }

    if (!table.is_added_discount_fixed_amount) {
      await queryInterface.addColumn(
        'purchase_orders',
        'is_added_discount_fixed_amount',
        {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: false,
          field: 'is_added_discount_fixed_amount',
        },
      );
    }

    if (!table.is_added_discount_percentage) {
      await queryInterface.addColumn(
        'purchase_orders',
        'is_added_discount_percentage',
        {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: false,
          field: 'is_added_discount_percentage',
        },
      );
    }
  },

  async down(queryInterface, Sequelize) {
    const table = await queryInterface.describeTable('purchase_orders');

    if (table.added_discount) {
      await queryInterface.removeColumn('purchase_orders', 'added_discount');
    }

    if (table.is_added_discount_fixed_amount) {
      await queryInterface.removeColumn(
        'purchase_orders',
        'is_added_discount_fixed_amount',
      );
    }

    if (table.is_added_discount_percentage) {
      await queryInterface.removeColumn(
        'purchase_orders',
        'is_added_discount_percentage',
      );
    }
  },
};
