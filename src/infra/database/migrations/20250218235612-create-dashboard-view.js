'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async down(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(
      `DROP VIEW IF EXISTS vw_dashboard_requisitions`,
    );
  },
  async up(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(`
      CREATE OR REPLACE VIEW vw_dashboard_requisitions AS
        SELECT
          r.id AS requisition_id,
        CASE
          WHEN (r.id IS NOT NULL)
          AND (
            COALESCE(cs.id, po.id, dr.id, pr.id) IS NULL
          ) THEN r.id
          WHEN (cs.id IS NOT NULL)
          AND (
            COALESCE(po.id, dr.id, pr.id) IS NULL
          ) THEN cs.id
          WHEN (po.id IS NOT NULL)
          AND (
            COALESCE(dr.id, pr.id) IS NULL
          ) THEN po.id
          WHEN (dr.id IS NOT NULL)
            AND (pr.id IS NULL) THEN dr.id
          WHEN (pr.id IS NOT NULL) THEN pr.id
		    ELSE NULL
      END AS ref_id,
      CASE
        WHEN (r.id IS NOT NULL) THEN CASE
          WHEN r.status = 'draft' THEN 'RS-TMP-'
          ELSE 'RS-'
        END || r.company_code || r.rs_letter || COALESCE(
          r.rs_number,
          r.draft_rs_number
        )
        ELSE NULL
      END AS rs_combined_number,
      CASE
        WHEN (cs.id IS NOT NULL) THEN CASE
          WHEN cs.status = 'draft' THEN 'CS-TMP-'
          ELSE 'CS-'
        END || r.company_code || cs.cs_letter || COALESCE(
          cs.cs_number,
          cs.draft_cs_number
        )
	      ELSE NULL
      END AS canvass_number,
      CASE
        WHEN (po.id IS NOT NULL) THEN 'PO-' || r.company_code || po.po_letter || po.po_number
		    ELSE NULL
      END AS po_number,
      CASE
        WHEN (pr.id IS NOT NULL) THEN CASE
        WHEN pr.is_draft = true THEN 'PR-TMP-'
        ELSE 'PR-'
        END || r.company_code || pr.pr_letter || COALESCE(
          pr.pr_number,
          pr.draft_pr_number
        )
		    ELSE NULL
	    END AS pr_number,
      CASE
        WHEN (dr.id IS NOT NULL) THEN CASE
        WHEN dr.is_draft = true THEN 'RR-TMP-'
        ELSE 'RR-'
        END || COALESCE(
          dr.dr_number,
          dr.draft_dr_number 
        )
		    ELSE NULL
	    END AS dr_number,
    CONCAT( u.first_name,' ',
      u.last_name ) AS requestor_full_name,
      c.name AS company_name,
      p.name AS project_name,
      d.name AS department_name,
      r.type AS ref_type,
      r.status AS ref_status,
      r.created_at AS ref_created_at,
      r.updated_at AS ref_updated_at,
      r.status,
      cs.id AS cs_id,
      po.id AS po_id,
      dr.id AS dr_id,
      pr.id AS pr_id
    FROM
      requisitions r
      LEFT JOIN canvass_requisitions cs ON r.id = cs.requisition_id
      LEFT JOIN purchase_orders po ON r.id = po.requisition_id
      LEFT JOIN delivery_receipts dr ON r.id = dr.requisition_id
      LEFT JOIN rs_payment_requests pr ON r.id = pr.requisition_id
      LEFT JOIN users u ON r.created_by = u.id
      LEFT JOIN companies c ON r.company_id = c.id
      LEFT JOIN departments d ON r.department_id = d.id
      LEFT JOIN projects p ON r.project_id = p.id
    `);
  },
};
