'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('requisition_approvers', 'approver_level', {
      type: Sequelize.INTEGER,
      allowNull: false,
      defaultValue: 0,
    });

    // Update existing records with sequential approver levels
    await queryInterface.sequelize.query(`
      WITH ranked_approvers AS (
        SELECT 
          id,
          requisition_id,
          CASE
            -- Company and Association sequence
            WHEN model_type = 'user' AND level = 0 THEN 1
            WHEN model_type = 'department' AND level = 1 THEN 2
            WHEN model_type = 'department' AND is_optional_approver = true THEN 3
            
            -- Project sequence
            WHEN model_type = 'user' AND level = 0 THEN 1
            WHEN model_type = 'department' AND level = 1 THEN 2
            WHEN model_type = 'department' AND is_optional_approver = true THEN 3
            WHEN model_type = 'project' AND level = 1 THEN 4
            WHEN model_type = 'project' AND is_optional_approver = true THEN 5
            
            ELSE level + 6
          END AS sequence_order
        FROM requisition_approvers
      ),
      sequential_levels AS (
        SELECT 
          id,
          ROW_NUMBER() OVER (PARTITION BY requisition_id ORDER BY sequence_order) AS new_level
        FROM ranked_approvers
      )
      UPDATE requisition_approvers
      SET approver_level = sl.new_level
      FROM sequential_levels sl
      WHERE requisition_approvers.id = sl.id
    `);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn(
      'requisition_approvers',
      'approver_level',
    );
  },
};
