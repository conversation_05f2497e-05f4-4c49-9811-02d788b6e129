'use strict';
const { USER_STATUS } = require('../../../domain/constants/userConstants');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */

    await queryInterface.createTable('users', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      username: {
        type: Sequelize.STRING(255),
        allowNull: false,
        unique: true,
        validate: {
          len: [5, 50],
        },
        indexes: [{ unique: true, fields: ['username'] }],
      },
      email: {
        type: Sequelize.STRING(255),
        allowNull: false,
        unique: true,
        validate: {
          isEmail: true,
        },
        indexes: [{ unique: true, fields: ['email'] }],
      },
      password: {
        type: Sequelize.STRING(255),
        allowNull: false,
        validate: {
          len: [8, 255],
        },
      },
      firstName: {
        type: Sequelize.STRING(50),
        allowNull: true,
        field: 'first_name',
      },
      lastName: {
        type: Sequelize.STRING(50),
        allowNull: true,
        field: 'last_name',
      },
      roleId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'role_id',
        references: {
          model: 'roles',
          key: 'id',
        },
        validate: {
          isInt: true,
        },
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
      },
      otpSecret: {
        type: Sequelize.STRING(512),
        allowNull: true,
        field: 'otp_secret',
      },
      status: {
        type: Sequelize.ENUM,
        allowNull: false,
        defaultValue: USER_STATUS.ACTIVE,
        values: Object.values(USER_STATUS),
      },
      isPasswordTemporary: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true,
        field: 'is_password_temporary',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'created_at',
        defaultValue: Sequelize.fn('now'),
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'updated_at',
        defaultValue: Sequelize.fn('now'),
      },
      deletedAt: {
        type: Sequelize.DATE,
        allowNull: true,
        field: 'deleted_at',
      },
    });
  },

  async down(queryInterface) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
    await queryInterface.dropTable('users');
  },
};
