'use strict';
const { withTimescaleDBCompression } = require('../utils/timescale-db-migration-helper');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      await withTimescaleDBCompression(queryInterface, transaction, 'delivery_receipt_items_history', async () => {
        await queryInterface.changeColumn(
          'delivery_receipt_items_history',
          'qty_ordered',
          {
            type: Sequelize.DECIMAL(12, 3),
            allowNull: false,
          },
          { transaction },
        );

        await queryInterface.changeColumn(
          'delivery_receipt_items_history',
          'qty_delivered',
          {
            type: Sequelize.DECIMAL(12, 3),
            allowNull: false,
          },
          { transaction },
        );

        await queryInterface.changeColumn(
          'delivery_receipt_items_history',
          'qty_returned',
          {
            type: Sequelize.DECIMAL(12, 3),
            allowNull: false,
          },
          { transaction },
        );
      });

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    // Rollback logic would follow the same pattern if needed
    console.log('⚠️  Rollback not implemented for this migration');
  },
};
