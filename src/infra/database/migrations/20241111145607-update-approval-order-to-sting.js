'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.removeIndex('approvers', ['approval_order']);

    await queryInterface.changeColumn('approvers', 'approval_order', {
      type: Sequelize.STRING(100),
      allowNull: false,
    });

    await queryInterface.addIndex('approvers', ['approval_order']);
  },

  async down(queryInterface, Sequelize) {
    try {
      await queryInterface.removeIndex('approvers', ['approval_order']);

      await queryInterface.changeColumn('approvers', 'approval_order', {
        type: Sequelize.INTEGER,
        allowNull: false,
      });

      await queryInterface.addIndex('approvers', ['approval_order']);
    } catch (error) {
      if (error.name === 'SequelizeDatabaseError') {
        console.log('Table "approvers" does not exist.');
      } else {
        throw error;
      }
    }
  },
};
