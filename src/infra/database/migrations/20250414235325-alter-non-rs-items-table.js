'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('non_requisition_items', 'unit', {
      type: Sequelize.STRING(255),
      defaultValue: 'm', //added default value no need to refresh data
      allowNull: false,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('non_requisition_items', 'unit');
  },
};
