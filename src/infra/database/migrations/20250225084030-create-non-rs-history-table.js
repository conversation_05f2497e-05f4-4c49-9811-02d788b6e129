'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('non_requisition_histories', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      nonRequisitionId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'non_requisitions',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'non_requisition_id',
      },
      approverId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
        field: 'approver_id',
      },
      status: {
        type: Sequelize.STRING(255),
        allowNull: false,
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('now'),
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('now'),
        onUpdate: Sequelize.fn('now'),
      },
    });

    await queryInterface.addIndex(
      'non_requisition_histories',
      ['non_requisition_id'],
      {
        name: 'non_requisition_histories_non_requisition_id_index',
      },
    );

    await queryInterface.addIndex('non_requisition_histories', ['updated_at'], {
      name: 'non_requisition_histories_updated_at_index',
    });

    await queryInterface.addIndex('non_requisition_histories', ['status'], {
      name: 'non_requisition_histories_status_index',
    });

    await queryInterface.addIndex(
      'non_requisition_histories',
      ['approver_id'],
      {
        name: 'non_requisition_histories_approver_id_index',
      },
    );
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeIndex(
      'non_requisition_histories',
      'non_requisition_histories_non_requisition_id_index',
    );
    await queryInterface.removeIndex(
      'non_requisition_histories',
      'non_requisition_histories_updated_at_index',
    );
    await queryInterface.removeIndex(
      'non_requisition_histories',
      'non_requisition_histories_status_index',
    );
    await queryInterface.removeIndex(
      'non_requisition_histories',
      'non_requisition_histories_approver_id_index',
    );

    await queryInterface.dropTable('non_requisition_histories');
  },
};
