'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('delivery_receipts', 'status', {
      type: Sequelize.STRING,
      allowNull: true,
    });

    // Update existing records
    await queryInterface.sequelize.query(`
      UPDATE delivery_receipts 
      SET status = CASE 
        WHEN is_draft = 'true' THEN 'Draft' 
        ELSE 'Delivered' 
      END
    `);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('delivery_receipts', 'status');
  },
};
