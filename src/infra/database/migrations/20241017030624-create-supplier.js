'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('suppliers', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        field: 'user_id',
      },
      name: {
        type: Sequelize.STRING(100),
        allowNull: false,
      },
      contact: {
        type: Sequelize.STRING(20),
        allowNull: true,
      },
      tin: {
        type: Sequelize.STRING(20),
        allowNull: false,
      },
      address: {
        type: Sequelize.TEXT,
        allowNull: false,
      },
      contactPerson: {
        type: Sequelize.STRING(100),
        allowNull: true,
        field: 'contact_person',
      },
      contactNumber: {
        type: Sequelize.STRING(20),
        allowNull: true,
        field: 'contact_number',
      },
      citizenshipCode: {
        type: Sequelize.STRING(2),
        allowNull: false,
        field: 'citizenship_code',
      },
      natureOfIncome: {
        type: Sequelize.STRING(20),
        allowNull: false,
        field: 'nature_of_income',
      },
      payCode: {
        type: Sequelize.STRING(4),
        allowNull: false,
        field: 'pay_code',
        unique: true,
      },
      iccode: {
        type: Sequelize.STRING(2),
        allowNull: false,
        field: 'ic_code',
      },
      status: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      deletedAt: {
        type: Sequelize.DATE,
        field: 'deleted_at',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'created_at',
        defaultValue: Sequelize.fn('now'),
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'updated_at',
        defaultValue: Sequelize.fn('now'),
      },
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('suppliers');
  },
};
