'use strict';

const {
  PO_STATUS,
} = require('../../../domain/constants/purchaseOrderConstants');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('purchase_orders', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      poNumber: {
        type: Sequelize.STRING(255),
        allowNull: false,
        field: 'po_number',
      },
      poLetter: {
        type: Sequelize.STRING(255),
        allowNull: false,
        field: 'po_letter',
      },
      requisitionId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'requisitions',
          key: 'id',
        },
        validate: {
          isInt: true,
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'requisition_id',
      },
      canvassRequisitionId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'canvass_requisitions',
          key: 'id',
        },
        validate: {
          isInt: true,
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'canvass_requisition_id',
      },
      supplierId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'supplier_id',
      },
      supplierType: {
        type: Sequelize.STRING(50),
        allowNull: false,
        field: 'supplier_type',
      },
      status: {
        type: Sequelize.STRING(50),
        allowNull: false,
        defaultValue: PO_STATUS.FOR_PO_REVIEW,
      },
      deliveryAddress: {
        type: Sequelize.TEXT,
        allowNull: true,
        field: 'delivery_address',
      },
      terms: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      warrantyId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'warranty_id',
      },
      depositPercent: {
        type: Sequelize.DECIMAL(20, 2),
        allowNull: true,
        field: 'deposit_percent',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('now'),
        field: 'created_at',
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('now'),
        field: 'updated_at',
      },
    });

    await queryInterface.addIndex('purchase_orders', ['po_number']);
    await queryInterface.addIndex('purchase_orders', ['po_letter']);
    await queryInterface.addIndex('purchase_orders', ['requisition_id']);
    await queryInterface.addIndex('purchase_orders', [
      'canvass_requisition_id',
    ]);
    await queryInterface.addIndex('purchase_orders', [
      'supplier_id',
      'supplier_type',
    ]);
    await queryInterface.addIndex('purchase_orders', ['status']);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeIndex('purchase_orders', ['po_number']);
    await queryInterface.removeIndex('purchase_orders', ['requisition_id']);
    await queryInterface.removeIndex('purchase_orders', [
      'canvass_requisition_id',
    ]);
    await queryInterface.removeIndex('purchase_orders', [
      'supplier_id',
      'supplier_type',
    ]);
    await queryInterface.removeIndex('purchase_orders', ['status']);

    await queryInterface.dropTable('purchase_orders');
  },
};
