'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('invoice_reports', 'payment_request_id', {
      type: Sequelize.INTEGER,
      allowNull: true,
      field: 'payment_request_id',
      references: {
        model: 'rs_payment_requests',
        key: 'id',
      },
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('invoice_reports', 'payment_request_id');
  },
};
