'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.renameColumn(
      'requisition_approvers',
      'is_additional_approver',
      'is_optional_approver',
    );
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.renameColumn(
      'requisition_approvers',
      'is_optional_approver',
      'is_additional_approver',
    );
  },
};
