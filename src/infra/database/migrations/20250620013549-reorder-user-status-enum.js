'use strict';

const USER_STATUS_OLD = Object.freeze({
  INACTIVE: 'inactive',
  ACTIVE: 'active',
  ON_LEAVE: 'on-leave',
});

const USER_STATUS_NEW = Object.freeze({
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  ON_LEAVE: 'on-leave',
});

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(`
      ALTER TABLE users ALTER COLUMN status DROP DEFAULT;
    `);

    await queryInterface.sequelize.query(`
      DO $$
      BEGIN
          IF EXISTS (SELECT 1 FROM pg_type WHERE typname = 'enum_users_status') AND
             NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'enum_users_status_old') THEN
              ALTER TYPE enum_users_status RENAME TO enum_users_status_old;
          END IF;
      END
      $$;
    `);

    await queryInterface.sequelize.query(`
      DO $$
      BEGIN
          IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'enum_users_status') THEN
              CREATE TYPE enum_users_status AS ENUM (
                  '${USER_STATUS_NEW.ACTIVE}',
                  '${USER_STATUS_NEW.INACTIVE}',
                  '${USER_STATUS_NEW.ON_LEAVE}'
              );
          END IF;
      END
      $$;
    `);

    await queryInterface.sequelize.query(`
      ALTER TABLE users
      ALTER COLUMN status TYPE enum_users_status
      USING (
        CASE status::text
          WHEN '${USER_STATUS_OLD.INACTIVE}' THEN '${USER_STATUS_NEW.INACTIVE}'
          WHEN '${USER_STATUS_OLD.ACTIVE}' THEN '${USER_STATUS_NEW.ACTIVE}'
          WHEN '${USER_STATUS_OLD.ON_LEAVE}' THEN '${USER_STATUS_NEW.ON_LEAVE}'
          ELSE NULL
        END
      )::enum_users_status;
    `);

    await queryInterface.sequelize.query(`
      DROP TYPE IF EXISTS enum_users_status_old;
    `);

    await queryInterface.sequelize.query(`
      ALTER TABLE users ALTER COLUMN status SET DEFAULT '${USER_STATUS_NEW.ACTIVE}'::enum_users_status;
    `);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(`
      ALTER TABLE users ALTER COLUMN status DROP DEFAULT;
    `);

    await queryInterface.sequelize.query(`
      DO $$
      BEGIN
          IF EXISTS (SELECT 1 FROM pg_type WHERE typname = 'enum_users_status') AND
             NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'enum_users_status_temp') THEN
              ALTER TYPE enum_users_status RENAME TO enum_users_status_temp;
          END IF;
      END
      $$;
    `);

    await queryInterface.sequelize.query(`
      DO $$
      BEGIN
          IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'enum_users_status') THEN
              CREATE TYPE enum_users_status AS ENUM (
                  '${USER_STATUS_OLD.INACTIVE}',
                  '${USER_STATUS_OLD.ACTIVE}',
                  '${USER_STATUS_OLD.ON_LEAVE}'
              );
          END IF;
      END
      $$;
    `);

    await queryInterface.sequelize.query(`
      ALTER TABLE users
      ALTER COLUMN status TYPE enum_users_status
      USING (
        CASE status::text
          WHEN '${USER_STATUS_NEW.ACTIVE}' THEN '${USER_STATUS_OLD.ACTIVE}'
          WHEN '${USER_STATUS_NEW.INACTIVE}' THEN '${USER_STATUS_OLD.INACTIVE}'
          WHEN '${USER_STATUS_NEW.ON_LEAVE}' THEN '${USER_STATUS_OLD.ON_LEAVE}'
          ELSE NULL
        END
      )::enum_users_status;
    `);

    await queryInterface.sequelize.query(`
      DROP TYPE IF EXISTS enum_users_status_temp;
    `);

    await queryInterface.sequelize.query(`
      ALTER TABLE users ALTER COLUMN status SET DEFAULT '${USER_STATUS_OLD.INACTIVE}'::enum_users_status;
    `);
  },
};
