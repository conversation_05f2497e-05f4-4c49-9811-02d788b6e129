'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('projects', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      code: {
        type: Sequelize.STRING(255),
        allowNull: false,
        unique: true,
      },
      name: {
        type: Sequelize.STRING(255),
        allowNull: false,
      },
      initial: {
        type: Sequelize.STRING(255),
        allowNull: false,
      },
      startDate: {
        type: Sequelize.DATE,
        allowNull: true,
        field: 'start_date',
      },
      endDate: {
        type: Sequelize.DATE,
        allowNull: true,
        field: 'end_date',
      },
      companyId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'companies',
          key: 'id',
        },
        validate: {
          isInt: true,
        },
        onDelete: 'SET NULL',
        onUpdate: 'CASCADE',
        field: 'company_id',
      },
      modifiedBy: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id',
        },
        onDelete: 'SET NULL',
        onUpdate: 'CASCADE',
        field: 'modified_by',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
        field: 'created_at',
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
        field: 'updated_at',
      },
    });

    await queryInterface.addIndex('projects', ['code'], {
      unique: true,
    });
    await queryInterface.addIndex('projects', ['name']);
    await queryInterface.addIndex('projects', ['company_id']);
    await queryInterface.addIndex('projects', ['modified_by']);
    await queryInterface.addIndex('projects', ['start_date']);
    await queryInterface.addIndex('projects', ['end_date']);
  },

  async down(queryInterface, _Sequelize) {
    await queryInterface.dropTable('projects');
  },
};
