'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn(
      'delivery_receipts',
      'latest_delivery_date',
      {
        type: Sequelize.DATE,
        allowNull: true,
      },
    );

    await queryInterface.addColumn(
      'delivery_receipts',
      'latest_delivery_status',
      {
        type: Sequelize.STRING,
        allowNull: true,
      },
    );
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn(
      'delivery_receipts',
      'latest_delivery_date',
    );
    await queryInterface.removeColumn(
      'delivery_receipts',
      'latest_delivery_status',
    );
  },
};
