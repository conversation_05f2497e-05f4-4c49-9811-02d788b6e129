'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    try {
      console.log(
        'Starting migration to update requisition_canvass_histories statuses...',
      );

      // Update the statuses in the history table
      await queryInterface.sequelize.query(`
        UPDATE "requisition_canvass_histories"
        SET "status" = CASE
            WHEN "status" = 'for_approval' THEN 'for_cs_approval'
            WHEN "status" = 'partially_canvassed' THEN 'for_cs_approval'
            WHEN "status" = 'approved' THEN 'cs_approved'
            WHEN "status" = 'rejected' THEN 'cs_rejected'
            WHEN "status" = 'draft' THEN 'cs_draft'
            ELSE "status"
        END
        WHERE "status" IN ('for_approval', 'partially_canvassed', 'approved', 'rejected', 'draft');
      `);

      console.log(
        'Successfully updated requisition_canvass_histories statuses.',
      );
    } catch (error) {
      console.error(
        'Failed to migrate requisition_canvass_histories statuses:',
        error,
      );
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    try {
      console.log(
        'Reverting migration for requisition_canvass_histories statuses...',
      );

      await queryInterface.sequelize.query(`
        UPDATE "requisition_canvass_histories"
        SET "status" = CASE
            WHEN "status" = 'for_cs_approval' THEN 'for_approval'
            WHEN "status" = 'cs_approved' THEN 'approved'
            WHEN "status" = 'cs_rejected' THEN 'rejected'
            WHEN "status" = 'cs_draft' THEN 'draft'
            ELSE "status"
        END
        WHERE "status" IN ('for_cs_approval', 'cs_approved', 'cs_rejected', 'cs_draft');
      `);

      console.log(
        'Successfully reverted requisition_canvass_histories statuses.',
      );
    } catch (error) {
      console.error(
        'Failed to revert requisition_canvass_histories statuses migration:',
        error,
      );
      throw error;
    }
  },
};
