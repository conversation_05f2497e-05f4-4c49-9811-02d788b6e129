'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn(
      'rs_payment_requests',
      'includes_incidental_fees',
      {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        field: 'includes_incidental_fees',
      },
    );
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn(
      'rs_payment_requests',
      'includes_incidental_fees',
    );
  },
};
