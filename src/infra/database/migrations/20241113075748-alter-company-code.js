'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('companies', 'code_integer', {
      type: Sequelize.INTEGER,
      allowNull: true,
    });

    await queryInterface.sequelize.query(`
      UPDATE companies 
      SET code_integer = CAST(TRIM(BOTH FROM code) AS INTEGER)
    `);

    await queryInterface.removeColumn('companies', 'code');

    await queryInterface.renameColumn('companies', 'code_integer', 'code');

    await queryInterface.changeColumn('companies', 'code', {
      type: Sequelize.INTEGER,
      allowNull: false,
      unique: true,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.changeColumn('companies', 'code', {
      type: Sequelize.STRING(255),
      allowNull: false,
      unique: true,
    });
  },
};
