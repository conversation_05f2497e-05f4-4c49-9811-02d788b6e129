'use strict';
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('items', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      itemCd: {
        type: Sequelize.STRING(20),
        unique: true,
        field: 'item_cd',
      },
      itmDes: {
        type: Sequelize.STRING(255),
        allowNull: false,
        field: 'itm_des',
      },
      unit: {
        type: Sequelize.STRING(20),
      },
      acctCd: {
        type: Sequelize.STRING(20),
        field: 'acct_cd',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
        field: 'created_at',
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
        field: 'updated_at',
        onUpdate: Sequelize.NOW,
      },
    });
  },
  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('items');
  },
};
