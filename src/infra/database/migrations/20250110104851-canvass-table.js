'use strict';

const {
  CANVASS_STATUS,
} = require('../../../domain/constants/canvassConstants');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('canvass_requisitions', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      requisitionId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'requisitions',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'requisition_id',
      },
      csNumber: {
        type: Sequelize.STRING(8),
        allowNull: true,
        field: 'cs_number',
      },
      csLetter: {
        type: Sequelize.STRING(2),
        allowNull: false,
        field: 'cs_letter',
      },
      draftCsNumber: {
        type: Sequelize.STRING(8),
        allowNull: true,
        field: 'draft_cs_number',
      },
      status: {
        allowNull: false,
        type: Sequelize.STRING(255),
        defaultValue: CANVASS_STATUS.DRAFT,
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('now'),
        field: 'created_at',
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('now'),
        field: 'updated_at',
      },
    });

    await queryInterface.addIndex('canvass_requisitions', ['requisition_id']);
    await queryInterface.addIndex('canvass_requisitions', ['status']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('canvass_requisitions');
  },
};
