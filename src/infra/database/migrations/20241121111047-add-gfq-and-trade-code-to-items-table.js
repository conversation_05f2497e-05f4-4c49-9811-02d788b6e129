'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('items', 'gfq', {
      type: Sequelize.INTEGER,
    });

    await queryInterface.addColumn('items', 'trade_code', {
      type: Sequelize.INTEGER,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('items', 'gfq');
    await queryInterface.removeColumn('items', 'trade_code');
  },
};
