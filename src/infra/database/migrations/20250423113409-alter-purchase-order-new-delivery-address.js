'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const table = await queryInterface.describeTable('purchase_orders');
    if (!table.new_delivery_address) {
      await queryInterface.addColumn(
        'purchase_orders',
        'new_delivery_address',
        {
          type: Sequelize.TEXT,
          allowNull: true,
          field: 'new_delivery_address',
        },
      );
    }

    if (!table.is_new_delivery_address) {
      await queryInterface.addColumn(
        'purchase_orders',
        'is_new_delivery_address',
        {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: false,
          field: 'is_new_delivery_address',
        },
      );
    }
  },

  async down(queryInterface, Sequelize) {
    const table = await queryInterface.describeTable('purchase_orders');

    if (table.new_delivery_address) {
      await queryInterface.removeColumn(
        'purchase_orders',
        'new_delivery_address',
      );
    }

    if (table.is_new_delivery_address) {
      await queryInterface.removeColumn(
        'purchase_orders',
        'is_new_delivery_address',
      );
    }
  },
};
