'use strict';

const { SUPPLIER_TYPE } = require('../../../domain/constants/canvassConstants');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('canvass_item_suppliers', 'supplier_type', {
      allowNull: false,
      defaultValue: SUPPLIER_TYPE.SUPPLIER,
      type: Sequelize.ENUM(Object.values(SUPPLIER_TYPE)),
    });
  },

  down: async (queryInterface, Sequelize) => {},
};
