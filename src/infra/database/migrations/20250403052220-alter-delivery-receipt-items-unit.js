'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.changeColumn('delivery_receipt_items', 'unit', {
      type: Sequelize.STRING(10),
      allowNull: false,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.changeColumn('delivery_receipt_items', 'unit', {
      type: Sequelize.STRING(5),
      allowNull: false,
    });
  },
};
