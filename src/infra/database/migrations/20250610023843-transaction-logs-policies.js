'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Enable compression & optimization
    // await queryInterface.sequelize.query(`
    //   ALTER TABLE transaction_logs
    //   SET (
    //     timescaledb.compress,
    //     timescaledb.compress_segmentby = 'rs_id, user_id, level',
    //     timescaledb.compress_orderby = 'time DESC'
    //   );
    // `);
    // // Compression policy
    // await queryInterface.sequelize.query(`
    //   SELECT add_compression_policy(
    //     'transaction_logs',
    //     INTERVAL '1 day',
    //     if_not_exists => TRUE
    //   );
    // `);
  },

  async down(queryInterface, Sequelize) {
    // await queryInterface.sequelize.query(`
    //   SELECT remove_compression_policy('transaction_logs', if_exists => TRUE);
    // `);
    // await queryInterface.sequelize.query(`
    //   ALTER TABLE transaction_logs
    //   RESET (
    //     timescaledb.compress,
    //     timescaledb.compress_orderby,
    //     timescaledb.compress_segmentby
    //   );
    // `);
  },
};
