'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const tablesToUpdate = [
      'requisition_approvers',
      'purchase_order_approvers',
      'canvass_approvers',
      'non_requisition_approvers',
      'rs_payment_request_approvers',
    ];

    for (const tableName of tablesToUpdate) {
      await queryInterface.addColumn(tableName, 'override_by', {
        type: Sequelize.JSONB,
        allowNull: true,
        defaultValue: null,
      });
    }
  },

  async down(queryInterface, Sequelize) {
    const tablesToUpdate = [
      'requisition_approvers',
      'purchase_order_approvers',
      'canvass_approvers',
      'non_requisition_approvers',
      'rs_payment_request_approvers',
    ];

    for (const tableName of tablesToUpdate) {
      await queryInterface.removeColumn(tableName, 'override_by');
    }
  },
};
