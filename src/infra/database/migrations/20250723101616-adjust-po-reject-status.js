'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.bulkUpdate(
      'purchase_orders',
      {
        status: 'po_rejected',
      },
      {
        status: 'reject_po',
      },
    );
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkUpdate(
      'purchase_orders',
      {
        status: 'reject_po',
      },
      {
        status: 'po_rejected',
      },
    );
  },
};
