'use strict';
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('non_ofm_items', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      itemName: {
        type: Sequelize.STRING(20),
        unique: true,
        field: 'item_name',
      },
      itemType: {
        type: Sequelize.STRING(20),
        field: 'item_type',
      },
      unit: {
        type: Sequelize.STRING(20),
      },
      acctCd: {
        type: Sequelize.STRING(20),
        unique: true,
        field: 'acct_cd',
      },
      companyCode: {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'company_code',
      },
      projectCode: {
        type: Sequelize.STRING(20),
        allowNull: true,
        field: 'project_code',
      },
      tradeCode: {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'trade_code',
      },
      description: {
        type: Sequelize.STRING(255),
        allowNull: false,
        field: 'description',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
        field: 'created_at',
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
        field: 'updated_at',
        onUpdate: Sequelize.NOW,
      },
    });
  },
  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('non_ofm_items');
  },
};
