'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    const tableName = 'requisitions';

    const indices = [
      {
        name: 'requisition_number_index',
        fields: ['company_code', 'rs_letter', 'rs_number'],
      },
      {
        name: 'draft_requisition_number_index',
        fields: ['company_code', 'rs_letter', 'draft_rs_number'],
      },
    ];

    for (const index of indices) {
      const indexExists = await queryInterface.sequelize.query(
        `SELECT 1 FROM pg_indexes WHERE tablename = '${tableName}' AND indexname = '${index.name}'`,
      );

      if (indexExists[0].length === 0) {
        await queryInterface.addIndex(tableName, index.fields, {
          name: index.name,
        });
      }
    }
  },
  down: async (queryInterface, Sequelize) => {
    const tableName = 'requisitions';

    const indicesToRemove = [
      'requisition_number_index',
      'draft_requisition_number_index',
    ];

    for (const indexName of indicesToRemove) {
      const indexExists = await queryInterface.sequelize.query(
        `SELECT 1 FROM pg_indexes WHERE tablename = '${tableName}' AND indexname = '${indexName}'`,
      );

      if (indexExists[0].length > 0) {
        await queryInterface.removeIndex('requisitions', indexName);
      }
    }
  },
};
