'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Create area_names table
    await queryInterface.createTable('association_areas', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      code: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true,
      },
      name: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });

    // Add index for code field
    await queryInterface.addIndex('association_areas', ['code'], {
      unique: true,
      name: 'association_areas_code_unique',
    });

    // Insert new area data
    await queryInterface.bulkInsert('association_areas', [
      {
        id: 1,
        code: 'MET14',
        name: 'MET 1-4',
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        id: 2,
        code: 'TMR_TGR_TGH_OTR',
        name: 'TMR / TGR / TGH / OTR',
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        id: 3,
        code: 'GET_CER_BMCAI',
        name: 'GET / CER / BMCAI',
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        id: 4,
        code: 'CDC_OFC_MLA_ORTIGAS_VCB_SALES_OFFICE',
        name: 'CDC OFC / MLA / ORTIGAS / VCB / SALES OFFICE',
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        id: 5,
        code: 'CC10_RADA',
        name: 'CC10 / RADA',
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        id: 6,
        code: 'PPT12_101X_GCR_CL1_CL3',
        name: 'PPT1&2 / 101X / GCR / CL1 / CL3',
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        id: 7,
        code: 'ONP_PH1_TNP',
        name: 'ONP / PH1 / TNP',
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        id: 8,
        code: 'MEM2_NRT',
        name: 'MEM2 / NRT',
        created_at: new Date(),
        updated_at: new Date(),
      },
    ]);

    // Update department_association_approvals table
    await queryInterface.sequelize.query(`
      UPDATE department_association_approvals 
      SET area_code = CASE 
        WHEN area_code = 'AREA1' THEN 'MET14'
        WHEN area_code = 'AREA2' THEN 'TMR_TGR_TGH_OTR'
        WHEN area_code = 'AREA3' THEN 'GET_CER_BMCAI'
        WHEN area_code = 'AREA4' THEN 'CDC_OFC_MLA_ORTIGAS_VCB_SALES_OFFICE'
        ELSE area_code
      END
    `);

    // Update companies table
    await queryInterface.sequelize.query(`
      UPDATE companies 
      SET area_code = CASE 
        WHEN area_code = 'AREA1' THEN 'MET14'
        WHEN area_code = 'AREA2' THEN 'TMR_TGR_TGH_OTR'
        WHEN area_code = 'AREA3' THEN 'GET_CER_BMCAI'
        WHEN area_code = 'AREA4' THEN 'CDC_OFC_MLA_ORTIGAS_VCB_SALES_OFFICE'
        ELSE area_code
      END
    `);
  },

  async down(queryInterface, Sequelize) {
    // Revert area codes in companies table
    await queryInterface.sequelize.query(`
      UPDATE companies 
      SET area_code = CASE 
        WHEN area_code = 'MET14' THEN 'AREA1'
        WHEN area_code = 'TMR_TGR_TGH_OTR' THEN 'AREA2'
        WHEN area_code = 'GET_CER_BMCAI' THEN 'AREA3'
        WHEN area_code = 'CDC_OFC_MLA_ORTIGAS_VCB_SALES_OFFICE' THEN 'AREA4'
        ELSE area_code
      END
    `);

    // Revert area codes in department_association_approvals table
    await queryInterface.sequelize.query(`
      UPDATE department_association_approvals 
      SET area_code = CASE 
        WHEN area_code = 'MET14' THEN 'AREA1'
        WHEN area_code = 'TMR_TGR_TGH_OTR' THEN 'AREA2'
        WHEN area_code = 'GET_CER_BMCAI' THEN 'AREA3'
        WHEN area_code = 'CDC_OFC_MLA_ORTIGAS_VCB_SALES_OFFICE' THEN 'AREA4'
        ELSE area_code
      END
    `);

    // Remove index
    await queryInterface.removeIndex(
      'association_areas',
      'association_areas_code_unique',
    );

    // Drop area_names table
    await queryInterface.dropTable('association_areas');
  },
};
