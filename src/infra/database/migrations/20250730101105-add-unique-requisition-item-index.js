'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const [rowsToClean, metadata] = await queryInterface.sequelize.query(`
        SELECT DISTINCT ci.id AS "canvassItemId"
        FROM "canvass_items" AS ci
        JOIN "requisition_item_lists" AS ril_parent ON ci.requisition_item_list_id = ril_parent.id
        WHERE ril_parent.id NOT IN (
            SELECT MIN(id)
            FROM "requisition_item_lists"
            GROUP BY requisition_id, item_id
        )
        AND (ril_parent.requisition_id, ril_parent.item_id) IN (
            SELECT requisition_id, item_id
            FROM "requisition_item_lists"
            GROUP BY requisition_id, item_id
            HAVING COUNT(*) > 1
        )
        AND EXISTS (
            SELECT 1
            FROM "purchase_order_items" AS poi
            WHERE poi.canvass_item_id = ci.id
        );
    `);
    const problematicCanvassItemIds = rowsToClean.map(
      (row) => row.canvassItemId,
    );
    console.log(
      `Step 0: Identified problematic canvass_item_ids: ${problematicCanvassItemIds.length > 0 ? problematicCanvassItemIds.join(', ') : 'None'}`,
    );

    if (problematicCanvassItemIds.length > 0) {
      console.log(
        `Step A1: Deleting dependent "purchase_order_items" for canvass_item_ids: ${problematicCanvassItemIds.join(', ')}...`,
      );
      await queryInterface.sequelize.query(`
            DELETE FROM "purchase_order_items"
            WHERE canvass_item_id IN (${problematicCanvassItemIds.map((id) => `${id}`).join(',')});
        `);
      console.log(`Step A1: Dependent "purchase_order_items" deleted.`);

      console.log(
        `Step A2: Deleting problematic "canvass_items" with IDs: ${problematicCanvassItemIds.join(', ')}...`,
      );
      await queryInterface.sequelize.query(`
            DELETE FROM "canvass_items"
            WHERE id IN (${problematicCanvassItemIds.map((id) => `${id}`).join(',')});
        `);
      console.log(`Step A2: Problematic "canvass_items" deleted.`);
    } else {
      console.log(
        'Step A: No specific problematic canvass_item_ids to delete for initial FK cleanup.',
      );
    }

    console.log(
      'Step B: Cleaning up duplicate (requisition_id, item_id) entries in "requisition_item_lists"...',
    );
    await queryInterface.sequelize.query(`
      DELETE FROM "requisition_item_lists"
      WHERE id NOT IN (
          SELECT MIN(id)
          FROM "requisition_item_lists"
          GROUP BY requisition_id, item_id
      )
      AND (requisition_id, item_id) IN (
          SELECT requisition_id, item_id
          FROM "requisition_item_lists"
          GROUP BY requisition_id, item_id
          HAVING COUNT(*) > 1
      );
    `);
    console.log(
      'Step B: Duplicate entries in "requisition_item_lists" removed.',
    );

    console.log(
      'Step C: Adding unique constraint "unique_requisition_item_per_requisition" to "requisition_item_lists"...',
    );
    await queryInterface.addConstraint('requisition_item_lists', {
      fields: ['requisition_id', 'item_id', 'created_at'],
      type: 'unique',
      name: 'unique_requisition_item_per_requisition',
    });
    console.log(
      'Step C: Unique constraint "unique_requisition_item_per_requisition" added successfully.',
    );

    console.log(
      '--- Data Cleanup and Constraint Application Migration Completed. ---',
    );
  },

  down: async (queryInterface, Sequelize) => {
    console.log(
      '--- Starting Revert for Data Cleanup and Constraint Application Migration ---',
    );

    console.log(
      'Revert Step C: Removing unique constraint from "requisition_item_lists"...',
    );
    await queryInterface.removeConstraint(
      'requisition_item_lists',
      'unique_requisition_item_per_requisition',
    );
    console.log(
      'Revert Step C: Unique constraint removed from "requisition_item_lists".',
    );

    console.log(
      '--- Revert Completed. Note: Deleted data is not restored automatically. ---',
    );
  },
};
