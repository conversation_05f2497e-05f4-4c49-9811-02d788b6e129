'use strict';
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('rs_payment_requests', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      prDraftNumber: {
        type: Sequelize.STRING(8),
        allowNull: true,
        field: 'draft_pr_number',
      },
      prNumber: {
        type: Sequelize.STRING(8),
        allowNull: true,
        field: 'pr_number',
      },
      prLetter: {
        type: Sequelize.STRING(2),
        allowNull: false,
        field: 'pr_letter',
      },
      isDraft: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        field: 'is_draft',
        defaultValue: false,
      },
      requisitionId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'requisition_id',
      },
      purchaseOrderId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'purchase_order_id',
      },
      deliveryInvoiceId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'delivery_invoice_id',
      },
      termsData: {
        type: Sequelize.JSONB,
        allowNull: true,
        field: 'terms_data',
      },
      payableDate: {
        type: Sequelize.DATE,
        allowNull: true,
        field: 'payable_date',
      },
      discountIn: {
        type: Sequelize.STRING,
        allowNull: true,
        field: 'discount_in',
      },
      discountPercentage: {
        type: Sequelize.DECIMAL,
        allowNull: true,
        field: 'discount_percentage',
      },
      discountAmount: {
        type: Sequelize.DECIMAL,
        allowNull: true,
        field: 'discount_amount',
      },
      withholdingTaxDeduction: {
        type: Sequelize.DECIMAL,
        allowNull: true,
        field: 'withholding_tax_deduction',
      },
      deliveryFee: {
        type: Sequelize.DECIMAL,
        allowNull: true,
        field: 'delivery_fee',
      },
      tip: {
        type: Sequelize.DECIMAL,
        allowNull: true,
        field: 'tip',
      },
      extraCharges: {
        type: Sequelize.DECIMAL,
        allowNull: true,
        field: 'extra_charges',
      },
      status: {
        type: Sequelize.STRING,
        allowNull: false,
        field: 'status',
      },
      totalAmount: {
        type: Sequelize.DECIMAL,
        allowNull: true,
        field: 'total_amount',
      },
    });
  },
  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('rs_payment_requests');
  },
};
