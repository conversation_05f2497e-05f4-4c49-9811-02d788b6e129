'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('requisition_delivery_histories', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      requisitionId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'requisition_id',
        references: {
          model: 'requisitions',
          key: 'id',
        },
      },
      drNumber: {
        type: Sequelize.STRING,
        allowNull: false,
        field: 'dr_number',
      },
      supplier: {
        type: Sequelize.STRING,
        allowNull: false,
        field: 'supplier',
      },
      dateOrdered: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'date_ordered',
      },
      quantityOrdered: {
        type: Sequelize.FLOAT,
        allowNull: false,
        field: 'quantity_ordered',
      },
      quantityDelivered: {
        type: Sequelize.FLOAT,
        allowNull: false,
        field: 'quantity_delivered',
      },
      dateDelivered: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'date_delivered',
      },
      status: {
        type: Sequelize.STRING,
        allowNull: false,
        field: 'status',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'created_at',
        defaultValue: Sequelize.fn('now'),
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'updated_at',
        defaultValue: Sequelize.fn('now'),
      },
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('requisition_delivery_histories');
  },
};
