'use strict';

const { SUPPLIER_TYPE } = require('../../../domain/constants/canvassConstants');
const {
  PO_ITEM_STATUS,
} = require('../../../domain/constants/purchaseOrderConstants');

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('purchase_order_cancelled_items', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      purchaseOrderId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'purchase_orders',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'purchase_order_id',
      },
      requisitionId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'requisitions',
          key: 'id',
        },
        validate: {
          isInt: true,
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'requisition_id',
      },
      canvassRequisitionId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'canvass_requisitions',
          key: 'id',
        },
        validate: {
          isInt: true,
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'canvass_requisition_id',
      },
      canvassItemId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'canvass_items',
          key: 'id',
        },
        validate: {
          isInt: true,
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'canvass_item_id',
      },
      requisitionItemListId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'requisition_item_lists',
          key: 'id',
        },
        validate: {
          isInt: true,
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'requisition_item_list_id',
      },
      supplierId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        validate: {
          isInt: true,
        },
        field: 'supplier_id',
      },
      supplierType: {
        allowNull: false,
        defaultValue: SUPPLIER_TYPE.SUPPLIER,
        type: Sequelize.ENUM(Object.values(SUPPLIER_TYPE)),
        field: 'supplier_type',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('now'),
        field: 'created_at',
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('now'),
        field: 'updated_at',
      },
      status: {
        type: Sequelize.STRING,
        allowNull: false,
        defaultValue: PO_ITEM_STATUS.NEW,
        field: 'status',
      },
    });

    await queryInterface.addIndex(
      'purchase_order_cancelled_items',
      [
        'requisition_id',
        'canvass_item_id',
        'purchase_order_id',
        'canvass_requisition_id',
        'requisition_item_list_id',
        'supplier_id',
        'supplier_type',
      ],
      {
        unique: true,
        name: 'unique_po_cancelled_items',
      },
    );

    await queryInterface.addIndex(
      'purchase_order_cancelled_items',
      ['purchase_order_id'],
      {
        name: 'po_cancelled_items_po_id_index',
      },
    );

    await queryInterface.addIndex(
      'purchase_order_cancelled_items',
      ['requisition_id'],
      {
        name: 'po_cancelled_items_requisition_id_index',
      },
    );

    await queryInterface.addIndex(
      'purchase_order_cancelled_items',
      ['canvass_item_id'],
      {
        name: 'po_cancelled_items_canvass_item_id_index',
      },
    );

    await queryInterface.addIndex(
      'purchase_order_cancelled_items',
      ['supplier_id', 'supplier_type'],
      {
        name: 'po_cancelled_items_supplier_composite_index',
      },
    );

    await queryInterface.addIndex(
      'purchase_order_cancelled_items',
      ['canvass_requisition_id', 'requisition_item_list_id'],
      {
        name: 'po_cancelled_items_requisition_composite_index',
      },
    );
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeIndex(
      'purchase_order_cancelled_items',
      'po_cancelled_items_po_id_index',
    );
    await queryInterface.removeIndex(
      'purchase_order_cancelled_items',
      'po_cancelled_items_requisition_id_index',
    );
    await queryInterface.removeIndex(
      'purchase_order_cancelled_items',
      'po_cancelled_items_canvass_item_id_index',
    );
    await queryInterface.removeIndex(
      'purchase_order_cancelled_items',
      'po_cancelled_items_supplier_composite_index',
    );
    await queryInterface.removeIndex(
      'purchase_order_cancelled_items',
      'po_cancelled_items_requisition_composite_index',
    );

    await queryInterface.dropTable('purchase_order_cancelled_items');
  },
};
