'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface) {
    await queryInterface.addConstraint('rs_payment_requests', {
      fields: ['requisition_id'],
      type: 'foreign key',
      name: 'rs_payment_requests_requisition_id_fk',
      references: {
        table: 'requisitions',
        field: 'id',
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });

    await queryInterface.addConstraint('rs_payment_requests', {
      fields: ['purchase_order_id'],
      type: 'foreign key',
      name: 'rs_payment_requests_purchase_order_id_fk',
      references: {
        table: 'purchase_orders',
        field: 'id',
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });

    await queryInterface.addConstraint('rs_payment_requests', {
      fields: ['delivery_invoice_id'],
      type: 'foreign key',
      name: 'rs_payment_requests_delivery_invoice_id_fk',
      references: {
        table: 'delivery_receipt_invoices',
        field: 'id',
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
  },

  async down(queryInterface) {
    await queryInterface.removeConstraint(
      'rs_payment_requests',
      'rs_payment_requests_requisition_id_fk',
    );
    await queryInterface.removeConstraint(
      'rs_payment_requests',
      'rs_payment_requests_purchase_order_id_fk',
    );
    await queryInterface.removeConstraint(
      'rs_payment_requests',
      'rs_payment_requests_delivery_invoice_id_fk',
    );
  },
};
