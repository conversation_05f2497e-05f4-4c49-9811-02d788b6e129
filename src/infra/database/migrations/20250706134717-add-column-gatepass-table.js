'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const tableInfo = await queryInterface.describeTable('gate_passes');

    if (!tableInfo.requisition_id) {
      await queryInterface.addColumn('gate_passes', 'requisition_id', {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'requisitions',
          key: 'id',
        },
        field: 'requisition_id',
      });
    }
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('gate_passes', 'requisition_id');
  },
};
