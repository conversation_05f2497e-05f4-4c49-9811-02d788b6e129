'use strict';

const {
  TRADE_CATEGORIES,
} = require('../../../domain/constants/tradeConstants');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('trades', 'category', {
      type: Sequelize.ENUM('MAJOR', 'SUB'),
      allowNull: false,
      defaultValue: TRADE_CATEGORIES.MAJOR,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('trades', 'category');
  },
};
