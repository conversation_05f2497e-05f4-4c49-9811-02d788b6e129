'use strict';
const { PRS_JOURNEY } = require('../../../domain/constants/logConstants');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('transaction_logs', {
      time: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'user_id',
        comment: 'Session user id',
      },
      rsId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'rs_id',
        comment: 'Requisition ID',
      },
      level: {
        type: Sequelize.ENUM,
        allowNull: false,
        values: Object.values(PRS_JOURNEY),
        comment: 'Current Journey',
      },
      message: {
        // Success or error logs
        type: Sequelize.TEXT,
        allowNull: false,
        comment: 'Result of current RS history transaction',
      },
      metadata: {
        // Data if not get request
        type: Sequelize.JSONB,
        allowNull: true,
        comment: 'Additional contextual data about the action',
      },
    });

    // Chunk interval by 7 days
    // await queryInterface.sequelize.query(`
    //     SELECT create_hypertable(
    //       'transaction_logs',
    //       by_range('time', INTERVAL '7 days'),
    //       if_not_exists => TRUE
    //     );
    // `);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('transaction_logs');
  },
};
