'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    await queryInterface.createTable('company_departments', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      companyId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'company_id',
        references: {
          model: 'companies',
          key: 'id',
        },
        validate: {
          isInt: true,
        },
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
      },
      departmentId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'department_id',
        references: {
          model: 'departments',
          key: 'id',
        },
        validate: {
          isInt: true,
        },
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
      },
      managerId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'manager_id',
        references: {
          model: 'users',
          key: 'id',
        },
        validate: {
          isInt: true,
        },
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
      },
      assistantManagerId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'assistant_manager_id',
        references: {
          model: 'users',
          key: 'id',
        },
        validate: {
          isInt: true,
        },
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
      },
      secretaryId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'secretary_id',
        references: {
          model: 'users',
          key: 'id',
        },
        validate: {
          isInt: true,
        },
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
        field: 'created_at',
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
        field: 'updated_at',
      },
    });
  },

  async down(queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
    await queryInterface.dropTable('company_departments');
  },
};
