'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.removeIndex(
      'departments',
      'departments_assistant_manager_id_idx',
    );
    await queryInterface.removeIndex(
      'departments',
      'departments_department_head_id_idx',
    );
    await queryInterface.removeIndex(
      'departments',
      'departments_division_head_id_idx',
    );

    await queryInterface.removeColumn('departments', 'assistant_manager_id');
    await queryInterface.removeColumn('departments', 'department_head_id');
    await queryInterface.removeColumn(
      'departments',
      'department_division_head_id',
    );
  },

  async down(queryInterface, Sequelize) {},
};
