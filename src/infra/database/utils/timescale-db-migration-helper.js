'use strict';

/**
 * TimescaleDB Migration Helper Utilities
 * 
 * This module provides utility functions to handle TimescaleDB compression
 * during database migrations that need to modify table schemas.
 * 
 * Usage:
 * const { withTimescaleDBCompression } = require('../utils/timescaledb-migration-helper');
 * 
 * await withTimescaleDBCompression(queryInterface, transaction, 'table_name', async () => {
 *   // Your schema modification operations here
 *   await queryInterface.changeColumn('table_name', 'column', { ... }, { transaction });
 * });
 */

/**
 * Executes a callback function while temporarily disabling TimescaleDB compression
 * on the specified table if it exists and has compression enabled.
 * 
 * @param {Object} queryInterface - Sequelize query interface
 * @param {Object} transaction - Database transaction
 * @param {string} tableName - Name of the table to check/modify
 * @param {Function} callback - Function to execute with compression disabled
 * @returns {Promise<void>}
 */
async function withTimescaleDBCompression(queryInterface, transaction, tableName, callback) {
  console.log(`🔧 Checking ${tableName} for TimescaleDB compression...`);
  
  let compressionWasEnabled = false;
  
  try {
    // Check if TimescaleDB extension exists
    const [extensionExists] = await queryInterface.sequelize.query(`
      SELECT EXISTS(SELECT 1 FROM pg_extension WHERE extname = 'timescaledb');
    `, { transaction });

    if (!extensionExists[0].exists) {
      console.log(`ℹ️  TimescaleDB extension not found, proceeding without compression handling`);
      await callback();
      return;
    }

    // Check if table is a hypertable
    const [isHypertable] = await queryInterface.sequelize.query(`
      SELECT EXISTS (
        SELECT FROM timescaledb_information.hypertables
        WHERE hypertable_name = '${tableName}' AND hypertable_schema = 'public'
      );
    `, { transaction });

    if (!isHypertable[0].exists) {
      console.log(`ℹ️  ${tableName} is not a hypertable, proceeding without compression handling`);
      await callback();
      return;
    }

    console.log(`✅ ${tableName} is a hypertable, checking compression status...`);
    
    // Check if compression is enabled
    const [compressionEnabled] = await queryInterface.sequelize.query(`
      SELECT EXISTS (
        SELECT FROM timescaledb_information.compression_settings
        WHERE hypertable_name = '${tableName}' AND hypertable_schema = 'public'
      );
    `, { transaction });

    if (compressionEnabled[0].exists) {
      console.log(`⚠️  ${tableName} has compression enabled, temporarily disabling...`);
      compressionWasEnabled = true;
      
      // Decompress any compressed chunks first
      await queryInterface.sequelize.query(`
        SELECT decompress_chunk(format('%I.%I', c.chunk_schema, c.chunk_name))
        FROM timescaledb_information.chunks c
        JOIN pg_class pc ON pc.relname = c.chunk_name
        JOIN pg_namespace pn ON pn.nspname = c.chunk_schema AND pn.oid = pc.relnamespace
        WHERE c.hypertable_name = '${tableName}'
          AND c.hypertable_schema = 'public'
          AND c.is_compressed = true;
      `, { transaction });
      
      // Disable compression
      await queryInterface.sequelize.query(`
        ALTER TABLE ${tableName} SET (timescaledb.compress = false);
      `, { transaction });
      
      console.log(`✅ ${tableName} compression temporarily disabled`);
    }

    // Execute the callback with compression disabled
    await callback();

    // Re-enable compression if it was enabled before
    if (compressionWasEnabled) {
      console.log(`🔄 Re-enabling compression for ${tableName}...`);
      await queryInterface.sequelize.query(`
        ALTER TABLE ${tableName} SET (
          timescaledb.compress = true,
          timescaledb.compress_segmentby = 'id'
        );
      `, { transaction });
      console.log(`✅ ${tableName} compression re-enabled`);
    }

  } catch (error) {
    console.error(`❌ Error handling TimescaleDB compression for ${tableName}:`, error.message);
    throw error;
  }
}

/**
 * Executes a callback function while temporarily disabling TimescaleDB compression
 * on multiple tables if they exist and have compression enabled.
 * 
 * @param {Object} queryInterface - Sequelize query interface
 * @param {Object} transaction - Database transaction
 * @param {string[]} tableNames - Array of table names to check/modify
 * @param {Function} callback - Function to execute with compression disabled
 * @returns {Promise<void>}
 */
async function withMultipleTimescaleDBCompression(queryInterface, transaction, tableNames, callback) {
  console.log(`🔧 Checking multiple tables for TimescaleDB compression: ${tableNames.join(', ')}`);
  
  const compressionStates = {};
  
  try {
    // Check if TimescaleDB extension exists
    const [extensionExists] = await queryInterface.sequelize.query(`
      SELECT EXISTS(SELECT 1 FROM pg_extension WHERE extname = 'timescaledb');
    `, { transaction });

    if (!extensionExists[0].exists) {
      console.log(`ℹ️  TimescaleDB extension not found, proceeding without compression handling`);
      await callback();
      return;
    }

    // Handle each table
    for (const tableName of tableNames) {
      compressionStates[tableName] = await disableCompressionIfEnabled(queryInterface, transaction, tableName);
    }

    // Execute the callback with compression disabled
    await callback();

    // Re-enable compression for tables that had it
    for (const [tableName, hadCompression] of Object.entries(compressionStates)) {
      if (hadCompression) {
        await reEnableCompression(queryInterface, transaction, tableName);
      }
    }

  } catch (error) {
    console.error(`❌ Error handling TimescaleDB compression for multiple tables:`, error.message);
    throw error;
  }
}

/**
 * Helper function to disable compression on a single table if enabled
 * @private
 */
async function disableCompressionIfEnabled(queryInterface, transaction, tableName) {
  // Check if table is a hypertable
  const [isHypertable] = await queryInterface.sequelize.query(`
    SELECT EXISTS (
      SELECT FROM timescaledb_information.hypertables
      WHERE hypertable_name = '${tableName}' AND hypertable_schema = 'public'
    );
  `, { transaction });

  if (!isHypertable[0].exists) {
    console.log(`ℹ️  ${tableName} is not a hypertable`);
    return false;
  }

  console.log(`✅ ${tableName} is a hypertable, checking compression...`);
  
  // Check if compression is enabled
  const [compressionEnabled] = await queryInterface.sequelize.query(`
    SELECT EXISTS (
      SELECT FROM timescaledb_information.compression_settings
      WHERE hypertable_name = '${tableName}' AND hypertable_schema = 'public'
    );
  `, { transaction });

  if (compressionEnabled[0].exists) {
    console.log(`⚠️  ${tableName} has compression enabled, temporarily disabling...`);
    
    // Decompress any compressed chunks first
    await queryInterface.sequelize.query(`
      SELECT decompress_chunk(chunk_name) 
      FROM timescaledb_information.chunks 
      WHERE hypertable_name = '${tableName}' 
      AND hypertable_schema = 'public'
      AND is_compressed = true;
    `, { transaction });
    
    // Disable compression
    await queryInterface.sequelize.query(`
      ALTER TABLE ${tableName} SET (timescaledb.compress = false);
    `, { transaction });
    
    console.log(`✅ ${tableName} compression temporarily disabled`);
    return true;
  }

  return false;
}

/**
 * Helper function to re-enable compression on a table
 * @private
 */
async function reEnableCompression(queryInterface, transaction, tableName) {
  console.log(`🔄 Re-enabling compression for ${tableName}...`);
  await queryInterface.sequelize.query(`
    ALTER TABLE ${tableName} SET (
      timescaledb.compress = true,
      timescaledb.compress_segmentby = 'id'
    );
  `, { transaction });
  console.log(`✅ ${tableName} compression re-enabled`);
}

module.exports = {
  withTimescaleDBCompression,
  withMultipleTimescaleDBCompression
};
