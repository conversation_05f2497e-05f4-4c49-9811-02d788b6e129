module.exports = (sequelize, Sequelize) => {
  const SyncModel = sequelize.define(
    'syncs',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      model: {
        type: Sequelize.STRING(50),
        allowNull: false,
        unique: true,
      },
      lastSyncedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'last_synced_at',
      },
    },
    {
      underscored: true,
    },
  );

  return SyncModel;
};
