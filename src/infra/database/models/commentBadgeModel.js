module.exports = (sequelize, Sequelize) => {
  const CommentBadgeModel = sequelize.define(
    'comment_badges',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'user_id',
        unique: true,
      },
      commentId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'comment_id',
        unique: true,
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
        field: 'created_at',
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
        field: 'updated_at',
      },
    },
    {
      timestamps: true,
    },
    {
      indexes: [
        {
          unique: true,
          fields: ['user_id', 'comment_id'],
        },
      ],
    },
  );

  /* -------------------------------------------------------------------------- */
  /*                                Associations                                */
  /* -------------------------------------------------------------------------- */

  CommentBadgeModel.associate = (models) => {
    CommentBadgeModel.belongsTo(models.commentModel, {
      foreignKey: 'commentId',
      as: 'commentBadge',
    });
  };

  return CommentBadgeModel;
};
