const { convertDateToDDMMMYYYY } = require('../../../app/utils');

module.exports = (sequelize, Sequelize) => {
  const RequisitionModel = sequelize.define(
    'requisitions',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      rsNumber: {
        type: Sequelize.STRING(8),
        allowNull: true,
        field: 'rs_number',
      },
      rsLetter: {
        type: Sequelize.STRING(2),
        allowNull: false,
        field: 'rs_letter',
      },
      companyCode: {
        type: Sequelize.STRING(5),
        allowNull: false,
        field: 'company_code',
      },
      createdBy: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'created_by',
      },
      companyId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'company_id',
      },
      departmentId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'department_id',
      },
      projectId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'project_id',
      },
      dateRequired: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'date_required',
        get() {
          return convertDateToDDMMMYYYY(this.getDataValue('dateRequired'));
        },
      },
      deliveryAddress: {
        type: Sequelize.STRING(255),
        allowNull: false,
        field: 'delivery_address',
      },
      purpose: {
        type: Sequelize.STRING(255),
        allowNull: true,
        field: 'purpose',
      },
      chargeTo: {
        type: Sequelize.STRING(255),
        allowNull: true,
        field: 'charge_to',
      },
      chargeToId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'charge_to_id',
      },
      status: {
        type: Sequelize.STRING(20),
        allowNull: false,
        field: 'status',
      },
      assignedTo: {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'assigned_to',
      },
      type: {
        type: Sequelize.STRING,
        allowNull: false,
        field: 'type',
        defaultValue: 'ofm',
      },
      // Force close tracking fields
      forceClosedAt: {
        type: Sequelize.DATE,
        allowNull: true,
        field: 'force_closed_at',
        get() {
          const value = this.getDataValue('forceClosedAt');
          return value ? convertDateToDDMMMYYYY(value) : null;
        },
      },
      forceClosedBy: {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'force_closed_by',
        references: {
          model: 'users',
          key: 'id',
        },
      },
      forceCloseReason: {
        type: Sequelize.TEXT,
        allowNull: true,
        field: 'force_close_reason',
      },
      forceCloseScenario: {
        type: Sequelize.STRING(50),
        allowNull: true,
        field: 'force_close_scenario',
      },
      draftRsNumber: {
        type: Sequelize.STRING(8),
        allowNull: true,
        field: 'draft_rs_number',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'created_at',
        defaultValue: Sequelize.fn('now'),
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'updated_at',
        defaultValue: Sequelize.fn('now'),
      },
      companyName: {
        type: Sequelize.STRING,
        field: 'company_name',
        allowNull: true,
      },
      companyNameLocked: {
        type: Sequelize.BOOLEAN,
        field: 'company_name_locked',
        defaultValue: false,
      },
      category: {
        type: Sequelize.STRING,
        allowNull: false,
        field: 'category',
      },
    },
    {
      timestamps: true,
      underscored: true,
    },
    {
      indexes: [
        {
          fields: ['company_code', 'rs_letter', 'rs_number'],
          name: 'requisition_number_index',
        },
        {
          fields: ['company_code', 'rs_letter', 'draft_rs_number'],
          name: 'draft_requisition_number_index',
        },
      ],
    },
  );

  /* -------------------------------------------------------------------------- */
  /*                                Associations                                */
  /* -------------------------------------------------------------------------- */
  RequisitionModel.beforeUpdate(async (requisition) => {
    try {
      if (
        requisition.changed('status') &&
        ['for_delivery', 'payment_request', 'pr_approval'].includes(
          requisition.status,
        )
      ) {
        const company = await sequelize.model('companies').findOne({
          where: { id: requisition.companyId },
        });

        if (!company) {
          return;
        }

        requisition.companyName = company.name;
        requisition.companyNameLocked = true;
      }
    } catch (error) {
      console.error(
        'HOOK_ERROR - requisitionModel - beforeUpdate: ',
        error.stack,
      );
    }
  });

  RequisitionModel.associate = (models) => {
    RequisitionModel.hasMany(models.requisitionItemListModel, {
      foreignKey: 'requisitionId',
      as: 'requisitionItemLists',
    });

    RequisitionModel.belongsTo(models.companyModel, {
      foreignKey: 'companyId',
      as: 'company',
    });

    RequisitionModel.belongsTo(models.departmentModel, {
      foreignKey: 'departmentId',
      as: 'department',
    });

    RequisitionModel.belongsTo(models.projectModel, {
      foreignKey: 'projectId',
      as: 'project',
    });

    RequisitionModel.belongsTo(models.userModel, {
      foreignKey: 'createdBy',
      as: 'createdByUser',
    });

    RequisitionModel.belongsTo(models.userModel, {
      foreignKey: 'assignedTo',
      as: 'assignee',
    });

    RequisitionModel.hasMany(models.tomItemModel, {
      foreignKey: 'requisitionId',
      as: 'tomItems',
    });

    RequisitionModel.hasMany(models.attachmentModel, {
      foreignKey: 'model_id',
      as: 'attachments',
    });

    RequisitionModel.hasMany(models.commentModel, {
      foreignKey: 'model_id',
      as: 'comments',
    });

    RequisitionModel.hasMany(models.requisitionApproverModel, {
      foreignKey: 'requisitionId',
      as: 'requisitionApprovers',
    });

    RequisitionModel.hasMany(models.deliveryReceiptModel, {
      foreignKey: 'requisitionId',
      as: 'deliveryReceipt',
    });

    RequisitionModel.hasOne(models.viewDashboardModel, {
      foreignKey: 'id',
      sourceKey: 'id',
      as: 'dashboardRequisition',
    });

    RequisitionModel.hasMany(models.rsPaymentRequestModel, {
      foreignKey: 'requisitionId',
      as: 'rsPaymentRequest',
    });

    RequisitionModel.hasMany(models.canvassRequisitionModel, {
      foreignKey: 'requisitionId',
      as: 'canvassRequisitions',
    });

    RequisitionModel.hasMany(models.requisitionBadgeModel, {
      foreignKey: 'requisitionId',
      as: 'requisitionBadges',
    });

    RequisitionModel.hasMany(models.purchaseOrderModel, {
      foreignKey: 'requisitionId',
      as: 'purchaseOrders',
    });

    /* Charge To */
    RequisitionModel.belongsTo(models.companyModel, {
      foreignKey: 'chargeToId',
      as: 'chargeToCompany',
    });

    RequisitionModel.belongsTo(models.supplierModel, {
      foreignKey: 'chargeToId',
      as: 'chargeToSupplier',
    });

    RequisitionModel.belongsTo(models.projectModel, {
      foreignKey: 'chargeToId',
      as: 'chargeToProject',
    });
  };

  return RequisitionModel;
};
