const {
  NON_RS_DISCOUNT_TYPE,
} = require('../../../domain/constants/nonRSConstants');

module.exports = (sequelize, Sequelize) => {
  const NonRequisitionItemModel = sequelize.define(
    'non_requisition_items',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      nonRequisitionId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'non_requisitions',
          key: 'id',
        },
        validate: {
          isInt: true,
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'non_requisition_id',
      },
      name: {
        type: Sequelize.STRING(255),
        allowNull: false,
      },
      unit: {
        type: Sequelize.STRING(255),
        allowNull: false,
      },
      quantity: {
        type: Sequelize.DECIMAL(13, 3),
        allowNull: false,
        set(value) {
          const fixedValue = parseFloat(value || 0).toFixed(3);
          this.setDataValue('quantity', fixedValue);
        },
        get() {
          const rawValue = this.getDataValue('quantity');
          return parseFloat(rawValue || 0);
        },
      },
      amount: {
        type: Sequelize.DOUBLE,
        allowNull: true,
      },
      discountValue: {
        type: Sequelize.DOUBLE,
        allowNull: false,
        field: 'discount_value',
      },
      discountType: {
        type: Sequelize.ENUM,
        allowNull: false,
        defaultValue: NON_RS_DISCOUNT_TYPE.FIXED,
        values: Object.values(NON_RS_DISCOUNT_TYPE),
        field: 'discount_type',
      },
      discountedPrice: {
        type: Sequelize.DOUBLE,
        allowNull: false,
        field: 'discounted_price',
      },
    },
    {
      timestamps: true,
      underscored: true,
    },
  );

  NonRequisitionItemModel.associate = (models) => {};

  return NonRequisitionItemModel;
};
