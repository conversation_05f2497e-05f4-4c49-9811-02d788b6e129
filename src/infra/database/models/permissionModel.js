const { underscoredIf } = require('sequelize/lib/utils');

module.exports = (sequelize, Sequelize) => {
  const PermissionModel = sequelize.define(
    'permissions',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      module: {
        type: Sequelize.STRING(50),
        allowNull: false,
      },
      action: {
        type: Sequelize.STRING(50),
        allowNull: false,
      },
    },
    {
      timestamps: true,
      underscored: true,
    },
  );

  /* -------------------------------------------------------------------------- */
  /*                                Associations                                */
  /* -------------------------------------------------------------------------- */

  PermissionModel.associate = (models) => {
    PermissionModel.belongsToMany(models.roleModel, {
      through: models.rolePermissionModel,
      foreignKey: 'permissionId',
      otherKey: 'roleId',
      as: 'roles',
    });
  };

  return PermissionModel;
};
