module.exports = (sequelize, Sequelize) => {
  const RequisitionApproverModel = sequelize.define(
    'requisition_approvers',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      requisitionId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'requisitions',
          key: 'id',
        },
        field: 'requisition_id',
      },
      modelId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'model_id',
      },
      approverId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id',
        },
        field: 'approver_id',
      },
      altApproverId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'alt_approver_id',
      },
      level: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      isAltApprover: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        field: 'is_alt_approver',
        defaultValue: false,
      },
      modelType: {
        type: Sequelize.STRING,
        allowNull: false,
        field: 'model_type',
      },
      status: {
        type: Sequelize.STRING,
        allowNull: false,
        field: 'status',
      },
      isOptionalApprover: {
        type: Sequelize.BOOLEAN,
        allowNull: true,
        field: 'is_optional_approver',
        defaultValue: false,
      },
      optionalApproverItemIds: {
        type: Sequelize.ARRAY(Sequelize.INTEGER),
        allowNull: true,
        defaultValue: [],
        field: 'optional_approver_item_ids',
      },
      addedBy: {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'added_by',
      },
      isAdditionalApprover: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        field: 'is_additional_approver',
        defaultValue: false,
      },
      overrideBy: {
        type: Sequelize.JSONB,
        field: 'override_by',
        defaultValue: null,
      },
      approverLevel: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'approver_level',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'created_at',
        defaultValue: Sequelize.fn('now'),
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'updated_at',
        defaultValue: Sequelize.fn('now'),
      },
    },
    {
      timestamps: true,
    },
  );

  /* -------------------------------------------------------------------------- */
  /*                                Associations                                */
  /* -------------------------------------------------------------------------- */

  RequisitionApproverModel.associate = (models) => {
    RequisitionApproverModel.belongsTo(models.userModel, {
      foreignKey: 'altApproverId',
      as: 'altApprover',
    });

    RequisitionApproverModel.belongsTo(models.userModel, {
      foreignKey: 'approverId',
      as: 'approver',
    });

    RequisitionApproverModel.belongsTo(models.requisitionModel, {
      foreignKey: 'requisitionId',
      as: 'requisition',
    });
  };

  return RequisitionApproverModel;
};
