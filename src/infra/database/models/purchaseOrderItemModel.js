module.exports = (sequelize, Sequelize) => {
  const PurchaseOrderItemModel = sequelize.define(
    'purchase_order_items',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      purchaseOrderId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'purchase_orders',
          key: 'id',
        },
        onDelete: 'CASCADE',
        field: 'purchase_order_id',
      },
      canvassItemId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'canvass_items',
          key: 'id',
        },
        field: 'canvass_item_id',
      },
      requisitionItemListId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'requisition_item_list_id',
      },
      quantityPurchased: {
        type: Sequelize.DECIMAL(13, 3),
        allowNull: false,
        defaultValue: 0,
        field: 'quantity_purchased',
        set(value) {
          const fixedValue = parseFloat(value || 0).toFixed(3);
          this.setDataValue('quantityPurchased', fixedValue);
        },
        get() {
          const rawValue = this.getDataValue('quantityPurchased');
          return parseFloat(rawValue || 0);
        },
      },
      canvassItemSupplierId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'canvass_item_suppliers',
          key: 'id',
        },
        field: 'canvass_item_supplier_id',
      },
    },
    {
      timestamps: true,
      underscored: true,
    },
  );

  // TODO: request history for items (quantity_ordered)
  PurchaseOrderItemModel.afterCreate(
    async (purchaseOrderItem, { transaction }) => {
      // fetch item name
      // must include quantity ordered from parent RS
      // await sequelize.model('requisition_item_histories').create({});
    },
  );

  PurchaseOrderItemModel.afterUpdate(
    async (purchaseOrderItem, { transaction }) => {
      // fetch item name
      // must include quantity ordered from parent RS
      // await sequelize.model('requisition_item_histories').create({});
    },
  );

  PurchaseOrderItemModel.associate = (models) => {
    PurchaseOrderItemModel.belongsTo(models.canvassItemSupplierModel, {
      foreignKey: 'canvassItemSupplierId',
      as: 'canvassItemSupplier',
    });

    PurchaseOrderItemModel.belongsTo(models.requisitionItemListModel, {
      foreignKey: 'requisitionItemListId',
      as: 'requisitionItemList',
    });

    PurchaseOrderItemModel.belongsTo(models.purchaseOrderModel, {
      foreignKey: 'purchaseOrderId',
      as: 'purchaseOrder',
    });

    PurchaseOrderItemModel.hasMany(models.deliveryReceiptItemModel, {
      foreignKey: 'poItemId',
      as: 'deliveryReceiptItems',
    });

    PurchaseOrderItemModel.belongsTo(models.canvassItemModel, {
      foreignKey: 'canvassItemId',
      as: 'canvassItem',
    });
  };

  return PurchaseOrderItemModel;
};
