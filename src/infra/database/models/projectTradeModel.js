module.exports = (sequelize, Sequelize) => {
  const ProjectTradeModel = sequelize.define(
    'projects_trades',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      projectId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'projects',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      },
      tradeId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'trades',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      },
      engineerId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      },
    },
    {
      timestamps: true,
      underscored: true,
    },
  );

  /* -------------------------------------------------------------------------- */
  /*                                Associations                                */
  /* -------------------------------------------------------------------------- */

  ProjectTradeModel.associate = (models) => {
    ProjectTradeModel.belongsTo(models.projectModel, {
      foreignKey: 'projectId',
      targetKey: 'id',
      as: 'project',
    });

    ProjectTradeModel.belongsTo(models.tradeModel, {
      foreignKey: 'tradeId',
      targetKey: 'id',
      as: 'trade',
    });

    ProjectTradeModel.belongsTo(models.userModel, {
      foreignKey: 'engineerId',
      targetKey: 'id',
      as: 'engineer',
    });
  };

  return ProjectTradeModel;
};
