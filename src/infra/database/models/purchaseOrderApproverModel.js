const {
  PO_APPROVER_STATUS,
} = require('../../../domain/constants/purchaseOrderConstants');

module.exports = (sequelize, Sequelize) => {
  const PurchaseOrderApproverModel = sequelize.define(
    'purchase_order_approvers',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      purchaseOrderId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'purchase_orders',
          key: 'id',
        },
        onDelete: 'CASCADE',
        field: 'purchase_order_id',
      },
      level: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'user_id',
      },
      status: {
        allowNull: false,
        type: Sequelize.STRING(255),
        defaultValue: PO_APPROVER_STATUS.PENDING,
      },
      roleId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'roles',
          key: 'id',
        },
        validate: {
          isInt: true,
        },
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
        field: 'role_id',
      },
      isAdhoc: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        field: 'is_adhoc',
      },
      altApproverId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'alt_approver_id',
      },
      addedBy: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id',
        },
        field: 'added_by',
      },
      overrideBy: {
        type: Sequelize.JSONB,
        field: 'override_by',
        defaultValue: null,
      },
    },
    {
      timestamps: true,
      underscored: true,
    },
  );

  PurchaseOrderApproverModel.associate = (models) => {
    PurchaseOrderApproverModel.belongsTo(models.userModel, {
      foreignKey: 'userId',
      as: 'approver',
    });

    PurchaseOrderApproverModel.belongsTo(models.userModel, {
      foreignKey: 'altApproverId',
      as: 'altApprover',
    });

    PurchaseOrderApproverModel.belongsTo(models.purchaseOrderModel, {
      foreignKey: 'purchaseOrderId',
      as: 'purchaseOrder',
    });

    PurchaseOrderApproverModel.belongsTo(models.roleModel, {
      foreignKey: 'roleId',
      as: 'role',
    });
  };

  return PurchaseOrderApproverModel;
};
