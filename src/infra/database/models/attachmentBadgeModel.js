module.exports = (sequelize, Sequelize) => {
  const AttachmentBadgeModel = sequelize.define(
    'attachment_badges',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'user_id',
      },
      attachmentId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'attachment_id',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
        field: 'created_at',
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
        field: 'updated_at',
      },
    },
    {
      timestamps: true,
    },
    {
      indexes: [
        {
          unique: true,
          fields: ['user_id', 'attachment_id'],
        },
      ],
    },
  );

  /* -------------------------------------------------------------------------- */
  /*                                Associations                                */
  /* -------------------------------------------------------------------------- */

  AttachmentBadgeModel.associate = (models) => {
    AttachmentBadgeModel.belongsTo(models.attachmentModel, {
      foreignKey: 'attachmentId',
      as: 'attachmentBadge',
    });
  };

  return AttachmentBadgeModel;
};
