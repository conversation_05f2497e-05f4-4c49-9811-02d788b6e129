module.exports = (sequelize, DataTypes) => {
  const InvoiceHistoryModel = sequelize.define(
    'invoice_report_histories',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      requisitionId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        field: 'requisition_id',
        references: {
          model: 'requisitions',
          key: 'id',
        },
      },
      purchaseOrderId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'purchase_orders',
          key: 'id',
        },
      },
      invoiceReportId: {
        type: DataTypes.INTEGER,
        references: {
          model: 'invoice_reports',
          key: 'id',
        },
        allowNull: false,
        onDelete: 'CASCADE',
      },
      irNumber: {
        type: DataTypes.STRING,
        allowNull: false,
        field: 'ir_number',
      },
      supplierInvoiceNo: {
        type: DataTypes.STRING,
        allowNull: false,
        field: 'supplier_invoice_no',
      },
      issuedInvoiceDate: {
        type: DataTypes.DATE,
        allowNull: false,
        field: 'issued_invoice_date',
      },
      invoiceAmount: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false,
        field: 'invoice_amount',
      },
      status: {
        type: DataTypes.STRING(50),
        allowNull: false,
        defaultValue: '--',
        field: 'status',
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        field: 'created_at',
        defaultValue: DataTypes.fn('now'),
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
        field: 'updated_at',
        defaultValue: DataTypes.fn('now'),
      },
    },
    {
      timestamps: true,
      underscored: true,
    },
  );

  InvoiceHistoryModel.associate = (models) => {
    InvoiceHistoryModel.belongsTo(models.invoiceReportModel, {
      foreignKey: 'invoiceReportId',
      onDelete: 'CASCADE',
    });
  };

  return InvoiceHistoryModel;
};
