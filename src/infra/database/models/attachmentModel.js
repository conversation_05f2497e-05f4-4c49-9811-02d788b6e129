module.exports = (sequelize, Sequelize) => {
  const AttachmentModel = sequelize.define(
    'attachments',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      model: {
        type: Sequelize.STRING(100),
        allowNull: false,
      },
      modelId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'model_id',
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'user_id',
      },
      fileName: {
        type: Sequelize.STRING(199),
        allowNull: false,
        field: 'file_name',
      },
      path: {
        type: Sequelize.STRING(100),
        allowNull: false,
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
        field: 'created_at',
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
        field: 'updated_at',
      },
    },
    {
      timestamps: true,
    },
  );

  /* -------------------------------------------------------------------------- */
  /*                                Associations                                */
  /* -------------------------------------------------------------------------- */

  AttachmentModel.associate = (models) => {
    AttachmentModel.belongsTo(models.supplierModel, {
      foreignKey: 'model_id',
      as: 'supplier',
    });

    AttachmentModel.belongsTo(models.userModel, {
      foreignKey: 'user_id',
      as: 'userAttachment',
    });

    AttachmentModel.hasOne(models.attachmentBadgeModel, {
      foreignKey: 'attachmentId',
      as: 'attachmentBadge',
      onDelete: 'CASCADE',
    });

    AttachmentModel.belongsTo(models.requisitionModel, {
      foreignKey: 'model_id',
      as: 'requisition',
    });

    AttachmentModel.hasMany(models.attachmentBadgeModel, {
      foreignKey: 'attachmentId',
      as: 'badges',
      onDelete: 'CASCADE',
    });
  };

  return AttachmentModel;
};
