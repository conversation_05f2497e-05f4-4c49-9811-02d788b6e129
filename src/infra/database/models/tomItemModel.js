module.exports = (sequelize, Sequelize) => {
  const TomItemModel = sequelize.define(
    'tom_items',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      name: {
        type: Sequelize.STRING(255),
        allowNull: false,
        field: 'name',
      },
      unit: {
        type: Sequelize.STRING(20),
        allowNull: false,
        field: 'unit',
      },
      quantity: {
        type: Sequelize.DECIMAL(10, 3),
        allowNull: false,
        field: 'quantity',
        set(value) {
          const fixedValue = parseFloat(value || 0).toFixed(3);
          this.setDataValue('quantity', fixedValue);
        },
        get() {
          const rawValue = this.getDataValue('quantity');
          return parseFloat(rawValue || 0);
        },
      },
      notes: {
        type: Sequelize.STRING(255),
        allowNull: true,
        field: 'notes',
      },
      requisitionId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'requisition_id',
        references: {
          model: 'requisitions',
          key: 'id',
        },
      },
    },
    {
      timestamps: true,
      underscored: true,
    },
  );

  // ItemModel.associate = (models) => {
  //   ItemModel.hasMany(models.requisitionItemListModel, {
  //     foreignKey: 'itemId',
  //     as: 'requisitionItemLists',
  //   });
  // };

  TomItemModel.associate = (models) => {
    TomItemModel.belongsTo(models.requisitionModel, {
      foreignKey: 'requisitionId',
      as: 'requisition',
    });
  };

  return TomItemModel;
};
