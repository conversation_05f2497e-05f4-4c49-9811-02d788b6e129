module.exports = (sequelize, Sequelize) => {
  const LeaveModel = sequelize.define(
    'leaves',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      userId: {
        allowNull: false,
        type: Sequelize.INTEGER,
        field: 'user_id',
      },
      startDate: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'start_date',
      },
      endDate: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'end_date',
      },
      totalDays: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'total_days',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'created_at',
        defaultValue: Sequelize.fn('now'),
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'updated_at',
        defaultValue: Sequelize.fn('now'),
      },
    },
    {
      timestamps: true,
    },
  );

  /* -------------------------------------------------------------------------- */
  /*                                Associations                                */
  /* -------------------------------------------------------------------------- */

  LeaveModel.associate = (models) => {
    LeaveModel.belongsTo(models.userModel, {
      foreignKey: 'userId',
      as: 'user',
    });
  };

  return LeaveModel;
};
