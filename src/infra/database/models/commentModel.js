module.exports = (sequelize, Sequelize) => {
  const CommentModel = sequelize.define(
    'comments',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      model: {
        type: Sequelize.STRING(100),
        allowNull: false,
      },
      modelId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'model_id',
      },
      commentedBy: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'commented_by',
        references: {
          model: 'users',
          key: 'id',
        },
      },
      comment: {
        type: Sequelize.TEXT,
        allowNull: false,
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
        field: 'created_at',
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
        field: 'updated_at',
      },
    },
    {
      timestamps: true,
    },
  );

  /* -------------------------------------------------------------------------- */
  /*                                Associations                                */
  /* -------------------------------------------------------------------------- */

  CommentModel.associate = (models) => {
    CommentModel.belongsTo(models.supplierModel, {
      foreignKey: 'model_id',
      as: 'supplier',
    });

    CommentModel.belongsTo(models.userModel, {
      foreignKey: 'commented_by',
      as: 'userComment',
    });

    CommentModel.hasOne(models.commentBadgeModel, {
      foreignKey: 'commentId',
      as: 'commentBadge',
    });
    CommentModel.belongsTo(models.requisitionModel, {
      foreignKey: 'model_id',
      as: 'requisition',
    });
  };

  return CommentModel;
};
