'use strict';
const { log } = require('../../../domain/constants');
const { PRS_JOURNEY } = log;

module.exports = (sequelize, Sequelize) => {
  const TransactionLogModel = sequelize.define(
    'transaction_logs',
    {
      time: {
        type: Sequelize.DATE,
        allowNull: false,
        primaryKey: true,
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'user_id',
        comment: 'Session user id',
      },
      rsId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'rs_id',
        comment: 'Requisitions ID',
      },
      level: {
        type: Sequelize.ENUM,
        allowNull: false,
        values: Object.values(PRS_JOURNEY),
        comment: 'Current Journey',
      },
      message: {
        type: Sequelize.TEXT,
        allowNull: false,
        comment: 'Result of current RS history transaction',
      },
      metadata: {
        type: Sequelize.JSONB,
        allowNull: true,
        comment: 'Additional contextual data about the action',
      },
    },
    {
      id: false,
      timestamps: true,
      // remap to time
      createdAt: 'time',
      updatedAt: false,
      underscored: true,
      tableName: 'transaction_logs',
    },
  );

  // No associations for the time being

  return TransactionLogModel;
};
