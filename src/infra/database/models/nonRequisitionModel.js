const {
  NON_RS_STATUS,
  NON_RS_DISCOUNT_TYPE,
} = require('../../../domain/constants/nonRSConstants');

module.exports = (sequelize, Sequelize) => {
  const NonRequisitionModel = sequelize.define(
    'non_requisitions',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      nonRsLetter: {
        type: Sequelize.STRING(2),
        allowNull: false,
        field: 'non_rs_letter',
      },
      nonRsNumber: {
        type: Sequelize.STRING(8),
        allowNull: true,
        field: 'non_rs_number',
      },
      category: {
        type: Sequelize.STRING(8),
        allowNull: false,
        field: 'category',
      },
      draftNonRsNumber: {
        type: Sequelize.STRING(8),
        allowNull: true,
        field: 'draft_non_rs_number',
      },
      chargeTo: {
        type: Sequelize.STRING(255),
        allowNull: true,
        field: 'charge_to',
      },
      chargeToId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'charge_to_id',
      },
      companyId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'company_id',
      },
      departmentId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'department_id',
      },
      projectId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'project_id',
      },
      supplierId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'supplier_id',
      },
      createdBy: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        validate: {
          isInt: true,
        },
        field: 'created_by',
      },
      invoiceDate: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'invoice_date',
      },
      status: {
        allowNull: false,
        type: Sequelize.STRING(50),
        defaultValue: NON_RS_STATUS.DRAFT,
      },
      invoiceNo: {
        allowNull: false,
        type: Sequelize.STRING(255),
        field: 'invoice_no',
      },
      payableTo: {
        allowNull: false,
        type: Sequelize.STRING(255),
        field: 'payable_to',
      },
      groupDiscountType: {
        type: Sequelize.ENUM,
        allowNull: true,
        values: Object.values(NON_RS_DISCOUNT_TYPE),
        field: 'group_discount_type',
      },
      groupDiscountPrice: {
        type: Sequelize.DECIMAL(20, 2),
        allowNull: true,
        field: 'group_discount_price',
      },
      supplierInvoiceAmount: {
        type: Sequelize.DECIMAL(20, 2),
        allowNull: true,
        field: 'supplier_invoice_amount',
      },
      totalAmount: {
        type: Sequelize.DECIMAL(20, 2),
        allowNull: true,
        field: 'total_amount',
      },
      totalAmount: {
        type: Sequelize.DECIMAL(20, 2),
        allowNull: true,
        field: 'total_amount',
      },
      totalDiscount: {
        type: Sequelize.DECIMAL(20, 2),
        allowNull: true,
        field: 'total_discount',
      },
      totalDiscountedAmount: {
        type: Sequelize.DECIMAL(20, 2),
        allowNull: true,
        field: 'total_discounted_amount',
      },

      chargeToDetails: {
        type: Sequelize.VIRTUAL,
        get() {
          switch (this.chargeTo) {
            case 'project':
              return this.chargeToProject;
            case 'company':
              return this.chargeToCompany?.category === 'company'
                ? this.company
                : null;
            case 'association':
              return this.chargeToCompany?.category === 'association'
                ? this.chargeToCompany
                : null;
            case 'supplier':
              return this.chargeToSupplier;
            default:
              return null;
          }
        },
      },
    },
    {
      timestamps: true,
      underscored: true,
    },
  );

  NonRequisitionModel.associate = (models) => {
    NonRequisitionModel.belongsTo(models.companyModel, {
      foreignKey: 'companyId',
      as: 'company',
    });

    NonRequisitionModel.belongsTo(models.supplierModel, {
      foreignKey: 'supplierId',
      as: 'supplier',
    });

    NonRequisitionModel.belongsTo(models.departmentModel, {
      foreignKey: 'departmentId',
      as: 'department',
    });

    NonRequisitionModel.belongsTo(models.projectModel, {
      foreignKey: 'projectId',
      as: 'project',
    });
    NonRequisitionModel.belongsTo(models.userModel, {
      foreignKey: 'createdBy',
      as: 'requestor',
    });

    NonRequisitionModel.belongsTo(models.companyModel, {
      foreignKey: 'chargeToId',
      as: 'chargeToCompany',
    });

    NonRequisitionModel.belongsTo(models.projectModel, {
      foreignKey: 'chargeToId',
      as: 'chargeToProject',
    });

    NonRequisitionModel.belongsTo(models.companyModel, {
      foreignKey: 'chargeToId',
      as: 'chargeToAssociation',
    });

    NonRequisitionModel.belongsTo(models.supplierModel, {
      foreignKey: 'chargeToId',
      as: 'chargeToSupplier',
    });

    NonRequisitionModel.hasMany(models.nonRequisitionApproverModel, {
      foreignKey: 'nonRequisitionId',
      sourceKey: 'id',
      as: 'approvals',
    });

    NonRequisitionModel.hasOne(models.noteModel, {
      foreignKey: 'modelId',
      sourceKey: 'id',
      as: 'note',
    });

    NonRequisitionModel.hasOne(models.noteModel, {
      foreignKey: 'modelId',
      sourceKey: 'id',
      as: 'invoiceNotes',
    });

    NonRequisitionModel.hasMany(models.attachmentModel, {
      foreignKey: 'modelId',
      sourceKey: 'id',
      as: 'attachments',
    });

    NonRequisitionModel.hasMany(models.attachmentModel, {
      foreignKey: 'modelId',
      sourceKey: 'id',
      as: 'invoiceAttachment',
    });
  };

  return NonRequisitionModel;
};
