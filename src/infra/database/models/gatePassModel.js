module.exports = (sequelize, Sequelize) => {
  const GatePassModel = sequelize.define('gate_passes', {
    id: {
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
      type: Sequelize.INTEGER,
    },
    poId: {
      type: Sequelize.INTEGER,
      allowNull: false,
      field: 'purchase_order_id',
    },
    gatePassNumber: {
      type: Sequelize.INTEGER,
      allowNull: false,
      field: 'gate_pass_number',
    },
    createdAt: {
      allowNull: false,
      type: Sequelize.DATE,
      defaultValue: Sequelize.fn('now'),
    },
    updatedAt: {
      allowNull: false,
      type: Sequelize.DATE,
      defaultValue: Sequelize.fn('now'),
    },
    requisitionId: {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: 'requisitions',
        key: 'id',
      },
      field: 'requisition_id',
    },
  });

  /* -------------------------------------------------------------------------- */
  /*                                Associations                                */
  /* -------------------------------------------------------------------------- */

  GatePassModel.associate = (models) => {
    GatePassModel.belongsTo(models.requisitionModel, {
      foreignKey: 'requisitionId',
      as: 'requisition',
    });
  };

  return GatePassModel;
};
