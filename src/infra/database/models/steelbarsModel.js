module.exports = (sequelize, Sequelize) => {
  const SteelbarsModel = sequelize.define(
    'steelbars',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      grade: {
        type: Sequelize.STRING(20),
        allowNull: false,
      },
      diameter: {
        type: Sequelize.DECIMAL,
        allowNull: false,
      },
      length: {
        type: Sequelize.DECIMAL,
        allowNull: false,
      },
      weight: {
        type: Sequelize.DECIMAL,
        allowNull: false,
        get() {
          return parseFloat(this.getDataValue('weight')).toFixed(3);
        },
      },
      kgPerMeter: {
        type: Sequelize.DECIMAL,
        allowNull: false,
        field: 'kg_per_meter',
      },
      ofmAcctcd: {
        type: Sequelize.STRING(50),
        allowNull: true,
        field: 'ofm_acctcd',
      },
    },
    {
      timestamps: true,
      underscored: true,
    },
  );

  /* -------------------------------------------------------------------------- */
  /*                                Associations                                  */
  /* -------------------------------------------------------------------------- */

  SteelbarsModel.associate = (models) => {
    SteelbarsModel.belongsTo(models.itemModel, {
      foreignKey: 'ofm_acctcd',
      targetKey: 'acctCd',
      as: 'item',
    });
  };

  return SteelbarsModel;
};
