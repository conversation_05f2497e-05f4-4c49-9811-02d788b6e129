const { USER_STATUS } = require('../../../domain/constants/userConstants');

module.exports = (sequelize, Sequelize) => {
  const UserModel = sequelize.define(
    'users',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      username: {
        type: Sequelize.STRING(255),
        allowNull: false,
        unique: true,
        validate: {
          len: [5, 50],
        },
        indexes: [{ unique: true, fields: ['username'] }],
      },
      email: {
        type: Sequelize.STRING(255),
        allowNull: true,
        unique: true,
        validate: {
          isEmail: { ignore_max_length: true },
          len: { args: [1, 100], msg: 'Email must be at most 100 characters' },
        },
        indexes: [{ unique: true, fields: ['email'] }],
      },
      password: {
        type: Sequelize.STRING(255),
        allowNull: false,
        validate: {
          len: [8, 255],
        },
      },
      tempPass: {
        type: Sequelize.STRING(100),
        allowNull: true,
        field: 'temp_pass',
      },
      firstName: {
        type: Sequelize.STRING(50),
        allowNull: true,
        field: 'first_name',
      },
      lastName: {
        type: Sequelize.STRING(50),
        allowNull: true,
        field: 'last_name',
      },
      roleId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'roles',
          key: 'id',
        },
        validate: {
          isInt: true,
        },
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
        field: 'role_id',
      },
      departmentId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'departments',
          key: 'id',
        },
        validate: {
          isInt: true,
        },
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
        field: 'department_id',
      },
      otpSecret: {
        type: Sequelize.STRING(512),
        allowNull: true,
        field: 'otp_secret',
      },
      status: {
        type: Sequelize.ENUM,
        allowNull: false,
        defaultValue: USER_STATUS.ACTIVE,
        values: Object.values(USER_STATUS),
      },
      isPasswordTemporary: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true,
        field: 'is_password_temporary',
      },
      supervisorId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id',
        },
        validate: {
          isInt: true,
        },
        onDelete: 'SET NULL',
        onUpdate: 'CASCADE',
        field: 'supervisor_id',
      },
      fullNameUser: {
        type: Sequelize.VIRTUAL,
        get() {
          return `${this.firstName} ${this.lastName}`;
        },
      },
    },
    {
      paranoid: true,
      timestamps: true,
      underscored: true,
    },
  );

  /* -------------------------------------------------------------------------- */
  /*                                Associations                                */
  /* -------------------------------------------------------------------------- */

  UserModel.associate = (models) => {
    UserModel.belongsTo(UserModel, {
      foreignKey: 'supervisorId',
      as: 'supervisor',
    });

    UserModel.belongsTo(models.roleModel, { foreignKey: 'roleId', as: 'role' });

    UserModel.belongsTo(models.departmentModel, {
      foreignKey: 'departmentId',
      as: 'department',
    });

    UserModel.hasMany(models.notificationModel, {
      foreignKey: 'recipientUserIds',
      as: 'userNotifications',
      constraints: false,
      scope: {
        recipientUserIds: {
          [models.Sequelize.Op.contains]: [models.Sequelize.col('user.id')],
        },
      },
    });

    UserModel.hasMany(models.leaveModel, {
      foreignKey: 'userId',
      as: 'userLeaves',
    });

    UserModel.hasMany(models.requisitionModel, {
      foreignKey: 'createdBy',
      as: 'requisitions',
    });

    UserModel.hasMany(models.requisitionApproverModel, {
      foreignKey: 'approverId',
      as: 'approvers',
    });
  };

  return UserModel;
};
