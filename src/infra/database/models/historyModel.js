module.exports = (sequelize, Sequelize) => {
  const HistoryModel = sequelize.define('histories', {
    id: {
      autoIncrement: true,
      primaryKey: true,
      type: Sequelize.INTEGER,
    },
    rsNumber: {
      type: Sequelize.STRING(8),
      field: 'rs_number',
    },
    rsLetter: {
      type: Sequelize.STRING(2),
      field: 'rs_letter',
    },
    type: {
      type: Sequelize.STRING(255),
      field: 'type',
    },
    itemId: {
      type: Sequelize.INTEGER,
      allowNull: false,
      field: 'item_id',
    },
    dateRequested: {
      allowNull: false,
      type: Sequelize.DATE,
      field: 'date_requested',
    },
    quantityRequested: {
      type: Sequelize.DECIMAL(13, 3),
      field: 'quantity_requested',
    },
    companyId: {
      type: Sequelize.INTEGER,
      field: 'company_id',
      allowNull: true,
      references: {
        model: 'companies',
        key: 'id',
      },
    },
    projectId: {
      type: Sequelize.INTEGER,
      field: 'project_id',
      allowNull: true,
      references: {
        model: 'projects',
        key: 'id',
      },
    },
    departmentId: {
      type: Sequelize.INTEGER,
      field: 'department_id',
      allowNull: true,
      references: {
        model: 'departments',
        key: 'id',
      },
    },
    price: {
      type: Sequelize.STRING,
      field: 'price',
      allowNull: true,
    },
    quantityDelivered: {
      allowNull: true,
      type: Sequelize.DECIMAL(13, 3),
      field: 'quantity_delivered',
    },
    dateDelivered: {
      allowNull: true,
      type: Sequelize.DATE,
      field: 'date_delivered',
    },
    drItemId: {
      allowNull: true,
      type: Sequelize.INTEGER,
      field: 'dr_item_id',
      references: {
        model: 'delivery_receipt_items',
        key: 'id',
      },
    },
    createdAt: {
      type: Sequelize.DATE,
      allowNull: false,
      field: 'created_at',
      defaultValue: Sequelize.fn('now'),
    },
    updatedAt: {
      type: Sequelize.DATE,
      allowNull: false,
      field: 'updated_at',
      defaultValue: Sequelize.fn('now'),
    },
  });

  /* -------------------------------------------------------------------------- */
  /*                                Associations                                */
  /* -------------------------------------------------------------------------- */

  HistoryModel.associate = (models) => {
    HistoryModel.belongsTo(models.itemModel, {
      foreignKey: 'itemId',
      as: 'item',
    });

    HistoryModel.belongsTo(models.companyModel, {
      foreignKey: 'companyId',
      as: 'companies',
    });

    HistoryModel.belongsTo(models.projectModel, {
      foreignKey: 'projectId',
      as: 'projects',
    });

    HistoryModel.belongsTo(models.departmentModel, {
      foreignKey: 'departmentId',
      as: 'departments',
    });
  };
  return HistoryModel;
};
