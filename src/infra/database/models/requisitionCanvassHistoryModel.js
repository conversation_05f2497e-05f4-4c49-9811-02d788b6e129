module.exports = (sequelize, Sequelize) => {
  const RequisitionCanvassHistoryModel = sequelize.define(
    'requisition_canvass_histories',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      requisitionId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'requisitions',
          key: 'id',
        },
        field: 'requisition_id',
      },
      requisitionItemListId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'requisition_item_lists',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'requisition_item_list_id',
      },
      supplierId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'suppliers',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'supplier_id',
      },
      canvassRequisitionId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'canvass_requisitions',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'canvass_requisition_id',
      },
      canvassNumber: {
        type: Sequelize.STRING,
        allowNull: false,
        field: 'canvass_number',
      },
      supplier: {
        type: Sequelize.STRING,
        allowNull: false,
        field: 'supplier',
      },
      item: {
        type: Sequelize.STRING,
        allowNull: false,
        field: 'item',
      },
      price: {
        type: Sequelize.FLOAT,
        allowNull: false,
        field: 'price',
      },
      discount: {
        type: Sequelize.FLOAT,
        allowNull: false,
        field: 'discount',
      },
      canvassDate: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'canvass_date',
        defaultValue: Sequelize.fn('now'),
      },
      status: {
        type: Sequelize.STRING,
        allowNull: false,
        field: 'status',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'created_at',
        defaultValue: Sequelize.fn('now'),
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'updated_at',
        defaultValue: Sequelize.fn('now'),
      },
    },
  );

  /* -------------------------------------------------------------------------- */
  /*                                Associations                                */
  /* -------------------------------------------------------------------------- */

  RequisitionCanvassHistoryModel.associate = (models) => {};

  return RequisitionCanvassHistoryModel;
};
