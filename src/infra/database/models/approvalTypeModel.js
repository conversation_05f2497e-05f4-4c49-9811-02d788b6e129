module.exports = (sequelize, Sequelize) => {
  const ApprovalTypeModel = sequelize.define(
    'approval_types',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      name: {
        type: Sequelize.STRING(100),
        allowNull: false,
        unique: true,
      },
      code: {
        type: Sequelize.STRING(50),
        allowNull: false,
        unique: true,
      },
    },
    {
      timestamps: true,
      underscored: true,
    },
  );
  return ApprovalTypeModel;
};
