module.exports = (sequelize, Sequelize) => {
  const RequisitionReturnHistoryModel = sequelize.define(
    'requisition_return_histories',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      requisitionId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'requisitions',
          key: 'id',
        },
        field: 'requisition_id',
      },
      drNumber: {
        type: Sequelize.STRING,
        allowNull: false,
        field: 'dr_number',
      },
      item: {
        type: Sequelize.STRING,
        allowNull: false,
        field: 'item',
      },
      supplier: {
        type: Sequelize.STRING,
        allowNull: false,
        field: 'supplier',
      },
      status: {
        type: Sequelize.STRING,
        allowNull: false,
        field: 'status',
      },
      quantityOrdered: {
        type: Sequelize.DECIMAL(13, 3),
        allowNull: false,
        field: 'quantity_ordered',
      },
      quantityReturned: {
        type: Sequelize.DECIMAL(13, 3),
        allowNull: false,
        field: 'quantity_returned',
      },
      returnDate: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'return_date',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'created_at',
        defaultValue: Sequelize.fn('now'),
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'updated_at',
        defaultValue: Sequelize.fn('now'),
      },
    },
  );

  /* -------------------------------------------------------------------------- */
  /*                                Associations                                */
  /* -------------------------------------------------------------------------- */

  RequisitionReturnHistoryModel.associate = (models) => {};

  return RequisitionReturnHistoryModel;
};
