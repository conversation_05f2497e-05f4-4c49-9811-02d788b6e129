const { convertDateToDDMMMYYYY } = require('../../../app/utils');

module.exports = (sequelize, Sequelize) => {
  const ForceCloseLogModel = sequelize.define(
    'force_close_logs',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      requisitionId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'requisition_id',
        references: {
          model: 'requisitions',
          key: 'id',
        },
        validate: {
          isInt: true,
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'user_id',
        references: {
          model: 'users',
          key: 'id',
        },
        validate: {
          isInt: true,
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      },
      scenarioType: {
        type: Sequelize.STRING(50),
        allowNull: false,
        field: 'scenario_type',
        validate: {
          isIn: [
            [
              'ACTIVE_PO_PARTIAL_DELIVERY',
              'CLOSED_PO_WITH_REMAINING_CANVASS_QTY',
              'CLOSED_PO_PENDING_CS',
            ],
          ],
        },
      },
      validationPath: {
        type: Sequelize.STRING(50),
        allowNull: false,
        field: 'validation_path',
        validate: {
          isIn: [['ACTIVE_PO_PATH', 'CLOSED_PO_PATH']],
        },
      },
      quantitiesAffected: {
        type: Sequelize.JSON,
        allowNull: true,
        field: 'quantities_affected',
        get() {
          const value = this.getDataValue('quantitiesAffected');
          return value
            ? typeof value === 'string'
              ? JSON.parse(value)
              : value
            : null;
        },
        set(value) {
          this.setDataValue(
            'quantitiesAffected',
            value ? JSON.stringify(value) : null,
          );
        },
      },
      documentsCancelled: {
        type: Sequelize.JSON,
        allowNull: true,
        field: 'documents_cancelled',
        get() {
          const value = this.getDataValue('documentsCancelled');
          return value
            ? typeof value === 'string'
              ? JSON.parse(value)
              : value
            : null;
        },
        set(value) {
          this.setDataValue(
            'documentsCancelled',
            value ? JSON.stringify(value) : null,
          );
        },
      },
      poAdjustments: {
        type: Sequelize.JSON,
        allowNull: true,
        field: 'po_adjustments',
        get() {
          const value = this.getDataValue('poAdjustments');
          return value
            ? typeof value === 'string'
              ? JSON.parse(value)
              : value
            : null;
        },
        set(value) {
          this.setDataValue(
            'poAdjustments',
            value ? JSON.stringify(value) : null,
          );
        },
      },
      forceCloseNotes: {
        type: Sequelize.TEXT,
        allowNull: false,
        field: 'force_close_notes',
        validate: {
          len: [1, 500], // Match entity schema: minimum 1 character, maximum 500
        },
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'created_at',
        defaultValue: Sequelize.fn('now'),
        get() {
          return convertDateToDDMMMYYYY(this.getDataValue('createdAt'));
        },
      },
    },
    {
      timestamps: false, // We only need createdAt, not updatedAt
      underscored: true,
      indexes: [
        {
          fields: ['requisition_id'],
          name: 'idx_force_close_logs_requisition_id',
        },
        {
          fields: ['user_id'],
          name: 'idx_force_close_logs_user_id',
        },
        {
          fields: ['scenario_type'],
          name: 'idx_force_close_logs_scenario_type',
        },
        {
          fields: ['created_at'],
          name: 'idx_force_close_logs_created_at',
        },
      ],
    },
  );

  ForceCloseLogModel.associate = (models) => {
    ForceCloseLogModel.belongsTo(models.requisitionModel, {
      foreignKey: 'requisitionId',
      as: 'requisition',
    });

    ForceCloseLogModel.belongsTo(models.userModel, {
      foreignKey: 'userId',
      as: 'user',
    });
  };

  return ForceCloseLogModel;
};
