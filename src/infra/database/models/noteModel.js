module.exports = (sequelize, DataTypes) => {
  const NoteModel = sequelize.define(
    'notes',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      model: {
        type: DataTypes.STRING(100),
        allowNull: false,
      },
      modelId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      userName: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      userType: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      commentType: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      note: {
        type: DataTypes.TEXT,
      },
    },
    {
      underscored: true,
      timestamps: true,
    },
  );

  NoteModel.associate = (models) => {
    NoteModel.hasMany(models.noteBadgeModel, {
      foreignKey: 'noteId',
      as: 'badges',
      onDelete: 'CASCADE',
    });
  };

  return NoteModel;
};
