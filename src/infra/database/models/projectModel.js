module.exports = (sequelize, Sequelize) => {
  const ProjectModel = sequelize.define(
    'projects',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      code: {
        type: Sequelize.STRING(255),
        allowNull: false,
        unique: true,
      },
      name: {
        type: Sequelize.STRING(255),
        allowNull: false,
      },
      initial: {
        type: Sequelize.STRING(255),
        allowNull: false,
      },
      companyCode: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      address: {
        type: Sequelize.STRING(255),
        allowNull: true,
      },
    },
    {
      timestamps: true,
      underscored: true,
    },
  );

  /* -------------------------------------------------------------------------- */
  /*                                Associations                                */
  /* -------------------------------------------------------------------------- */

  ProjectModel.associate = (models) => {
    ProjectModel.belongsToMany(models.companyModel, {
      through: 'project_companies',
      foreignKey: 'projectId',
      otherKey: 'companyId',
      as: 'taggedCompanies',
    });

    ProjectModel.belongsTo(models.companyModel, {
      foreignKey: 'companyCode',
      targetKey: 'code',
      as: 'company',
    });

    ProjectModel.hasMany(models.ofmItemListModel, {
      foreignKey: 'project_code',
      sourceKey: 'code',
      as: 'ofmItemLists',
    });

    ProjectModel.hasMany(models.requisitionModel, {
      foreignKey: 'projectId',
      as: 'requisitions',
    });

    ProjectModel.hasMany(models.nonRequisitionModel, {
      foreignKey: 'projectId',
      as: 'nonRS',
    });

    ProjectModel.hasMany(models.projectTradeModel, {
      foreignKey: 'projectId',
      as: 'projectTrades',
    });
  };

  return ProjectModel;
};
