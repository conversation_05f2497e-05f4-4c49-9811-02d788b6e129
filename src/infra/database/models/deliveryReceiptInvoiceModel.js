module.exports = (sequelize, DataTypes) => {
  const DeliveryReceiptInvoice = sequelize.define(
    'delivery_receipt_invoices',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      deliveryReceiptId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'delivery_receipts',
          key: 'id',
        },
      },
      invoiceNo: {
        type: DataTypes.STRING,
      },
      issuedInvoiceDate: {
        type: DataTypes.DATE,
      },
      totalSales: {
        type: DataTypes.DECIMAL(20, 2),
      },
      vatAmount: {
        type: DataTypes.DECIMAL(20, 2),
      },
      vatExemptedAmount: {
        type: DataTypes.DECIMAL(20, 2),
      },
      zeroRatedAmount: {
        type: DataTypes.DECIMAL(20, 2),
      },
    },
    {
      timestamps: true,
      underscored: true,
    },
  );

  DeliveryReceiptInvoice.associate = (models) => {
    DeliveryReceiptInvoice.belongsTo(models.deliveryReceiptModel, {
      foreignKey: 'deliveryReceiptId',
      onDelete: 'CASCADE',
    });

    DeliveryReceiptInvoice.hasOne(models.attachmentModel, {
      foreignKey: 'modelId',
      as: 'invoiceAttachment',
    });
  };

  return DeliveryReceiptInvoice;
};
