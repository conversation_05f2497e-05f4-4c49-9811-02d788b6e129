const {
  DELIVERY_ITEM_STATUSES,
} = require('../../../domain/constants/deliveryReceiptItemConstants');

module.exports = (sequelize, Sequelize) => {
  const RequisitionDeliveryHistoryModel = sequelize.define(
    'requisition_delivery_histories',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      requisitionId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'requisitions',
          key: 'id',
        },
        field: 'requisition_id',
      },
      drNumber: {
        type: Sequelize.STRING,
        allowNull: false,
        field: 'dr_number',
      },
      supplier: {
        type: Sequelize.STRING,
        allowNull: false,
        field: 'supplier',
      },
      dateOrdered: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'date_ordered',
      },
      quantityOrdered: {
        type: Sequelize.DECIMAL(13, 3),
        allowNull: false,
        field: 'quantity_ordered',
      },
      quantityDelivered: {
        type: Sequelize.DECIMAL(13, 3),
        allowNull: false,
        field: 'quantity_delivered',
      },
      dateDelivered: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'date_delivered',
      },
      status: {
        type: Sequelize.STRING,
        allowNull: false,
        field: 'status',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'created_at',
        defaultValue: Sequelize.fn('now'),
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'updated_at',
        defaultValue: Sequelize.fn('now'),
      },
    },
  );

  /* -------------------------------------------------------------------------- */
  /*                                Associations                                */
  /* -------------------------------------------------------------------------- */

  RequisitionDeliveryHistoryModel.associate = (models) => {};

  return RequisitionDeliveryHistoryModel;
};
