const { SUPPLIER_TYPE } = require('../../../domain/constants/canvassConstants');
const {
  PO_ITEM_STATUS,
} = require('../../../domain/constants/purchaseOrderConstants');

module.exports = (sequelize, Sequelize) => {
  const PurchaseOrderCancelledItemsModel = sequelize.define(
    'purchase_order_cancelled_items',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      purchaseOrderId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'purchase_orders',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'purchase_order_id',
      },
      requisitionId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'requisitions',
          key: 'id',
        },
        validate: {
          isInt: true,
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'requisition_id',
      },
      canvassRequisitionId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'canvass_requisitions',
          key: 'id',
        },
        validate: {
          isInt: true,
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'canvass_requisition_id',
      },
      canvassItemId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'canvass_items',
          key: 'id',
        },
        validate: {
          isInt: true,
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'canvass_item_id',
      },
      requisitionItemListId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'requisition_item_lists',
          key: 'id',
        },
        validate: {
          isInt: true,
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'requisition_item_list_id',
      },
      supplierId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        validate: {
          isInt: true,
        },
        field: 'supplier_id',
      },
      supplierType: {
        allowNull: false,
        defaultValue: SUPPLIER_TYPE.SUPPLIER,
        type: Sequelize.ENUM(Object.values(SUPPLIER_TYPE)),
        field: 'supplier_type',
      },
      status: {
        type: Sequelize.STRING,
        defaultValue: PO_ITEM_STATUS.NEW,
        allowNull: false,
        field: 'status',
      },
      supplierDetails: {
        type: Sequelize.VIRTUAL,
        get() {
          switch (this.supplierType) {
            case 'supplier':
              return this.supplier;
            case 'project':
              return this.project;
            case 'company':
              return this.company;
            default:
              return null;
          }
        },
      },
    },
    {
      timestamps: true,
      underscored: true,
    },
  );

  PurchaseOrderCancelledItemsModel.associate = (models) => {
    PurchaseOrderCancelledItemsModel.belongsTo(
      models.requisitionItemListModel,
      {
        foreignKey: 'requisitionItemListId',
        as: 'requisitionItem',
      },
    );

    PurchaseOrderCancelledItemsModel.belongsTo(models.requisitionModel, {
      foreignKey: 'requisitionId',
      as: 'requisition',
    });

    PurchaseOrderCancelledItemsModel.belongsTo(models.canvassRequisitionModel, {
      foreignKey: 'canvassRequisitionId',
      as: 'canvassRequisition',
    });

    PurchaseOrderCancelledItemsModel.belongsTo(models.canvassItemModel, {
      foreignKey: 'canvassItemId',
      as: 'canvassItem',
    });

    PurchaseOrderCancelledItemsModel.belongsTo(models.supplierModel, {
      foreignKey: 'supplierId',
      as: 'supplier',
    });

    PurchaseOrderCancelledItemsModel.belongsTo(models.projectModel, {
      foreignKey: 'supplierId',
      as: 'project',
    });

    PurchaseOrderCancelledItemsModel.belongsTo(models.companyModel, {
      foreignKey: 'supplierId',
      as: 'company',
    });
  };

  return PurchaseOrderCancelledItemsModel;
};
