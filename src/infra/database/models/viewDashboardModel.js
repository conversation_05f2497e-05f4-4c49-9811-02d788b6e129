const requisitionModel = require('./requisitionModel');

module.exports = (sequelize, Sequelize) => {
  const ViewDashboardModel = sequelize.define('vw_dashboard_requisitions', {
    id: {
      type: Sequelize.INTEGER,
      allowNull: false,
      field: 'requisition_id',
      primaryKey: true,
    },
    refId: {
      allowNull: false,
      type: Sequelize.INTEGER,
      field: 'ref_id',
    },
    refType: {
      type: Sequelize.TEXT,
      allowNull: false,
      field: 'ref_type',
    },
    rsCombinedNumber: {
      type: Sequelize.TEXT,
      allowNull: false,
      field: 'rs_combined_number',
    },
    canvassNumber: {
      type: Sequelize.TEXT,
      allowNull: false,
      field: 'canvass_number',
    },
    poNumber: {
      type: Sequelize.TEXT,
      allowNull: false,
      field: 'po_number',
    },
    prNumber: {
      type: Sequelize.TEXT,
      allowNull: false,
      field: 'pr_number',
    },
    drNumber: {
      type: Sequelize.TEXT,
      allowNull: false,
      field: 'dr_number',
    },
    // Force close fields (added in optimization)
    forceClosedAt: {
      type: Sequelize.DATE,
      allowNull: true,
      field: 'force_closed_at',
    },
    forceClosedBy: {
      type: Sequelize.INTEGER,
      allowNull: true,
      field: 'force_closed_by',
    },
    forceCloseReason: {
      type: Sequelize.TEXT,
      allowNull: true,
      field: 'force_close_reason',
    },
    forceCloseScenario: {
      type: Sequelize.STRING(50),
      allowNull: true,
      field: 'force_close_scenario',
    },
    refStatus: {
      type: Sequelize.STRING,
      allowNull: false,
      field: 'ref_status',
    },
    refCreatedAt: {
      type: Sequelize.DATE,
      allowNull: false,
      field: 'ref_created_at',
    },
    refUpdatedAt: {
      type: Sequelize.DATE,
      allowNull: false,
      field: 'ref_updated_at',
    },
    refUpdatedAt: {
      type: Sequelize.DATE,
      allowNull: false,
      field: 'ref_updated_at',
    },
    requestorFullName: {
      type: Sequelize.TEXT,
      allowNull: false,
      field: 'requestor_full_name',
    },
    companyName: {
      type: Sequelize.STRING(255),
      allowNull: false,
      field: 'company_name',
    },
    projectName: {
      type: Sequelize.STRING(255),
      allowNull: false,
      field: 'project_name',
    },
    departmentName: {
      type: Sequelize.STRING(255),
      allowNull: false,
      field: 'department_name',
    },
    status: {
      type: Sequelize.STRING(20),
      allowNull: false,
      field: 'status',
    },
    csId: {
      type: Sequelize.INTEGER,
      allowNull: true,
      field: 'cs_id',
    },
    poId: {
      type: Sequelize.INTEGER,
      allowNull: true,
      field: 'po_id',
    },
    drId: {
      type: Sequelize.INTEGER,
      allowNull: true,
      field: 'dr_id',
    },
    prId: {
      type: Sequelize.INTEGER,
      allowNull: true,
      field: 'pr_id',
    },
  });

  /* -------------------------------------------------------------------------- */
  /*                                Associations                                */
  /* -------------------------------------------------------------------------- */

  ViewDashboardModel.associate = (models) => {
    ViewDashboardModel.belongsTo(models.requisitionModel, {
      foreignKey: 'id',
      as: 'requisition',
    });
  };

  return ViewDashboardModel;
};
