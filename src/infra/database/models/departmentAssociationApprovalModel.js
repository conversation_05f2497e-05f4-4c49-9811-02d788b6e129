module.exports = (sequelize, Sequelize) => {
  const DepartmentAssociationApprovalModel = sequelize.define(
    'department_association_approvals',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      approvalTypeCode: {
        type: Sequelize.STRING(50),
        allowNull: false,
        references: {
          model: 'approval_types',
          key: 'code',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'approval_type_code',
      },
      level: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      areaCode: {
        type: Sequelize.STRING(100),
        allowNull: true,
        field: 'area_code',
      },
      approverId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        field: 'approver_id',
      },
    },
    {
      timestamps: true,
      underscored: true,
    },
  );

  DepartmentAssociationApprovalModel.associate = (models) => {
    DepartmentAssociationApprovalModel.belongsTo(models.userModel, {
      foreignKey: 'approverId',
      as: 'approver',
      targetKey: 'id',
    });
  };

  return DepartmentAssociationApprovalModel;
};
