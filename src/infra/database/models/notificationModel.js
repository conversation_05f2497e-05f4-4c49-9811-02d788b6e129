module.exports = (sequelize, Sequelize) => {
  const NotificationModel = sequelize.define(
    'notifications',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      title: {
        type: Sequelize.STRING(100),
        allowNull: false,
        validate: {
          len: [1, 100],
        },
      },
      message: {
        type: Sequelize.TEXT,
        allowNull: false,
        validate: {
          notEmpty: true,
        },
      },
      type: {
        type: Sequelize.STRING(50),
        allowNull: false,
      },
      recipientRoleId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'roles',
          key: 'id',
        },
        validate: {
          isInt: true,
        },
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
        field: 'recipient_role_id',
      },
      recipientUserIds: {
        type: Sequelize.ARRAY(Sequelize.INTEGER),
        allowNull: true,
        defaultValue: [],
        field: 'recipient_user_ids',
      },
      senderId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        validate: {
          isInt: true,
        },
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
        field: 'sender_id',
      },
      viewedBy: {
        type: Sequelize.ARRAY(Sequelize.INTEGER),
        allowNull: false,
        defaultValue: [],
        field: 'viewed_by',
      },
      metaData: {
        type: Sequelize.JSON,
        allowNull: true,
        field: 'meta_data',
      },
    },
    {
      paranoid: true,
      timestamps: true,
      underscored: true,
    },
  );

  /* -------------------------------------------------------------------------- */
  /*                                Associations                                */
  /* -------------------------------------------------------------------------- */

  NotificationModel.associate = (models) => {
    NotificationModel.belongsTo(models.userModel, {
      foreignKey: 'senderId',
      as: 'sender',
    });
  };

  return NotificationModel;
};
