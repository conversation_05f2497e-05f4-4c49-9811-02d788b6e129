module.exports = (sequelize, Sequelize) => {
  const TradeModel = sequelize.define(
    'trades',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      tradeName: {
        type: Sequelize.STRING(100),
        allowNull: false,
        field: 'trade_name',
      },
      tradeCode: {
        type: Sequelize.INTEGER,
        allowNull: false,
        unique: true,
        field: 'trade_code',
      },
      category: {
        type: Sequelize.ENUM('MAJOR', 'SUB'),
        allowNull: false,
        field: 'category',
      },
    },
    {
      timestamps: true,
      underscored: true,
    },
  );

  /* -------------------------------------------------------------------------- */
  /*                                Associations                                */
  /* -------------------------------------------------------------------------- */

  TradeModel.associate = (models) => {
    TradeModel.hasMany(models.ofmItemListModel, {
      foreignKey: 'trade_code',
      sourceKey: 'tradeCode',
      as: 'ofmItemLists',
    });

    TradeModel.hasMany(models.itemModel, {
      foreignKey: 'trade_code',
      sourceKey: 'tradeCode',
      as: 'items',
    });
  };

  return TradeModel;
};
