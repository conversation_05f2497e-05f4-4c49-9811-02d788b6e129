module.exports = (sequelize, Sequelize) => {
  const ProjectApprovalModel = sequelize.define(
    'project_approvals',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      projectId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'projects',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'project_id',
      },
      approvalTypeCode: {
        type: Sequelize.STRING(50),
        allowNull: false,
        references: {
          model: 'approval_types',
          key: 'code',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'approval_type_code',
      },
      level: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      isOptional: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        field: 'is_optional',
      },
      approverId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        field: 'approver_id',
      },
    },
    {
      timestamps: true,
      underscored: true,
    },
  );

  /* -------------------------------------------------------------------------- */
  /*                                Associations                                */
  /* -------------------------------------------------------------------------- */

  ProjectApprovalModel.associate = (models) => {
    ProjectApprovalModel.belongsTo(models.userModel, {
      foreignKey: 'approverId',
      as: 'approver',
      targetKey: 'id',
    });

    ProjectApprovalModel.belongsTo(models.projectModel, {
      foreignKey: 'projectId',
      as: 'project',
    });
  };

  return ProjectApprovalModel;
};
