module.exports = (sequelize, Sequelize) => {
  const NoteBadgeModel = sequelize.define(
    'note_badges',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'user_id',
      },
      noteId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'note_id',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
        field: 'created_at',
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
        field: 'updated_at',
      },
    },
    {
      timestamps: true,
    },
    {
      indexes: [
        {
          unique: true,
          fields: ['user_id', 'note_id'],
        },
      ],
    },
  );

  /* -------------------------------------------------------------------------- */
  /*                                Associations                                */
  /* -------------------------------------------------------------------------- */

  NoteBadgeModel.associate = (models) => {
    NoteBadgeModel.belongsTo(models.noteModel, {
      foreignKey: 'noteId',
      as: 'badges',
    });
  };

  return NoteBadgeModel;
};
