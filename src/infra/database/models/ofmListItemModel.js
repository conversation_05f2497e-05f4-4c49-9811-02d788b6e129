module.exports = (sequelize, Sequelize) => {
  const OfmListItemModel = sequelize.define(
    'ofm_list_items',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      ofmListId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'ofm_list_id',
      },
      ofmItemId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'ofm_item_id',
      },
    },
    {
      timestamps: true,
      underscored: true,
      indexes: [
        {
          unique: true,
          fields: ['ofm_list_id', 'ofm_item_id'],
          name: 'unique_ofm_list_item',
        },
      ],
    },
  );

  /* -------------------------------------------------------------------------- */
  /*                                Associations                                */
  /* -------------------------------------------------------------------------- */

  OfmListItemModel.associate = (models) => {
    OfmListItemModel.belongsTo(models.ofmItemListModel, {
      foreignKey: 'ofm_list_id',
      as: 'ofmList',
    });

    OfmListItemModel.belongsTo(models.itemModel, {
      foreignKey: 'ofm_item_id',
      as: 'item',
    });
  };

  return OfmListItemModel;
};
