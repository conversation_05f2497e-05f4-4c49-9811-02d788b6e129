module.exports = (sequelize, Sequelize) => {
  const SteelbarMatrixModel = sequelize.define(
    'steelbar_matrix',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      grade: {
        type: Sequelize.STRING,
        allowNull: false,
        get() {
          return this.getDataValue('grade').toString();
        },
      },
      diameter: {
        type: Sequelize.DECIMAL,
        allowNull: false,
        get() {
          return this.getDataValue('diameter').toString();
        },
      },
      length: {
        type: Sequelize.DECIMAL,
        allowNull: false,
        get() {
          return this.getDataValue('length').toString();
        },
      },
      weight: {
        type: Sequelize.DECIMAL,
        allowNull: false,
        get() {
          return this.getDataValue('weight').toString();
        },
      },
      weightPerPiece: {
        type: Sequelize.VIRTUAL,
        get() {
          return parseFloat(
            (this.getDataValue('weight') * this.getDataValue('length')).toFixed(
              3,
            ),
          );
        },
      },
      pricePerPiece: {
        type: Sequelize.VIRTUAL,
        get() {
          return parseFloat(
            (
              this.getDataValue('pricePerKg') * this.getDataValue('length')
            ).toFixed(2),
          );
        },
      },
    },
    {
      timestamps: true,
      underscored: true,
      tableName: 'steelbar_matrix',
    },
  );

  return SteelbarMatrixModel;
};
