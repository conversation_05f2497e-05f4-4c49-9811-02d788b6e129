module.exports = (sequelize, Sequelize) => {
  const DepartmentApprovalModel = sequelize.define(
    'department_approvals',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      departmentId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'departments',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'department_id',
      },
      approvalTypeCode: {
        type: Sequelize.STRING(50),
        allowNull: false,
        references: {
          model: 'approval_types',
          key: 'code',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'approval_type_code',
      },
      level: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      isOptional: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        field: 'is_optional',
      },
      approverId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        field: 'approver_id',
      },
    },
    {
      timestamps: true,
      underscored: true,
    },
  );

  /* -------------------------------------------------------------------------- */
  /*                                Associations                                */
  /* -------------------------------------------------------------------------- */

  DepartmentApprovalModel.associate = (models) => {
    DepartmentApprovalModel.belongsTo(models.userModel, {
      foreignKey: 'approverId',
      as: 'approver',
      targetKey: 'id',
    });

    DepartmentApprovalModel.belongsTo(models.departmentModel, {
      foreignKey: 'departmentId',
      as: 'department',
    });
  };

  return DepartmentApprovalModel;
};
