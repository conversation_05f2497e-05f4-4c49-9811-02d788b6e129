const {
  CANVASS_APPROVER_STATUS,
} = require('../../../domain/constants/canvassConstants');

module.exports = (sequelize, Sequelize) => {
  const CanvassApproverModel = sequelize.define(
    'canvass_approvers',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      canvassRequisitionId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'canvass_requisitions',
          key: 'id',
        },
        validate: {
          isInt: true,
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'canvass_requisition_id',
      },
      level: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'user_id',
      },
      altApproverId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'alt_approver_id',
      },
      status: {
        allowNull: false,
        type: Sequelize.STRING(255),
        defaultValue: CANVASS_APPROVER_STATUS.PENDING,
      },
      roleId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'roles',
          key: 'id',
        },
        validate: {
          isInt: true,
        },
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
        field: 'role_id',
      },
      isAdhoc: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        field: 'is_adhoc',
      },
      rejectReason: {
        allowNull: true,
        field: 'reject_reason',
        type: Sequelize.STRING(255),
      },
      overrideBy: {
        type: Sequelize.JSONB,
        field: 'override_by',
        defaultValue: null,
      },
    },
    {
      timestamps: true,
      underscored: true,
    },
  );

  CanvassApproverModel.associate = (models) => {
    CanvassApproverModel.belongsTo(models.userModel, {
      foreignKey: 'userId',
      as: 'approver',
    });

    CanvassApproverModel.belongsTo(models.userModel, {
      foreignKey: 'altApproverId',
      as: 'altApprover',
    });
    CanvassApproverModel.belongsTo(models.canvassRequisitionModel, {
      foreignKey: 'canvassRequisitionId',
      as: 'canvass',
    });

    CanvassApproverModel.belongsTo(models.roleModel, {
      foreignKey: 'roleId',
      as: 'role',
    });
  };

  // CanvassApproverModel.afterUpdate(async (approver, { transaction }) => {
  //   if (approver.changed('status')) {
  //     const canvass = await sequelize.model('canvass_requisitions').findOne({
  //       attributes: ['requisitionId', 'csLetter', 'csNumber'],
  //       where: {
  //         id: approver.canvassRequisitionId
  //       }
  //     });

  //     const requisition = await sequelize.model('requisitions').findOne({
  //       attributes: ['companyCode'],
  //       where: {
  //         id: canvass.requisitionId
  //       }
  //     });

  //     const user = await sequelize.model('users').findOne({
  //       attributes: ['id','firstName', 'lastName'],
  //       where: {
  //         id: approver.userId
  //       }
  //     });

  //     await sequelize.model('canvass_request_history').create({
  //       canvassId: approver.canvassRequisitionId,
  //       requisitionId: canvass.requisitionId,
  //       number: `CS-${requisition.companyCode}${canvass.csLetter}${canvass.csNumber}`,
  //       lastApprover: user.fullNameUser,
  //       status: canvass.status,
  //     }, {
  //       transaction
  //     });
  //   }
  // });

  return CanvassApproverModel;
};
