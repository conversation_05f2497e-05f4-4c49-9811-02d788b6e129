const {
  CANVASS_ITEM_STATUS,
} = require('../../../domain/constants/canvassConstants');

module.exports = (sequelize, Sequelize) => {
  const CanvassItemModel = sequelize.define(
    'canvass_items',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      requisitionId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'requisitions',
          key: 'id',
        },
        validate: {
          isInt: true,
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'requisition_id',
      },
      canvassRequisitionId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'canvass_requisitions',
          key: 'id',
        },
        validate: {
          isInt: true,
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'canvass_requisition_id',
      },
      requisitionItemListId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'requisition_item_lists',
          key: 'id',
        },
        validate: {
          isInt: true,
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'requisition_item_list_id',
      },
      status: {
        allowNull: false,
        type: Sequelize.STRING(255),
        defaultValue: CANVASS_ITEM_STATUS.NEW,
      },
      cancelledQty: {
        type: Sequelize.DECIMAL(13, 3),
        defaultValue: 0,
        allowNull: false,
        field: 'cancelled_qty',
      },
    },
    {
      timestamps: true,
      underscored: true,
    },
  );

  CanvassItemModel.associate = (models) => {
    CanvassItemModel.belongsTo(models.requisitionModel, {
      foreignKey: 'requisitionId',
      as: 'requisition',
    });

    CanvassItemModel.belongsTo(models.canvassRequisitionModel, {
      foreignKey: 'canvassRequisitionId',
      as: 'canvass',
    });

    CanvassItemModel.belongsTo(models.requisitionItemListModel, {
      foreignKey: 'requisitionItemListId',
      as: 'requisitionItem',
    });

    CanvassItemModel.belongsTo(models.canvassRequisitionModel, {
      foreignKey: 'canvassRequisitionId',
      as: 'canvassRequisition',
    });

    CanvassItemModel.hasMany(models.canvassItemSupplierModel, {
      foreignKey: 'canvassItemId',
      as: 'suppliers',
    });
  };

  return CanvassItemModel;
};
