module.exports = (sequelize, Sequelize) => {
  const NonOfmItemModel = sequelize.define(
    'non_ofm_items',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      itemName: {
        type: Sequelize.STRING(255),
        allowNull: false,
        unique: true,
        field: 'item_name',
      },
      itemType: {
        type: Sequelize.STRING(20),
        allowNull: false,
        field: 'item_type',
      },
      unit: {
        type: Sequelize.STRING(20),
        allowNull: false,
      },
      acctCd: {
        type: Sequelize.STRING(20),
        allowNull: false,
        field: 'acct_cd',
      },
      notes: {
        type: Sequelize.STRING(255),
        allowNull: true,
        field: 'notes',
      },
    },
    {
      timestamps: true,
      underscored: true,
    },
  );

  /* -------------------------------------------------------------------------- */
  /*                                Associations                                */
  /* -------------------------------------------------------------------------- */

  NonOfmItemModel.associate = (models) => {
    NonOfmItemModel.hasMany(models.requisitionItemListModel, {
      foreignKey: 'itemId',
      as: 'requisitionItemLists',
    });
  };

  return NonOfmItemModel;
};
