module.exports = (sequelize, Sequelize) => {
  const RSPaymentRequest = sequelize.define(
    'rs_payment_requests',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      prDraftNumber: {
        type: Sequelize.STRING(8),
        allowNull: true,
        field: 'draft_pr_number',
      },
      prNumber: {
        type: Sequelize.STRING(8),
        allowNull: true,
        field: 'pr_number',
      },
      prLetter: {
        type: Sequelize.STRING(2),
        allowNull: false,
        field: 'pr_letter',
      },
      isDraft: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        field: 'is_draft',
        defaultValue: false,
      },
      requisitionId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'requisition_id',
      },
      purchaseOrderId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        field: 'purchase_order_id',
      },
      deliveryInvoiceId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'delivery_invoice_id',
        references: {
          model: 'delivery_receipt_invoices',
          key: 'id',
        },
      },
      termsData: {
        type: Sequelize.JSONB,
        allowNull: true,
        field: 'terms_data',
      },
      payableDate: {
        type: Sequelize.DATE,
        allowNull: true,
        field: 'payable_date',
      },
      status: {
        type: Sequelize.STRING,
        allowNull: false,
        field: 'status',
      },
      totalAmount: {
        type: Sequelize.DECIMAL,
        allowNull: true,
        field: 'total_amount',
        get() {
          const rawValue = this.getDataValue('totalAmount');
          return parseFloat(rawValue || 0).toFixed(2);
        },
      },
      lastApproverId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'last_approver_id',
        references: {
          model: 'users',
          key: 'id',
        },
      },
      includesIncidentalFees: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        field: 'includes_incidental_fees',
      },
      // Cancellation tracking fields for force close functionality
      cancelledAt: {
        type: Sequelize.DATE,
        allowNull: true,
        field: 'cancelled_at',
      },
      cancelledBy: {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'cancelled_by',
        references: {
          model: 'users',
          key: 'id',
        },
      },
      cancellationReason: {
        type: Sequelize.STRING(100),
        allowNull: true,
        field: 'cancellation_reason',
      },
    },
    {
      timestamps: true,
      underscored: true,
    },
  );

  /* -------------------------------------------------------------------------- */
  /*                                Associations                                */
  /* -------------------------------------------------------------------------- */

  RSPaymentRequest.associate = (models) => {
    RSPaymentRequest.belongsTo(models.requisitionModel, {
      foreignKey: 'requisitionId',
      as: 'requisition',
    });
    RSPaymentRequest.belongsTo(models.purchaseOrderModel, {
      foreignKey: 'purchaseOrderId',
      as: 'purchaseOrder',
    });
    RSPaymentRequest.hasMany(models.rsPaymentRequestApproverModel, {
      foreignKey: 'paymentRequestId',
      as: 'paymentRequestApprovers',
    });
    RSPaymentRequest.hasOne(models.userModel, {
      foreignKey: 'id',
      sourceKey: 'lastApproverId',
      as: 'lastApprover',
    });
    RSPaymentRequest.hasMany(models.invoiceReportModel, {
      foreignKey: 'paymentRequestId',
      as: 'invoiceReports',
    });

    RSPaymentRequest.belongsTo(models.deliveryReceiptInvoiceModel, {
      foreignKey: 'deliveryInvoiceId',
      as: 'deliveryInvoice',
    });

    RSPaymentRequest.belongsTo(models.userModel, {
      foreignKey: 'cancelledBy',
      as: 'cancelledByUser',
    });
  };

  const createRequisitionPaymentHistoryRecord = async (
    paymentRequest,
    options,
  ) => {
    const { transaction } = options;

    try {
      const purchaseOrder = await sequelize.model('purchase_orders').findOne({
        attributes: ['id', 'supplierId', 'supplierType'],
        where: { id: paymentRequest.purchaseOrderId },
      });

      let supplier;
      if (purchaseOrder.supplierType === 'supplier') {
        supplier = await sequelize
          .model('suppliers')
          .findOne({
            attributes: ['name'],
            where: { id: purchaseOrder.supplierId },
          });
      } else if (purchaseOrder.supplierType === 'project') {
        supplier = await sequelize
          .model('projects')
          .findOne({
            attributes: ['name'],
            where: { id: purchaseOrder.supplierId },
          });
      } else {
        supplier = await sequelize
          .model('companies')
          .findOne({
            attributes: ['name'],
            where: { id: purchaseOrder.supplierId },
          });
      }

      await sequelize.model('requisition_payment_histories').create(
        {
          requisitionId: paymentRequest.requisitionId,
          prNumber: paymentRequest.prNumber || '-',
          amount: paymentRequest.totalAmount || 0,
          supplier: supplier.name,
          status: paymentRequest.status,
        },
        { transaction },
      );
    } catch (error) {
      console.error(
        'HOOK_ERROR - rsPaymentRequestModel - createRequisitionPaymentHistoryRecord: ',
        error.stack,
      );
    }
  };

  RSPaymentRequest.afterCreate(createRequisitionPaymentHistoryRecord);
  RSPaymentRequest.afterUpdate(createRequisitionPaymentHistoryRecord);

  return RSPaymentRequest;
};
