module.exports = (sequelize, Sequelize) => {
  const ItemModel = sequelize.define(
    'items',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      itemCd: {
        type: Sequelize.STRING(20),
        allowNull: false,
        unique: true,
        field: 'item_cd',
      },
      itmDes: {
        type: Sequelize.STRING(255),
        allowNull: false,
        field: 'itm_des',
      },
      unit: {
        type: Sequelize.STRING(20),
        allowNull: false,
        field: 'unit',
      },
      acctCd: {
        type: Sequelize.STRING(20),
        allowNull: false,
        field: 'acct_cd',
      },
      gfq: {
        type: Sequelize.DECIMAL(10, 3),
        allowNull: true,
        field: 'gfq',
        set(value) {
          const fixedValue = parseFloat(value || 0).toFixed(3);
          this.setDataValue('gfq', fixedValue);
        },
        get() {
          const rawValue = this.getDataValue('gfq');
          return parseFloat(rawValue || 0);
        },
      },
      tradeCode: {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'trade_code',
      },
      remainingGfq: {
        type: Sequelize.DECIMAL(10, 3),
        allowNull: true,
        field: 'remaining_gfq',
        set(value) {
          const fixedValue = parseFloat(value || 0).toFixed(3);
          this.setDataValue('remainingGfq', fixedValue);
        },
        get() {
          const rawValue = this.getDataValue('remainingGfq');
          return parseFloat(rawValue || 0);
        },
      },
      isSteelbars: {
        type: Sequelize.BOOLEAN,
        allowNull: true,
        field: 'is_steelbars',
      },
    },
    {
      timestamps: true,
      underscored: true,
    },
  );

  /* -------------------------------------------------------------------------- */
  /*                                Associations                                */
  /* -------------------------------------------------------------------------- */

  ItemModel.associate = (models) => {
    ItemModel.hasMany(models.requisitionItemListModel, {
      foreignKey: 'itemId',
      as: 'requisitionItemLists',
    });

    ItemModel.hasMany(models.ofmListItemModel, {
      foreignKey: 'ofm_item_id',
      as: 'listItems',
    });

    ItemModel.belongsTo(models.tradeModel, {
      foreignKey: 'trade_code',
      targetKey: 'tradeCode',
      as: 'trade',
    });

    ItemModel.hasOne(models.steelbarsModel, {
      foreignKey: 'ofm_acctcd',
      sourceKey: 'acctCd',
      as: 'steelbars',
    });
  };

  return ItemModel;
};
