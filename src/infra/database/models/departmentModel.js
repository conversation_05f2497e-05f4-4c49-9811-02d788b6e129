module.exports = (sequelize, Sequelize) => {
  const DepartmentModel = sequelize.define(
    'departments',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      code: {
        type: Sequelize.INTEGER,
        allowNull: false,
        unique: true,
      },
      name: {
        type: Sequelize.STRING(255),
        allowNull: false,
      },
    },
    {
      timestamps: true,
      underscored: true,
    },
  );

  /* -------------------------------------------------------------------------- */
  /*                                Associations                                */
  /* -------------------------------------------------------------------------- */

  DepartmentModel.associate = (models) => {
    DepartmentModel.hasMany(models.requisitionModel, {
      foreignKey: 'departmentId',
      as: 'requisitions',
    });

    DepartmentModel.hasMany(models.nonRequisitionModel, {
      foreignKey: 'departmentId',
      as: 'nonRS',
    });
  };

  return DepartmentModel;
};
