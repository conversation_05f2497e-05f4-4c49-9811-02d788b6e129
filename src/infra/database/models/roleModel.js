module.exports = (sequelize, Sequelize) => {
  const RoleModel = sequelize.define(
    'roles',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      name: {
        type: Sequelize.STRING(255),
        allowNull: false,
        unique: true,
      },
      isPermanent: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        field: 'is_permanent',
      },
    },
    {
      timestamps: true,
      underscored: true,
    },
  );

  /* -------------------------------------------------------------------------- */
  /*                                Associations                                */
  /* -------------------------------------------------------------------------- */

  RoleModel.associate = (models) => {
    RoleModel.hasMany(models.userModel, { foreignKey: 'roleId', as: 'users' });

    RoleModel.belongsToMany(models.permissionModel, {
      through: models.rolePermissionModel,
      foreignKey: 'roleId',
      otherKey: 'permissionId',
      as: 'permissions',
    });

    RoleModel.hasMany(models.notificationModel, {
      foreignKey: 'recipientRoleId',
      as: 'notifications',
    });
  };

  return RoleModel;
};
