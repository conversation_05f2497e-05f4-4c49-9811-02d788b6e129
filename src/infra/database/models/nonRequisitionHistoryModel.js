module.exports = (sequelize, Sequelize) => {
  const NonRequisitionHistoryModel = sequelize.define(
    'non_requisition_histories',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      nonRequisitionId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'non_requisitions',
          key: 'id',
        },
        validate: {
          isInt: true,
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
        field: 'non_requisition_id',
      },
      approverId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        validate: {
          isInt: true,
        },
        field: 'approver_id',
      },
      status: {
        allowNull: false,
        type: Sequelize.STRING(255),
      },
    },
    {
      timestamps: true,
      underscored: true,
    },
  );

  NonRequisitionHistoryModel.associate = (models) => {
    NonRequisitionHistoryModel.belongsTo(models.nonRequisitionModel, {
      foreignKey: 'nonRequisitionId',
      as: 'requisition',
    });

    NonRequisitionHistoryModel.belongsTo(models.userModel, {
      foreignKey: 'approverId',
      as: 'approver',
    });
  };

  return NonRequisitionHistoryModel;
};
