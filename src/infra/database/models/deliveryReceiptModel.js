const {
  DELIVERY_ITEM_STATUSES,
} = require('../../../domain/constants/deliveryReceiptItemConstants');
const { deliveryReceipt } = require('../../../domain/entities');
const {
  STATUSES,
} = require('../../../domain/constants/deliveryReceiptConstants');

module.exports = (sequelize, DataTypes) => {
  const DeliveryReceipt = sequelize.define(
    'delivery_receipts',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      requisitionId: {
        type: DataTypes.INTEGER,
        references: {
          model: 'requisitions',
          key: 'id',
        },
      },
      invoiceId: {
        type: DataTypes.INTEGER,
        references: {
          model: 'invoice_reports',
          key: 'id',
        },
        field: 'invoice_id',
      },
      companyCode: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      drNumber: {
        type: DataTypes.STRING(8),
        unique: true,
      },
      poId: {
        type: DataTypes.INTEGER,
        references: {
          model: 'purchase_orders',
          key: 'id',
        },
      },
      supplier: {
        type: DataTypes.STRING,
      },
      isDraft: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
      },
      draftDrNumber: {
        type: DataTypes.STRING(8),
        unique: true,
      },
      note: {
        type: DataTypes.STRING(60),
        allowNull: true,
      },
      invoiceNumber: {
        type: DataTypes.STRING,
        allowNull: true,
        field: 'invoice_number',
      },
      supplierDeliveryIssuedDate: {
        type: DataTypes.DATE,
        allowNull: true,
        field: 'supplier_delivery_issued_date',
      },
      issuedDate: {
        type: DataTypes.DATE,
        allowNull: true,
        field: 'issued_date',
      },
      latestDeliveryDate: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      latestDeliveryStatus: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      status: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      actualNumber: {
        type: DataTypes.VIRTUAL,
        get() {
          if (this.isDraft) {
            return `RR-TMP-${this.draftDrNumber}`;
          }
          return `RR-${this.drNumber}`;
        },
      },
      // Cancellation tracking fields for force close functionality
      cancelledAt: {
        type: DataTypes.DATE,
        allowNull: true,
        field: 'cancelled_at',
      },
      cancelledBy: {
        type: DataTypes.INTEGER,
        allowNull: true,
        field: 'cancelled_by',
        references: {
          model: 'users',
          key: 'id',
        },
      },
      cancellationReason: {
        type: DataTypes.STRING(100),
        allowNull: true,
        field: 'cancellation_reason',
      },
    },
    {
      timestamps: true,
      underscored: true,
    },
  );

  /**
   * Compute the latest delivery status of a delivery receipt.
   * @param {array<object>} items - an array of objects containing the properties
   *   `qtyOrdered`, `qtyDelivered`, and `qtyReturned`.
   * @returns {string|null} - the latest delivery status of the delivery receipt,
   *   which can be one of the values in `DELIVERY_ITEM_STATUSES`.
   */
  const computeLatestDeliveryStatus = (items) => {
    try {
      const itemStatuses = items.map((item) => {
        let status = '';
        if (item.qtyOrdered === item.qtyDelivered) {
          status = DELIVERY_ITEM_STATUSES.FULLY_DELIVERED;
          if (item.hasReturns) {
            status = DELIVERY_ITEM_STATUSES.FULLY_DELIVERED_WITH_RETURNS;
          }
        } else if (item.qtyDelivered < item.qtyOrdered) {
          status = DELIVERY_ITEM_STATUSES.PARTIALLY_DELIVERED;
          if (item.qtyReturned > 0) {
            status = DELIVERY_ITEM_STATUSES.PARTIALLY_DELIVERED_WITH_RETURNS;
          }
        }
        return status;
      });

      const partiallyDelivered = itemStatuses.some(
        (s) =>
          s === DELIVERY_ITEM_STATUSES.PARTIALLY_DELIVERED ||
          s === DELIVERY_ITEM_STATUSES.PARTIALLY_DELIVERED_WITH_RETURNS,
      );
      const fullyDelivered = itemStatuses.every(
        (s) =>
          s === DELIVERY_ITEM_STATUSES.FULLY_DELIVERED ||
          s === DELIVERY_ITEM_STATUSES.FULLY_DELIVERED_WITH_RETURNS,
      );

      let deliveryReceiptStatus;
      if (partiallyDelivered) {
        deliveryReceiptStatus = itemStatuses.some(
          (s) => s === DELIVERY_ITEM_STATUSES.PARTIALLY_DELIVERED_WITH_RETURNS,
        )
          ? DELIVERY_ITEM_STATUSES.PARTIALLY_DELIVERED_WITH_RETURNS
          : DELIVERY_ITEM_STATUSES.PARTIALLY_DELIVERED;
      } else if (fullyDelivered) {
        deliveryReceiptStatus = itemStatuses.some(
          (s) => s === DELIVERY_ITEM_STATUSES.FULLY_DELIVERED_WITH_RETURNS,
        )
          ? DELIVERY_ITEM_STATUSES.FULLY_DELIVERED_WITH_RETURNS
          : DELIVERY_ITEM_STATUSES.FULLY_DELIVERED;
      } else {
        deliveryReceiptStatus = null;
      }

      return deliveryReceiptStatus;
    } catch (error) {
      console.error(
        'HOOK_ERROR - deliveryReceiptModel - computeLatestDeliveryStatus:',
        error,
      );
    }
  };

  DeliveryReceipt.beforeCreate(async (instance) => {
    const items = instance.items;
    instance.latestDeliveryStatus = computeLatestDeliveryStatus(items);

    // Set status based on isDraft
    instance.status = instance.isDraft ? STATUSES.DRAFT : STATUSES.DELIVERED;
  });

  DeliveryReceipt.beforeUpdate(async (instance) => {
    const items = instance.items;
    if (items) {
      instance.latestDeliveryStatus = computeLatestDeliveryStatus(items);
    }

    // Set status based on isDraft
    if (instance.changed('isDraft') || !instance.status) {
      instance.status = instance.isDraft ? STATUSES.DRAFT : STATUSES.DELIVERED;
    }
  });

  DeliveryReceipt.associate = (models) => {
    DeliveryReceipt.belongsTo(models.requisitionModel, {
      foreignKey: 'requisitionId',
      as: 'requisition',
    });
    DeliveryReceipt.hasMany(models.deliveryReceiptItemModel, {
      foreignKey: 'drId',
      as: 'items',
    });
    DeliveryReceipt.hasMany(models.attachmentModel, {
      foreignKey: 'modelId',
      as: 'attachments',
    });
    DeliveryReceipt.belongsTo(models.purchaseOrderModel, {
      foreignKey: 'poId',
      as: 'purchaseOrder',
    });
    DeliveryReceipt.belongsTo(models.invoiceReportModel, {
      foreignKey: 'invoiceId',
      as: 'invoiceReport',
      onDelete: 'CASCADE',
    });
    DeliveryReceipt.hasMany(models.deliveryReceiptInvoiceModel, {
      foreignKey: 'deliveryReceiptId',
      as: 'deliveryReceiptInvoices',
    });

    DeliveryReceipt.belongsTo(models.userModel, {
      foreignKey: 'cancelledBy',
      as: 'cancelledByUser',
    });
    DeliveryReceipt.hasMany(models.deliveryReceiptItemModel, {
      foreignKey: 'drId',
      as: 'deliveryReceiptItems',
    });
  };

  return DeliveryReceipt;
};
