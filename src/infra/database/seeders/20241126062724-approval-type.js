'use strict';

const {
  APPROVAL_TYPES,
} = require('../../../domain/constants/approvalConstants');

module.exports = {
  async up(queryInterface, Sequelize) {
    const existingTypes = await queryInterface.sequelize.query(
      `SELECT code FROM approval_types`,
      {
        type: queryInterface.sequelize.QueryTypes.SELECT,
      },
    );

    const existingTypeCodes = existingTypes.map((type) => type.code);

    const dateNow = new Date();
    const approvalTypesToCreate = Object.values(APPROVAL_TYPES)
      .filter((type) => !existingTypeCodes.includes(type.code))
      .map((type) => ({
        name: type.name,
        code: type.code,
        created_at: dateNow,
        updated_at: dateNow,
      }));

    if (approvalTypesToCreate.length > 0) {
      await queryInterface.bulkInsert('approval_types', approvalTypesToCreate);
      console.log(
        'Approval types created:',
        approvalTypesToCreate.map((type) => type.code).join(', '),
      );
    } else {
      console.log('All approval types already exist');
    }
  },

  async down(queryInterface, Sequelize) {
    const typeCodes = Object.values(APPROVAL_TYPES).map((type) => type.code);
    await queryInterface.bulkDelete('approval_types', {
      code: {
        [Sequelize.Op.in]: typeCodes,
      },
    });
  },
};
