'use strict';
const { permission } = require('../../../domain/constants');

module.exports = {
  async up(queryInterface, Sequelize) {
    const dateNow = new Date();
    const permissions = Object.values(permission.PERMISSIONS).map(
      (permission) => ({
        module: permission.module,
        action: permission.action,
        created_at: dateNow,
        updated_at: dateNow,
      }),
    );

    /* Check existing permissions */
    const existingPermissions = await queryInterface.sequelize.query(
      `SELECT module, action FROM permissions`,
      { type: queryInterface.sequelize.QueryTypes.SELECT },
    );

    /* Filter out existing permissions */
    const newPermissions = permissions.filter(
      (permission) =>
        !existingPermissions.some(
          (existing) =>
            existing.module === permission.module &&
            existing.action === permission.action,
        ),
    );

    console.log('Permissions to add', newPermissions);

    if (newPermissions.length > 0) {
      await queryInterface.bulkInsert('permissions', newPermissions, {});
    } else {
      console.log('No new permissions to insert');
    }
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('permissions', null, {});
  },
};
