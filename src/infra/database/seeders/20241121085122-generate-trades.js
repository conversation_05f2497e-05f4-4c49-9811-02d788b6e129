'use strict';

const {
  MAJOR_TRADES,
  SUB_TRADES,
  TRADE_CATEGORIES,
} = require('../../../domain/constants/tradeConstants');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const trades = [
      ...Object.values(MAJOR_TRADES).map((trade) => ({
        trade_name: trade.name,
        trade_code: trade.code,
        category: TRADE_CATEGORIES.MAJOR,
        created_at: new Date(),
        updated_at: new Date(),
      })),
      ...Object.values(SUB_TRADES).map((trade) => ({
        trade_name: trade.name,
        trade_code: trade.code,
        category: TRADE_CATEGORIES.SUB,
        created_at: new Date(),
        updated_at: new Date(),
      })),
    ];

    for (const trade of trades) {
      await queryInterface.sequelize.query(
        `
        INSERT INTO trades (trade_name, trade_code, category, created_at, updated_at)
        VALUES (:trade_name, :trade_code, :category, :created_at, :updated_at)
        ON CONFLICT (trade_code)
        DO UPDATE SET
          trade_name = EXCLUDED.trade_name,
          category = EXCLUDED.category,
          updated_at = EXCLUDED.updated_at;
        `,
        {
          replacements: trade,
        },
      );
    }

    console.log('Trades upserted:');
    trades.forEach((trade) => {
      console.log(`- ${trade.trade_code} ${trade.trade_name}`);
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('trades', null, {});
  },
};
