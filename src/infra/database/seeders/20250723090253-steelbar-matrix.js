'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const steelbarMatrixData = [
      {
        grade: 40,
        dimensions: [
          { diameter: 10, weight: 0.617, length: [6.0, 7.5, 9.0, 10.5, 12.0] },
          { diameter: 12, weight: 0.888, length: [6.0, 7.5, 9.0, 10.5, 12.0] },
          { diameter: 16, weight: 1.578, length: [6.0, 7.5, 9.0, 10.5, 12.0] },
          { diameter: 20, weight: 2.466, length: [6.0, 7.5, 9.0, 10.5, 12.0] },
          { diameter: 25, weight: 3.853, length: [6.0, 7.5, 9.0, 10.5, 12.0] },
          { diameter: 28, weight: 4.834, length: [6.0, 7.5, 9.0, 10.5, 12.0] },
          { diameter: 32, weight: 6.313, length: [6.0, 7.5, 9.0, 10.5, 12.0] },
          { diameter: 36, weight: 7.99, length: [6.0, 7.5, 9.0, 10.5, 12.0] },
        ],
      },
      {
        grade: 60,
        dimensions: [
          { diameter: 10, weight: 0.617, length: [6.0, 7.5, 9.0, 10.5, 12.0] },
          { diameter: 12, weight: 0.888, length: [6.0, 7.5, 9.0, 10.5, 12.0] },
          { diameter: 16, weight: 1.578, length: [6.0, 7.5, 9.0, 10.5, 12.0] },
          { diameter: 20, weight: 2.466, length: [6.0, 7.5, 9.0, 10.5, 12.0] },
          { diameter: 25, weight: 3.853, length: [6.0, 7.5, 9.0, 10.5, 12.0] },
          { diameter: 28, weight: 4.834, length: [6.0, 7.5, 9.0, 10.5, 12.0] },
          { diameter: 32, weight: 6.313, length: [6.0, 7.5, 9.0, 10.5, 12.0] },
          { diameter: 36, weight: 7.99, length: [6.0, 7.5, 9.0, 10.5, 12.0] },
          { diameter: 40, weight: 9.865, length: [6.0, 7.5, 9.0, 10.5, 12.0] },
          { diameter: 50, weight: 15.413, length: [6.0, 7.5, 9.0, 10.5, 12.0] },
        ],
      },
      {
        grade: 75,
        dimensions: [
          { diameter: 10, weight: 0.617, length: [6.0, 7.5, 9.0, 10.5, 12.0] },
          { diameter: 12, weight: 0.888, length: [6.0, 7.5, 9.0, 10.5, 12.0] },
          { diameter: 16, weight: 1.578, length: [6.0, 7.5, 9.0, 10.5, 12.0] },
          { diameter: 20, weight: 2.466, length: [6.0, 7.5, 9.0, 10.5, 12.0] },
          { diameter: 25, weight: 3.853, length: [6.0, 7.5, 9.0, 10.5, 12.0] },
          { diameter: 28, weight: 4.834, length: [6.0, 7.5, 9.0, 10.5, 12.0] },
          { diameter: 32, weight: 6.313, length: [6.0, 7.5, 9.0, 10.5, 12.0] },
          { diameter: 36, weight: 7.99, length: [6.0, 7.5, 9.0, 10.5, 12.0] },
          { diameter: 40, weight: 9.865, length: [6.0, 7.5, 9.0, 10.5, 12.0] },
          { diameter: 50, weight: 15.413, length: [6.0, 7.5, 9.0, 10.5, 12.0] },
        ],
      },
    ];

    const steelbarMatrix = [];

    steelbarMatrixData.forEach(({ grade, dimensions }) => {
      dimensions.forEach(({ diameter, weight, length }) => {
        length.forEach((l) => {
          steelbarMatrix.push({
            grade,
            diameter,
            weight,
            length: l,
          });
        });
      });
    });

    await queryInterface.bulkInsert('steelbar_matrix', steelbarMatrix);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.bulkDelete('steelbar_matrix', null, {});
  },
};
