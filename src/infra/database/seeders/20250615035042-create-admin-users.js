'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const { users } = require('./data/admin_user');

    console.log(`Seeding ${users.length} users`);

    const existingUsers = await queryInterface.select(null, 'users', {
      where: {
        username: {
          [Sequelize.Op.in]: users.map((user) => user.username),
        },
      },
    });

    const newUsers = users.filter(
      (user) =>
        !existingUsers.some(
          (existingUser) => existingUser.username === user.username,
        ),
    );

    if (newUsers.length === 0) {
      console.log('All users already exist, nothing to seed!');
      return;
    }

    console.log(`Seeding ${newUsers.length} new users`);

    await queryInterface.bulkInsert('users', newUsers);
  },

  async down(queryInterface, Sequelize) {
    // do nothing
  },
};
