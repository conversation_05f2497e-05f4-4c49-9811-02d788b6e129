'use strict';
require('dotenv').config();
const bcrypt = require('bcryptjs');
const {
  USER_STATUS,
  USER_TYPES,
} = require('../../../domain/constants/userConstants');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    /**
     * Add seed commands here.
     *
     * Example:
     * await queryInterface.bulkInsert('People', [{
     *   name: '<PERSON>',
     *   isBetaMember: false
     * }], {});
     */

    /**
     * Add admin user
     */
    const adminUsername = process.env.ROOT_USER_NAME;
    const adminEmail = process.env.ROOT_USER_EMAIL;
    const adminPassword = bcrypt.hashSync(process.env.ROOT_USER_PASSWORD);

    /* Get the role ID for Root User */
    const [rootUserRole] = await queryInterface.sequelize.query(
      `SELECT id FROM roles WHERE name = '${USER_TYPES.ROOT_USER}'`,
    );

    if (!rootUserRole || rootUserRole.length === 0) {
      console.log('Root User role does not exist. Please seed roles first.');
      return;
    }

    const rootUserRoleId = rootUserRole[0].id;

    const [results] = await queryInterface.sequelize.query(
      `SELECT * FROM users WHERE email = '${adminEmail}'`,
    );

    if (results?.length === 0 && adminEmail && adminPassword) {
      console.log('Adding Root User');
      await queryInterface.bulkInsert(
        'users',
        [
          {
            username: adminUsername,
            email: adminEmail,
            password: adminPassword,
            first_name: 'Root',
            last_name: 'User',
            role_id: rootUserRoleId,
            status: USER_STATUS.ACTIVE,
            is_password_temporary: false,
            created_at: new Date(),
            updated_at: new Date(),
          },
        ],
        {},
      );
      console.log('Root User successfully added');
    } else {
      console.log('Root User already exist');
    }
  },

  async down(queryInterface, Sequelize) {
    /**
     * Add commands to revert seed here.
     *
     * Example:
     * await queryInterface.bulkDelete('People', null, {});
     */
    const adminEmail = process.env.ROOT_USER_NAME;
    await queryInterface.bulkDelete('users', { username: adminEmail }, {});
    console.log('Root User successfully deleted');
  },
};
