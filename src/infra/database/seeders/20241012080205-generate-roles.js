'use strict';
const { USER_TYPES } = require('../../../domain/constants/userConstants');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    /**
     * Add seed commands here.
     *
     * Example:
     * await queryInterface.bulkInsert('People', [{
     *   name: '<PERSON>',
     *   isBetaMember: false
     * }], {});
     */

    const userTypeList = Object.values(USER_TYPES);

    const roles = userTypeList.map((role) => ({
      name: role,
      is_permanent: true,
      created_at: new Date(),
      updated_at: new Date(),
    }));

    /* Check if roles already exist */
    const existingRoles = await queryInterface.sequelize.query(
      `SELECT name FROM roles WHERE name IN (:names)`,
      {
        replacements: { names: userTypeList },
        type: queryInterface.sequelize.QueryTypes.SELECT,
      },
    );

    const existingRoleNames = existingRoles.map((role) => role.name);

    /* Filter out roles that already exist */
    const newRoles = roles.filter(
      (role) => !existingRoleNames.includes(role.name),
    );

    console.log('Roles to add', newRoles);

    if (newRoles.length > 0) {
      await queryInterface.bulkInsert('roles', newRoles, {});
    }
  },

  async down(queryInterface, Sequelize) {
    /**
     * Add commands to revert seed here.
     *
     * Example:
     * await queryInterface.bulkDelete('People', null, {});
     */
    await queryInterface.bulkDelete('roles', null, {});
  },
};
