'use strict';

const REQUISITION_STATUS = Object.freeze({
  DRAFT: 'rs_draft',
  SUBMITTED: 'for_rs_approval',
  APPROVED: 'approved',
  ASSIGNED: 'assigned',
  ASSIGNING: 'assigning',
  REJECTED: 'rs_rejected',
  CLOSED: 'closed',
  RS_IN_PROGRESS: 'rs_in_progress',
});

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const oldToNewStatusMap = {
      draft: REQUISITION_STATUS.DRAFT,
      rejected: REQUISITION_STATUS.REJECTED,
      submitted: REQUISITION_STATUS.SUBMITTED,
      approved: REQUISITION_STATUS.CLOSED,
    };

    const tablesToUpdate = ['requisitions'];

    for (const tableName of tablesToUpdate) {
      const [results] = await queryInterface.sequelize.query(
        `SELECT DISTINCT status FROM ${tableName};`,
      );
      const existingOldStatuses = results.map((row) => row.status);

      for (const oldStatus of existingOldStatuses) {
        const newStatus =
          oldToNewStatusMap[oldStatus] || REQUISITION_STATUS.RS_IN_PROGRESS;

        await queryInterface.bulkUpdate(
          tableName,
          { status: newStatus },
          { status: oldStatus },
        );
      }
    }
  },

  down: async (queryInterface, Sequelize) => {
    const newToOldStatusMap = {
      [REQUISITION_STATUS.DRAFT]: 'draft',
      [REQUISITION_STATUS.REJECTED]: 'rejected',
      [REQUISITION_STATUS.SUBMITTED]: 'submitted',
      [REQUISITION_STATUS.CLOSED]: 'approved',
      [REQUISITION_STATUS.RS_IN_PROGRESS]: 'submitted',
    };

    const tablesToUpdate = ['requisitions'];

    for (const tableName of tablesToUpdate) {
      const [results] = await queryInterface.sequelize.query(
        `SELECT DISTINCT status FROM ${tableName};`,
      );
      const existingNewStatuses = results.map((row) => row.status);

      for (const newStatus of existingNewStatuses) {
        const oldStatus = newToOldStatusMap[newStatus] || 'submitted';

        await queryInterface.bulkUpdate(
          tableName,
          { status: oldStatus },
          { status: newStatus },
        );
      }
    }
  },
};
