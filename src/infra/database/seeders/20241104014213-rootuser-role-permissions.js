'use strict';
const { USER_TYPES } = require('../../../domain/constants/userConstants');
const { MODULES } = require('../../../domain/constants/permissionConstants');

module.exports = {
  async up(queryInterface, Sequelize) {
    const roles = await queryInterface.sequelize.query(
      `SELECT id, name FROM roles WHERE name = :name`,
      {
        replacements: { name: USER_TYPES.ROOT_USER },
        type: queryInterface.sequelize.QueryTypes.SELECT,
      },
    );

    if (!roles.length) {
      console.log('Root User role not found');
      return;
    }

    /* Get all user management permissions and roles view permission */
    const rootUserRole = roles[0];
    const permissions = await queryInterface.sequelize.query(
      `SELECT id, module, action 
       FROM permissions 
       WHERE module = :userModule 
       OR (module = :roleModule AND action = 'get')
       OR (module = :departmentModule AND action = 'get')`,
      {
        replacements: {
          userModule: MODULES.USERS,
          roleModule: MODULES.ROLES,
          departmentModule: MODULES.DEPARTMENTS,
        },
        type: queryInterface.sequelize.QueryTypes.SELECT,
      },
    );

    /* Create role permissions array for Root User */
    const dateNow = new Date();
    const rolePermissions = permissions.map((permission) => ({
      role_id: rootUserRole.id,
      permission_id: permission.id,
      created_at: dateNow,
      updated_at: dateNow,
    }));

    /* Check existing role permissions */
    const existingRolePermissions = await queryInterface.sequelize.query(
      `SELECT role_id, permission_id 
       FROM role_permissions 
       WHERE role_id = :roleId`,
      {
        replacements: { roleId: rootUserRole.id },
        type: queryInterface.sequelize.QueryTypes.SELECT,
      },
    );

    /* Filter out existing role permissions */
    const newRolePermissions = rolePermissions.filter(
      (rp) =>
        !existingRolePermissions.some(
          (existing) => existing.permission_id === rp.permission_id,
        ),
    );

    if (newRolePermissions.length > 0) {
      console.log('Root User - permissions to add', newRolePermissions);

      await queryInterface.bulkInsert(
        'role_permissions',
        newRolePermissions,
        {},
      );
      console.log(
        `Added ${newRolePermissions.length} permissions to Root User`,
      );
    } else {
      console.log('No new permissions to add');
    }

    /* Log assigned permissions for verification */
    console.log('Root User permissions:');
    permissions.forEach((p) => {
      console.log(`- ${p.module} ${p.action}`);
    });
  },

  async down(queryInterface, Sequelize) {
    const role = await queryInterface.sequelize.query(
      `SELECT id FROM roles WHERE name = :name`,
      {
        replacements: { name: USER_TYPES.ROOT_USER },
        type: queryInterface.sequelize.QueryTypes.SELECT,
      },
    );

    if (role.length) {
      await queryInterface.bulkDelete(
        'role_permissions',
        {
          role_id: role[0].id,
        },
        {},
      );
      console.log('Removed all Root User permissions');
    }
  },
};
