'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const warranties = await queryInterface.rawSelect(
      'warranties',
      {
        where: {},
        limit: 1,
      },
      ['id'],
    );

    if (!warranties) {
      await queryInterface.bulkInsert('warranties', [
        {
          name: '7 Days',
          type: 'purchase_order',
        },
        {
          name: '30 Days',
          type: 'purchase_order',
        },
        {
          name: '1 Month',
          type: 'purchase_order',
        },
        {
          name: '3 Months',
          type: 'purchase_order',
        },
        {
          name: '6 Months',
          type: 'purchase_order',
        },
        {
          name: '1 Year',
          type: 'purchase_order',
        },
      ]);
    }
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('warranties', null, {});
  },
};
