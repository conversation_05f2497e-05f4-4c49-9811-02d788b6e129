'use strict';
const { USER_TYPES } = require('../../../domain/constants/userConstants');

module.exports = {
  async up(queryInterface, Sequelize) {
    const roles = await queryInterface.sequelize.query(
      `SELECT id, name FROM roles WHERE name = :name`,
      {
        replacements: { name: USER_TYPES.ENGINEERS },
        type: queryInterface.sequelize.QueryTypes.SELECT,
      },
    );

    if (!roles.length) {
      console.log('Engineer role not found');
      return;
    }

    /**
     * Engineer
     * - OFM Items Module
     * - OFM Lists Module
     * - View Non-OFM
     * - Dashboard/RS except approval & history
     * - View Canvass
     * - View Orders
     * - Delivery Module
     * - View Payments
     * - Non-RS Payments
     */
    const engineerRole = roles[0];
    const permissions = await queryInterface.sequelize.query(
      `SELECT id, module, action FROM permissions 
       WHERE module IN (
         'ofm_items', 'ofm_lists', 'delivery', 'non_rs_payments'
       )
       OR (module = 'users' AND action = 'get')
       OR (module = 'departments' AND action = 'get')
       OR (module = 'projects' AND action = 'get')
       OR (module = 'companies' AND action = 'get')
       OR (module = 'non_ofm_items' AND action IN ('view', 'get'))
       OR (module = 'dashboard' AND action != 'approval')
       OR (module = 'canvass' AND action IN ('view', 'get'))
       OR (module = 'orders' AND action IN ('view', 'get'))
       OR (module = 'payments' AND action IN ('view', 'get'))`,
      {
        type: queryInterface.sequelize.QueryTypes.SELECT,
      },
    );

    const dateNow = new Date();
    const rolePermissions = permissions.map((permission) => ({
      role_id: engineerRole.id,
      permission_id: permission.id,
      created_at: dateNow,
      updated_at: dateNow,
    }));

    /* Check existing role permissions */
    const existingRolePermissions = await queryInterface.sequelize.query(
      `SELECT role_id, permission_id 
       FROM role_permissions 
       WHERE role_id = :roleId`,
      {
        replacements: { roleId: engineerRole.id },
        type: queryInterface.sequelize.QueryTypes.SELECT,
      },
    );

    /* Filter out existing role permissions */
    const newRolePermissions = rolePermissions.filter(
      (rp) =>
        !existingRolePermissions.some(
          (existing) => existing.permission_id === rp.permission_id,
        ),
    );

    if (newRolePermissions.length > 0) {
      console.log('Engineer - permissions to add', newRolePermissions);
      await queryInterface.bulkInsert(
        'role_permissions',
        newRolePermissions,
        {},
      );
      console.log(
        `Added ${newRolePermissions.length} permissions to Engineer role`,
      );
    } else {
      console.log('No new permissions to add');
    }

    /* Log assigned permissions for verification */
    console.log('Engineer permissions:');
    permissions.forEach((p) => {
      console.log(`- ${p.module} ${p.action}`);
    });
  },

  async down(queryInterface, Sequelize) {
    const role = await queryInterface.sequelize.query(
      `SELECT id FROM roles WHERE name = :name`,
      {
        replacements: { name: USER_TYPES.ENGINEERS },
        type: queryInterface.sequelize.QueryTypes.SELECT,
      },
    );

    if (role.length) {
      await queryInterface.bulkDelete(
        'role_permissions',
        {
          role_id: role[0].id,
        },
        {},
      );
      console.log('Removed all Engineer role permissions');
    }
  },
};
