require('dotenv').config();
const { requestContext } = require('@fastify/request-context');

const customLogger = (msg, queryOptions) => {
  const logger = requestContext.get('log');
  const { bind } = queryOptions;
  if (logger) {
    logger.info(`${msg}`);
    if (bind) {
      const bindValueLog = bind.reduce((acc, b, index) => {
        acc[`$${index + 1}`] = b;
        return acc;
      }, {});
      logger.info(JSON.stringify(bindValueLog));
    }
  } else {
    console.log(`${msg}`);
  }
};

module.exports = {
  development: {
    host: process.env.POSTGRES_HOST,
    dialect: process.env.DIALECT,
    port: process.env.POSTGRES_PORT,
    username: process.env.POSTGRES_USER,
    password: process.env.POSTGRES_PASSWORD,
    database: process.env.POSTGRES_DB,
    dialectOptions: {
      useUTC: true,
    },
    define: {
      timestamps: false,
    },
    pool: {
      max: Number(process.env.POOL_MAX) || 5,
      min: Number(process.env.POOL_MIN) || 0,
      acquire: Number(process.env.POOL_ACQUIRE) || 30000,
      idle: Number(process.env.POOL_IDLE) || 10000,
      evict: Number(process.env.POOL_EVICTION) || 20000,
    },
    logging: customLogger,
  },
  production: {
    host: process.env.POSTGRES_HOST,
    dialect: process.env.DIALECT,
    port: process.env.POSTGRES_PORT,
    username: process.env.POSTGRES_USER,
    password: process.env.POSTGRES_PASSWORD,
    database: process.env.POSTGRES_DB,
    dialectOptions: {
      useUTC: true,
      ...(process.env.POSTGRES_SSL_ENABLED === 'true' && {
        ssl: {
          require: process.env.POSTGRES_SSL_REQUIRE === 'true',
          rejectUnauthorized:
            process.env.POSTGRES_SSL_REJECT_UNAUTHORIZED === 'true',
        },
      }),
    },
    define: {
      timestamps: false,
    },
    pool: {
      max: Number(process.env.POOL_MAX) || 3,
      min: Number(process.env.POOL_MIN) || 1,
      acquire: Number(process.env.POOL_ACQUIRE) || 30000,
      idle: Number(process.env.POOL_IDLE) || 10000,
    },
    logging: false, // Disable DB query logging in production
  },
};
