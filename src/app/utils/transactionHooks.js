const fp = require('fastify-plugin');

async function transactionHooks(fastify, opts) {
  fastify.decorate('useTransaction', function (options = {}) {
    const { autoCommit = true } = options;

    this.addHook('onRequest', async (request, reply) => {
      const transaction = await fastify.diScope
        .resolve('db')
        .sequelize.transaction();
      request.transaction = transaction;
      request.transactionFinished = false;
      request.autoCommitTransaction = autoCommit;

      // Add methods to manually control transaction
      request.commitTransaction = async () => {
        if (!request.transaction || request.transactionFinished) return;
        await request.transaction.commit();
        request.transactionFinished = true;
      };

      request.rollbackTransaction = async () => {
        if (!request.transaction || request.transactionFinished) return;
        await request.transaction.rollback();
        request.transactionFinished = true;
      };
    });

    this.addHook('onResponse', async (request, reply) => {
      if (!request.transaction || request.transactionFinished) return;
      if (!request.autoCommitTransaction) return; // Skip auto-commit if disabled

      try {
        if (reply.statusCode < 400) {
          await request.transaction.commit();
        } else {
          await request.transaction.rollback();
        }
        request.transactionFinished = true;
      } catch (error) {
        request.log.error(`Transaction error: ${error.message}`);
        if (
          !request.transactionFinished &&
          !error.message.includes('finished with state')
        ) {
          try {
            await request.transaction.rollback();
          } catch (rollbackError) {
            request.log.error(`Rollback error: ${rollbackError.message}`);
          }
        }
        request.transactionFinished = true;
      }
    });

    this.addHook('onError', async (request, reply, error) => {
      if (!request.transaction || request.transactionFinished) return;

      try {
        await request.transaction.rollback();
        request.transactionFinished = true;
      } catch (rollbackError) {
        request.log.error(`Rollback error: ${rollbackError.message}`);
      }
    });
  });
}

module.exports = fp(transactionHooks);
