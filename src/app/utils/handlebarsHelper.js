const Handlebars = require('handlebars');
const {
  formatCurrency,
  amountInWordsPHP,
  convertDateToDDMMMYYYY,
} = require('./index');

function registerHelpers() {
  // Increment helper
  Handlebars.registerHelper('inc', function (value) {
    return parseInt(value) + 1;
  });

  Handlebars.registerHelper('formatDate', function (date) {
    return new Date(date).toLocaleDateString();
  });

  Handlebars.registerHelper('eq', function (v1, v2) {
    return v1 === v2;
  });

  Handlebars.registerHelper('json', function (context) {
    return JSON.stringify(context);
  });

  Handlebars.registerHelper('length', function (array) {
    return array ? array.length : 0;
  });

  Handlebars.registerHelper('times', function (n, block) {
    let accum = '';
    for (let i = 0; i < n; ++i) {
      accum += block.fn(i);
    }
    return accum;
  });

  Handlebars.registerHelper('add', function (a, b) {
    return a + b;
  });

  Handlebars.registerHelper('subtract', function (a, b) {
    return a - b;
  });

  Handlebars.registerHelper('lt', function (a, b) {
    return a < b;
  });

  Handlebars.registerHelper('gt', function (a, b) {
    return a > b;
  });

  Handlebars.registerHelper(
    'formatCurrency',
    function (amount, decimal = 2, style = 'currency') {
      return formatCurrency(amount, decimal, style);
    },
  );

  Handlebars.registerHelper('amountInWords', function (amount) {
    return amountInWordsPHP(amount);
  });

  Handlebars.registerHelper('convertDateToDDMMMYYYY', function (date) {
    return convertDateToDDMMMYYYY(date);
  });
}

module.exports = registerHelpers;
