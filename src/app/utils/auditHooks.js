class AuditHooks {
  constructor({ db, clientErrors }) {
    this.db = db;
    this.clientErrors = clientErrors;
  }

  addHooks(model, moduleName) {
    const createAuditLog = async (
      instance,
      actionType,
      description,
      metadata,
      options,
    ) => {
      try {
        const userId = options?.userId ?? null;
        const payload = options?.payload ?? {};

        await this.db.auditLogModel.create(
          {
            actionType,
            module: moduleName,
            description,
            metadata: {
              userId,
              payload,
              ...metadata,
            },
          },
          {
            transaction: options?.transaction,
          },
        );
      } catch (error) {
        console.error(`Error creating audit log for ${moduleName}:`, error);
        // Don't throw error to prevent disrupting the main operation
      }
    };

    model.addHook('afterCreate', async (instance, options) => {
      await createAuditLog(
        instance,
        'insert',
        `Created ${moduleName} with ID ${instance.id}`,
        {
          id: instance.id,
          newData: instance.get({ plain: true }),
        },
        options,
      );
    });

    model.addHook('afterUpdate', async (instance, options) => {
      const changes = instance.changed()
        ? instance.changed().reduce((acc, field) => {
            acc[field] = {
              old: instance._previousDataValues[field],
              new: instance.get(field),
            };
            return acc;
          }, {})
        : {};

      await createAuditLog(
        instance,
        'update',
        `Updated ${moduleName} with ID ${instance.id}`,
        {
          id: instance.id,
          changes,
        },
        options,
      );
    });

    model.addHook('afterDestroy', async (instance, options) => {
      await createAuditLog(
        instance,
        'delete',
        `Deleted ${moduleName} with ID ${instance.id}`,
        {
          id: instance.id,
          deletedData: instance.get({ plain: true }),
        },
        options,
      );
    });
  }
}

module.exports = AuditHooks;
