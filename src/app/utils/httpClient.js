const clientError = require('../errors/clientErrors');

class HTTPClient {
  constructor ({ endpoint }) {
    this.endpoint = endpoint;
    this.HTTP_METHODS = {
      GET: 'GET',
      POST: 'POST',
      PUT: 'PUT',
      DELETE: 'DELETE',
    };
  }

  async request({ method, path = null, body = null, headers = null }) {
    const defaultHeaders = {
      'Content-Type': 'application/json',
      'Cache-Control': 'no-cache',
      Accept: 'application/json',
    };

    const options = {
      method,
      headers: { ...defaultHeaders, ...headers },
      body: body ? JSON.stringify(body) : null,
    };

    console.log('=== REQUEST OPTIONS ===');
    console.log({ options });

    // Create an AbortController for timeout handling
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 60000); // 60 second timeout

    try {
      const response = await fetch(`${this.endpoint}${path}`, {
        ...options,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);
      return await this.processResponse(response);
    } catch (error) {
      clearTimeout(timeoutId);
      if (error.name === 'AbortError') {
        throw clientError.BAD_REQUEST({
          message: 'Request timeout',
          description: 'The external API request timed out after 60 seconds',
          errorCode: 'REQUEST_TIMEOUT',
        });
      }
      throw error;
    }
  }

  async processResponse(response) {
    console.log(response);

    if (!response.ok) {
      throw clientError.BAD_REQUEST({
        message: 'External API request failed',
        description:
          response.statusText || 'Failed to communicate with external service',
        errorCode: 'EXTERNAL_API_ERROR',
      });
    }

    return await response.json();
  }

  async get({ path, headers = null }) {
    console.log('=== HTTPClient GET DEBUG ===');
    console.log('Path:', path);
    console.log('Endpoint:', this.endpoint);
    console.log('Is Mock:', this.isMockEnvironment());

    console.log('=== MAKING REAL GET REQUEST TO MOCKTAIL ===');
    const response = await this.request({
      method: this.HTTP_METHODS.GET,
      path,
      headers,
    });

    // Handle mock API response format - extract data array if it exists
    if (
      this.isMockEnvironment() &&
      response.data &&
      Array.isArray(response.data)
    ) {
      return response.data;
    }

    return response;
  }

  async post({ path, body = null, headers = null }) {
    // Handle mock authentication for development/testing
    console.log('=== HTTPClient POST DEBUG ===');
    console.log('Path:', path);
    console.log('Endpoint:', this.endpoint);
    console.log('Is Mock:', this.isMockEnvironment());

    if (path === '/token/' && this.isMockEnvironment()) {
      console.log('=== RETURNING MOCK TOKEN ===');
      return {
        access: 'mock-token-12345',
        refresh: 'mock-refresh-token-12345',
        expires_in: 3600,
      };
    }

    console.log('=== MAKING REAL REQUEST ===');
    return await this.request({
      method: this.HTTP_METHODS.POST,
      path,
      body,
      headers,
    });
  }

  isMockEnvironment() {
    const isMock =
      this.endpoint?.includes('mocktail') ||
      this.endpoint?.includes('beeceptor') ||
      process.env.NODE_ENV === 'local';
    console.log('=== MOCK ENV CHECK ===');
    console.log('Endpoint:', this.endpoint);
    console.log('NODE_ENV:', process.env.NODE_ENV);
    console.log('Is Mock:', isMock);
    return isMock;
  }

  async put({ path, body = null, headers = null }) {
    return await this.request({
      method: this.HTTP_METHODS.PUT,
      path,
      body,
      headers,
    });
  }

  async delete({ path, headers = null }) {
    return await this.request({
      method: this.HTTP_METHODS.DELETE,
      path,
      headers,
    });
  }
}

module.exports = HTTPClient;
