const { TERMS } = require('./../../domain/constants').purchaseOrder;
//mock data
const percent = (totalAmount, percent) => {
  return totalAmount * (1 - percent / 100);
};
let remainingBalance;
let retentionBalance;
let issueDate;
let payableDate;

const terms = (payload) => {
  const {
    terms,
    deposit = 0,
    totalAmount,
    issuedInvoiceDate,
    employeeId,
  } = payload;

  switch (terms) {
    case TERMS.COD:
      //removing deposit
      if (payload.deposit) {
        totalAmount = percent(totalAmount, deposit);
      }
      return { totalAmount };

    case TERMS.DP_10:
      //removing deposit
      if (payload.deposit) {
        return (remainingBalance = percent(totalAmount, deposit + 10));
      }
      //adding the tax/extra charges
      remainingBalance = percent(totalAmount, 10);
      return { remainingBalance };

    case TERMS.DP_20:
      //removing deposit
      if (payload.deposit) {
        return (remainingBalance = percent(totalAmount, deposit + 20));
      }
      remainingBalance = percent(totalAmount, 20);
      return { remainingBalance };

    case TERMS.DP_30:
      //removing deposit
      if (payload.deposit) {
        return (remainingBalance = percent(totalAmount, deposit + 30));
      }
      remainingBalance = percent(totalAmount, 30);
      return { remainingBalance };

    case TERMS.DP_50:
      //removing deposit
      if (payload.deposit) {
        return (remainingBalance = percent(totalAmount, deposit + 50));
      }
      remainingBalance = percent(totalAmount, 30);
      return { remainingBalance };

    case TERMS.DP_80:
      //removing deposit
      if (payload.deposit) {
        return (remainingBalance = percent(totalAmount, deposit + 80));
      }
      remainingBalance = percent(totalAmount, 80);
      return { remainingBalance };

    case TERMS.DP_10_RETENTION_10:
      //removing deposit
      if (payload.deposit) {
        totalAmount = percent(totalAmount, deposit + 10);
      }
      //adding the tax/extra charges
      remainingBalance = remainingBalance = percent(totalAmount, 10);
      retentionBalance = remainingBalance * 0.1;

      return { remainingBalance, retentionBalance };
    case TERMS.DP_20_RETENTION_10:
      //removing deposit
      if (payload.deposit) {
        totalAmount = percent(totalAmount, deposit + 20);
      }
      //adding the tax/extra charges
      remainingBalance = remainingBalance = percent(totalAmount, 20);
      retentionBalance = remainingBalance * 0.1;

      return { totalAmount, remainingBalance, retentionBalance };
    case TERMS.DP_30_RETENTION_10:
      //removing deposit
      if (payload.deposit) {
        totalAmount = percent(totalAmount, deposit + 30);
      }
      //adding the tax/extra charges
      remainingBalance = remainingBalance = percent(totalAmount, 30);
      retentionBalance = remainingBalance * 0.1;

      return { totalAmount, remainingBalance, retentionBalance };
    case TERMS.CIA:
      return { employeeId, totalAmount };

    case TERMS.NET_15:
      issueDate = new Date(issuedInvoiceDate);
      payableDate = issueDate.setDate(issueDate.getDate() + 15);
      return { totalAmount, payableDate };

    case TERMS.NET_30:
      issueDate = new Date(issuedInvoiceDate);
      payableDate = issueDate.setDate(issueDate.getDate() + 30);
      return { totalAmount, payableDate };
    default:
      break;
  }
};

module.exports = terms;
