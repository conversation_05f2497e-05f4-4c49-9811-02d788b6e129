class AuditLog {
  constructor(container) {
    const { auditLogService, entities } = container;
    this.auditLogService = auditLogService;
    this.auditLogEntity = entities.auditLog;
  }

  async getAllAuditLogs(request, reply) {
    const { sortBy, ...queries } = request.query;
    const { auditLogSortSchema } = this.auditLogEntity;

    const parsedSortBy = auditLogSortSchema.parse(sortBy);

    const auditLogs = await this.auditLogService.getAllAuditLogs({
      ...queries,
      order: parsedSortBy,
    });

    return reply.status(200).send(auditLogs);
  }

  async getAuditLogById(request, reply) {
    const { id } = request.params;
    const auditLog = await this.auditLogService.getAuditLogById(id);
    return reply.status(200).send(auditLog);
  }
}

module.exports = AuditLog;
