class Supplier {
  constructor(container) {
    const {
      db,
      utils,
      supplierRepository,
      clientErrors,
      attachmentRepository,
      supplierService,
      commentRepository,
      entities,
      syncRepository,
      commentBadgeRepository,
      attachmentBadgeRepository,
      supplierSyncService,
      syncService,
    } = container;

    this.db = db;
    this.supplierRepository = supplierRepository;
    this.clientErrors = clientErrors;
    this.attachmentRepository = attachmentRepository;
    this.supplierService = supplierService;
    this.commentRepository = commentRepository;
    this.supplierEntity = entities.supplier;
    this.syncRepository = syncRepository;
    this.commentBadgeRepository = commentBadgeRepository;
    this.attachmentBadgeRepository = attachmentBadgeRepository;
    this.utils = utils;
    this.supplierSyncService = supplierSyncService;
    this.syncService = syncService;
  }

  async getAllSuppliers(request, reply) {
    const { sortBy, filterBy, ...queries } = request.query;
    const { supplierSortSchema, supplierFilterSchema } = this.supplierEntity;

    const transformSortBy = (sortBy) => {
      if (sortBy) {
        const parsed = JSON.parse(sortBy);

        if (parsed.lineOfBusiness) {
          parsed.lineOfBusiness =
            parsed.lineOfBusiness === 'DESC'
              ? 'DESC NULLS LAST'
              : 'ASC NULLS FIRST';
        }

        if (parsed.contactPerson) {
          parsed.contactPerson =
            parsed.contactPerson === 'DESC'
              ? 'DESC NULLS LAST'
              : 'ASC NULLS FIRST';
        }

        if (parsed.contactNumber) {
          parsed.contactNumber =
            parsed.contactNumber === 'DESC'
              ? 'DESC NULLS LAST'
              : 'ASC NULLS FIRST';
        }

        return JSON.stringify(parsed);
      }

      return sortBy;
    };

    const parsedSortBy = supplierSortSchema.parse(transformSortBy(sortBy));
    const parsedFilterBy = supplierFilterSchema.parse(filterBy);

    const filterByWhereClause =
      this.utils.buildFilterWhereClause(parsedFilterBy);

    const suppliers = await this.supplierRepository.getAllSuppliers({
      ...queries,
      filterBy: filterByWhereClause,
      order: parsedSortBy,
    });

    const supplierSync = await this.syncRepository.findByModel('supplier');

    return reply.status(200).send({
      ...suppliers,
      lastSyncedAt: supplierSync?.lastSyncedAt || null,
    });
  }

  async getAllActiveSuppliers(request, reply) {
    const suppliers = await this.supplierRepository.getAllActiveSuppliers();

    return reply.status(200).send(suppliers);
  }

  async getSupplierById(request, reply) {
    const supplier = await this.supplierRepository.getSupplierById(
      request.params.id,
    );

    supplier.noteBadge = await this.supplierService.commentBadge(
      request.userFromToken.id,
      supplier.id,
    );

    supplier.attachmentBadge = await this.supplierService.attachmentBadge(
      request.userFromToken.id,
      supplier.id,
    );

    return reply.status(200).send(supplier);
  }

  async updateSupplier(request, reply) {
    const { userFromToken } = request;
    const transaction = await this.db.sequelize.transaction();
    const previousValue = await this.supplierRepository.getSupplierById(
      request.params.id,
    );

    let updatedSupplier;
    try {
      const userId = userFromToken.id;
      const updateData = {
        ...request.body,
        userId,
        dateModified: new Date(),
      };
      updatedSupplier = await this.supplierRepository.updateById(
        request.params.id,
        updateData,
      );
      updatedSupplier = await this.supplierRepository.getSupplierById(
        request.params.id,
      );

      if (
        previousValue.status !== 'SUSPENDED' &&
        request.body?.status === 'SUSPENDED'
      ) {
        await this.supplierSyncService.processNotification({
          supplier: updatedSupplier,
          userId,
          notificationType: 'SUPPLIER_SUSPENDED',
        });
      }

      await transaction.commit();
    } catch (error) {
      console.log(error);
      await transaction.rollback();
      throw error;
    }

    return reply.status(200).send({ ...updatedSupplier, previousValue });
  }

  async updateById(id, data) {
    const updatedSupplier = await this.supplierRepository.updateById(id, data);

    if (!updatedSupplier) {
      throw this.clientErrors.NOT_FOUND({ message: 'Supplier not found' });
    }

    return updatedSupplier;
  }

  async destroySupplierById(request, reply) {
    const { id } = request.params;
    await this.supplierRepository.destroy(id);
    return reply.status(200).send({
      message: `Supplier with id of ${id} successfully deleted`,
    });
  }

  async syncSuppliers(request, reply) {
    const { userFromToken } = request;
    const syncResult = await this.syncService.queueSync({
      userFromToken,
      type: 'supplier',
      withTimestamp: false,
    });

    return reply.status(200).send(syncResult);
  }

  async createSupplierComment(request, reply) {
    const { userFromToken } = request;
    const supplier = await this.supplierRepository.getSupplierById(
      request.params.id,
    );

    if (!supplier) {
      throw this.clientErrors.NOT_FOUND({ message: 'Supplier not found' });
    }

    await this.commentRepository.create({
      model: 'supplier',
      modelId: supplier.id,
      commentedBy: userFromToken.id,
      comment: request.body.comment,
    });
    return reply
      .status(200)
      .send({ message: 'Supplier comment created successfully' });
  }

  async createSupplierAttachments(request, reply) {
    const { userFromToken } = request;

    const supplier = await this.supplierRepository.getSupplierById(
      request.params.id,
    );

    if (!supplier) {
      throw this.clientErrors.NOT_FOUND({ message: 'Supplier not found' });
    }
    const paths = request.body.attachments.map((file) => ({
      path: `/suppliers/${file.filePath}`,
      fileName: file.originalname,
    }));

    const transaction = await this.db.sequelize.transaction();

    try {
      await Promise.all(
        paths.map(async (file) => {
          await this.attachmentRepository.create({
            model: 'supplier',
            modelId: request.params.id,
            userId: userFromToken.id,
            path: file.path,
            fileName: file.fileName,
          });
        }),
      );

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }

    return reply
      .status(200)
      .send({ message: 'Supplier attachments created successfully' });
  }

  async getSupplierComments(request, reply) {
    const { id } = request.params;
    const { commentDateFrom, commentDateTo } = request.query;
    const whereClause = {
      modelId: id,
      model: 'supplier', // TODO: make this dynamic
    };

    // Check for commentDateFrom and commentDateTo in the request query
    if (commentDateFrom || commentDateTo) {
      whereClause.createdAt = {};

      if (commentDateFrom) {
        whereClause.createdAt[this.db.Sequelize.Op.gte] = commentDateFrom;
      }

      if (commentDateTo) {
        whereClause.createdAt[this.db.Sequelize.Op.lte] = commentDateTo;
      }
    }

    const comments = await this.commentRepository.findAll({
      where: whereClause,
      include: [
        {
          model: this.db.userModel,
          as: 'userComment',
          attributes: ['id', 'firstName', 'lastName'],
        },
        {
          model: this.db.commentBadgeModel,
          as: 'commentBadge',
        },
      ],
    });

    const groupedData = {};

    comments.data.forEach((comment) => {
      const createdDate = comment.createdAt.toISOString().split('T')[0]; // Extract date from createdAt
      const user = comment.userComment;

      if (!groupedData[createdDate]) {
        groupedData[createdDate] = {};
      }

      if (!groupedData[createdDate][user.id]) {
        groupedData[createdDate][user.id] = {
          id: user.id,
          firstName: user.firstName,
          lastName: user.lastName,
          comments: [],
        };
      }

      groupedData[createdDate][user.id].noteBadge =
        comment.commentBadge === null ? true : false;

      groupedData[createdDate][user.id].comments.push({
        id: comment.id,
        comment: comment.comment,
      });
    });

    const result = Object.entries(groupedData).map(([createdAt, users]) => ({
      createdAt,
      data: Object.values(users),
    }));

    return reply.status(200).send(result);
  }

  async getSupplierAttachments(request, reply) {
    const { id } = request.params;
    const { attachmentDateFrom, attachmentDateTo } = request.query;
    const whereClause = {
      modelId: id,
      model: 'supplier', // TODO: make this dynamic
    };

    // Check for attachmentDateFrom and attachmentDateTo in the request query
    if (attachmentDateFrom || attachmentDateTo) {
      whereClause.createdAt = {};

      if (attachmentDateFrom) {
        whereClause.createdAt[this.db.Sequelize.Op.gte] = attachmentDateFrom;
      }

      if (attachmentDateTo) {
        whereClause.createdAt[this.db.Sequelize.Op.lte] = attachmentDateTo;
      }
    }

    const attachments = await this.attachmentRepository.findAll({
      where: whereClause,
      paginate: false,
      include: [
        {
          model: this.db.userModel,
          as: 'userAttachment',
          attributes: ['id', 'firstName', 'lastName'],
        },
        {
          model: this.db.attachmentBadgeModel,
          as: 'attachmentBadge',
        },
      ],
    });

    const hasNewNotifications = attachments.data.some((attachment) =>
      attachment.attachmentBadge === null ? true : false,
    );

    const attachmentsData = attachments.data.map((attachment) => ({
      id: attachment?.id,
      fileName: attachment?.fileName,
      model: attachment?.model,
      modelId: attachment?.modelId,
      path: attachment?.path,
      userAttachment: attachment?.userAttachment,
      createdAt: attachment?.createdAt,
      updatedAt: attachment?.updatedAt,
    }));

    const result = {
      data: attachmentsData,
      hasNewNotifications,
      total: attachments.total,
    };

    return reply.status(200).send(result);
  }

  async markCommentOrAttachmentAsSeen(request, reply) {
    const { model, supplierId } = request.body;

    const modelData = await this.supplierRepository.findOne({
      where: {
        id: supplierId,
      },
      include: [
        {
          model: this.db[`${model}Model`],
          as: `${model}s`,
          required: true,
          include: [
            {
              model: this.db[`${model}BadgeModel`],
              as: `${model}Badge`,
            },
          ],
        },
      ],
    });

    if (!modelData) {
      throw this.clientErrors.NOT_FOUND({ message: `${model}s not found` });
    }

    const modelIds = modelData[`${model}s`].map((data) => data.id);

    await this[`${model}BadgeRepository`].bulkCreate(
      modelIds.map((id) => ({
        userId: request.userFromToken.id,
        [`${model}Id`]: id,
      })),
    );

    return reply
      .status(200)
      .send({ message: `${model} with id of ${supplierId} marked as seen` });
  }

  async updateAttachment(request, reply) {
    const { body } = request;
    const { id } = request.params;
    let previousValue;
    let newValue;

    const whereClause = {
      modelId: id,
      model: 'supplier', // TODO: make this dynamic
    };
    try {
      previousValue = await this.attachmentRepository.findAll({
        where: whereClause,
        include: [
          {
            model: this.db.userModel,
            as: 'userAttachment',
            attributes: ['id', 'firstName', 'lastName'],
          },
        ],
      });

      await this.supplierService.editAttachment(body);

      newValue = await this.attachmentRepository.findAll({
        where: whereClause,
        include: [
          {
            model: this.db.userModel,
            as: 'userAttachment',
            attributes: ['id', 'firstName', 'lastName'],
          },
        ],
      });
    } catch (error) {
      throw this.clientErrors.NOT_FOUND({
        message: 'Failed while updating Supplier attachments',
      });
    }

    return reply.status(200).send({
      message: `Successfully updated Supplier Attachments`,
      previousValue: previousValue.data,
      newValue: newValue.data,
    });
  }
}

module.exports = Supplier;
