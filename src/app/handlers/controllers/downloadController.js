class Download {
  constructor(container) {
    const {
      db,
      utils,
      entities,
      leaveService,
      fastify,
      leaveRepository,
      clientErrors,
      downloadService,
      nonRequisitionRepository,
    } = container;
    this.fastify = fastify;
    this.db = db;
    this.utils = utils;
    this.entities = entities;
    this.leaveService = leaveService;
    this.leaveRepository = leaveRepository;
    this.clientErrors = clientErrors;
    this.downloadService = downloadService;
    this.nonRequisitionRepository = nonRequisitionRepository;
    this.requisitionEntity = entities.requisition;
    this.downloadEntity = entities.downloadExcel;
  }
  async downloadDashboardExcel(request, reply) {
    this.fastify.log.info(`Generating dashboard Excel file...`);

    const { requisitionSortSchema, requisitionFilterSchema } =
      this.requisitionEntity;

    const date = new Date();

    const utcMilliseconds =
      date.getTime() + date.getTimezoneOffset() * 60 * 1000;

    const phOffset = 8 * 60 * 60 * 1000;

    const phTime = new Date(utcMilliseconds + phOffset);

    const fileName = `RSDASHBOARD_${this.utils.convertDateYYYYMMDDHHMMSS(phTime)}`;

    const { sortBy, filterBy, ...queries } = request.query;

    const parsedSortBy = requisitionSortSchema.parse(sortBy);
    const parsedFilterBy = requisitionFilterSchema.parse(filterBy);

    try {
      const result = await this.downloadService.downloadDashboardExcel({
        sortBy: parsedSortBy,
        filterBy: parsedFilterBy,
        userFromToken: request.userFromToken,
        ...queries,
      });

      return reply.status(200).send({ fileName, data: result });
    } catch (error) {
      this.fastify.log.error(
        `Error in dashboard excel controller: ${error.message}`,
      );
      throw error;
    }
  }

  async downloadNonRSDashboard(request, reply) {
    const { userFromToken } = request;
    const { category } = request.body;
    const userId = userFromToken.id;
    let fileName = category || 'all';

    const nonRSData = await this.nonRequisitionRepository.getAllNonRs({
      userId,
      category,
      paginate: false,
    });

    if (!nonRSData.total) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'No dashboard data to download',
      });
    }

    const downloadPayload = nonRSData.data.map((data) => {
      const {
        chargeTo,
        status,
        totalDiscountedAmount,
        updatedAt,
        requestor,
        nonRsLetter,
        draftNonRsNumber,
        nonRsNumber,
      } = data;

      const isDraft = status === 'draft';
      const nonRSNumber = isDraft
        ? `NRS-TMP-${nonRsLetter}${draftNonRsNumber}`
        : `NRS-${nonRsLetter}${nonRsNumber}`;
      const requestedBy = requestor?.fullName ?? '';

      return {
        'Non-RS Number': nonRSNumber,
        'Charge To': chargeTo,
        'Requested By': requestedBy,
        'Last Updated': updatedAt,
        Amount: totalDiscountedAmount,
        Status: status,
      };
    });

    const excelFile = this.downloadService.generateExcelFile(downloadPayload);

    return reply.status(200).send({ fileName, data: excelFile });
  }

  async downloadItemHistory(request, reply) {
    this.fastify.log.info(`Generating item history Excel file...`);

    const { type, filterBy, sortBy } = request.query;
    const { id } = request.params;
    const parsedSortBy =
      this.downloadEntity.downloadItemHistorySortSchema.parse(sortBy);
    const parsedFilterBy =
      this.downloadEntity.downloadItemHistoryFilterSchema.parse(filterBy);

    const date = new Date();
    const utcMilliseconds =
      date.getTime() + date.getTimezoneOffset() * 60 * 1000;
    const phOffset = 8 * 60 * 60 * 1000;
    const phTime = new Date(utcMilliseconds + phOffset);

    // Format filename according to requirements
    const formattedDate = this.utils.convertDateYYYYMMDDHHMMSS(phTime);
    const fileName =
      type === 'ofm'
        ? `OFM_ITEMHISTORY_${formattedDate}`
        : `NONOFM_ITEMHISTORY_${formattedDate}`;

    try {
      const excelData = await this.downloadService.downloadItemHistory({
        id,
        type,
        filterBy: parsedFilterBy,
        sortBy: parsedSortBy,
      });

      return reply.status(200).send({ fileName, data: excelData });
    } catch (error) {
      this.fastify.log.error(
        `Error in item history excel controller: ${error.message}`,
      );
      throw error;
    }
  }

  async downloadOfmItems(request, reply) {
    this.fastify.log.info(`Generating OFM Items Excel file...`);

    const date = new Date();
    const utcMilliseconds = date.getTime() + date.getTimezoneOffset() * 60 * 1000;
    const phOffset = 8 * 60 * 60 * 1000;
    const phTime = new Date(utcMilliseconds + phOffset);

    const fileName = `OFMITEM_${this.utils.convertDateYYYYMMDDHHMMSS(phTime)}`;

    try {
      const result = await this.downloadService.downloadOfmItems({
        userFromToken: request.userFromToken,
        ...request.query,
      });

      return reply.status(200).send({ fileName, data: result });
    } catch (error) {
      this.fastify.log.error(`Error in OFM items excel controller: ${error.message}`);
      throw error;
    }
  }

  async downloadOfmList(request, reply) {
    this.fastify.log.info(`Generating OFM List Excel file...`);

    const date = new Date();
    const utcMilliseconds = date.getTime() + date.getTimezoneOffset() * 60 * 1000;
    const phOffset = 8 * 60 * 60 * 1000;
    const phTime = new Date(utcMilliseconds + phOffset);

    const fileName = `OFMLIST_${this.utils.convertDateYYYYMMDDHHMMSS(phTime)}`;

    try {
      const result = await this.downloadService.downloadOfmList({
        userFromToken: request.userFromToken,
        ...request.query,
      });

      return reply.status(200).send({ fileName, data: result });
    } catch (error) {
      this.fastify.log.error(`Error in OFM list excel controller: ${error.message}`);
      throw error;
    }
  }
}

module.exports = Download;
