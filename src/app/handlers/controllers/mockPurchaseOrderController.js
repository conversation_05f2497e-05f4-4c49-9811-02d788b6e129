class MockPurchaseOrderController {
  constructor({ mockPurchaseOrderRepository }) {
    this.mockPurchaseOrderRepository = mockPurchaseOrderRepository;
  }

  async getAll() {
    const results = this.mockPurchaseOrderRepository.getAll();
    return results;
  }

  async getById(request) {
    const item = this.mockPurchaseOrderRepository.getById(request.params.id);
    return item;
  }
}

module.exports = MockPurchaseOrderController;
