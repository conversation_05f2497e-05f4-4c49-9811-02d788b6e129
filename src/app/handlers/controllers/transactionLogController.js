class TransactionLog {
  constructor(container) {
    const { transactionLogService, entities, clientErrors } = container;
    this.transactionLogService = transactionLogService;
    this.entities = entities.transactionLog;
    this.clientErrors = clientErrors;
  }

  async getRequisitionJourney(request, reply) {
    const { rsId } = request.params;

    if (isNaN(rsId)) {
      throw this.clientErrors.badRequest(`Requisition ID must be a number`);
    }

    const history =
      await this.transactionLogService.getAllRequisitionIdJourney(rsId);

    if (history.total === 0) {
      return reply.send({
        message: `No logs found for ${rsId}`,
      });
    }

    return reply.send({
      message: `Requisition ${rsId} journey retrieved successfully`,
      history,
    });
  }

  async createTransactionLog(request, reply) {
    try {
      const { body } = request;

      const log = await this.transactionLogService.createLog(body);

      return reply.send({
        message: 'Transaction log created successfully',
        log,
      });
    } catch (error) {
      throw this.clientErrors.BAD_REQUEST({
        message: error.message || JSON.stringify(error),
      });
    }
  }
}

module.exports = TransactionLog;
