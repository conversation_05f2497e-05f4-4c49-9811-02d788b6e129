class HealthController {
  constructor({ db }) {
    this.database = db;
  }

  async healthCheck(request, reply) {
    const startTime = Date.now();
    const healthStatus = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      service: 'prs-backend',
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV,
      uptime: process.uptime(),
      checks: {},
    };

    try {
      // Database connectivity check
      const dbStartTime = Date.now();
      await this.database.sequelize.authenticate();
      const dbEndTime = Date.now();

      healthStatus.checks.database = {
        status: 'healthy',
        responseTime: `${dbEndTime - dbStartTime}ms`,
        connection: 'active',
      };

      // Memory usage check
      const memUsage = process.memoryUsage();
      healthStatus.checks.memory = {
        status:
          memUsage.heapUsed / memUsage.heapTotal < 0.9 ? 'healthy' : 'warning',
        heapUsed: `${Math.round(memUsage.heapUsed / 1024 / 1024)}MB`,
        heapTotal: `${Math.round(memUsage.heapTotal / 1024 / 1024)}MB`,
        external: `${Math.round(memUsage.external / 1024 / 1024)}MB`,
      };

      // System load check (if available)
      if (process.cpuUsage) {
        const cpuUsage = process.cpuUsage();
        healthStatus.checks.cpu = {
          status: 'healthy',
          user: cpuUsage.user,
          system: cpuUsage.system,
        };
      }

      const totalTime = Date.now() - startTime;
      healthStatus.responseTime = `${totalTime}ms`;

      // Log health check
      request.log.info({
        type: 'HEALTH_CHECK',
        status: 'success',
        responseTime: totalTime,
        timestamp: new Date().toISOString(),
        checks: healthStatus.checks,
      });

      return reply.status(200).send(healthStatus);
    } catch (error) {
      healthStatus.status = 'unhealthy';
      healthStatus.checks.database = {
        status: 'unhealthy',
        error: error.message,
      };

      // Log health check failure
      request.log.error({
        type: 'HEALTH_CHECK',
        status: 'failed',
        error: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
      });

      return reply.status(503).send(healthStatus);
    }
  }

  async readinessCheck(request, reply) {
    try {
      // Check if database is ready
      await this.database.sequelize.authenticate();

      // Check if all required environment variables are set
      const requiredEnvVars = ['JWT_SECRET', 'POSTGRES_HOST', 'POSTGRES_DB'];
      const missingVars = requiredEnvVars.filter(
        (varName) => !process.env[varName],
      );

      if (missingVars.length > 0) {
        throw new Error(
          `Missing required environment variables: ${missingVars.join(', ')}`,
        );
      }

      request.log.info({
        type: 'READINESS_CHECK',
        status: 'ready',
        timestamp: new Date().toISOString(),
      });

      return reply.status(200).send({
        status: 'ready',
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      request.log.error({
        type: 'READINESS_CHECK',
        status: 'not_ready',
        error: error.message,
        timestamp: new Date().toISOString(),
      });

      return reply.status(503).send({
        status: 'not_ready',
        error: error.message,
        timestamp: new Date().toISOString(),
      });
    }
  }

  async livenessCheck(request, reply) {
    // Simple liveness check - if we can respond, we're alive
    request.log.info({
      type: 'LIVENESS_CHECK',
      status: 'alive',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
    });

    return reply.status(200).send({
      status: 'alive',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
    });
  }

  async metricsCheck(request, reply) {
    const metrics = {
      timestamp: new Date().toISOString(),
      service: 'prs-backend',
      environment: process.env.NODE_ENV,
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      cpu: process.cpuUsage ? process.cpuUsage() : null,
      version: process.version,
      platform: process.platform,
      arch: process.arch,
    };

    try {
      // Add database metrics
      const dbStats = await this.database.sequelize.query(
        "SELECT count(*) as connection_count FROM pg_stat_activity WHERE state = 'active'",
        { type: this.database.sequelize.QueryTypes.SELECT },
      );

      metrics.database = {
        activeConnections: parseInt(dbStats[0].connection_count),
        poolSize: this.database.sequelize.connectionManager.pool.size,
        poolUsed: this.database.sequelize.connectionManager.pool.used,
      };
    } catch (error) {
      metrics.database = {
        error: error.message,
      };
    }

    request.log.info({
      type: 'METRICS_CHECK',
      metrics,
      timestamp: new Date().toISOString(),
    });

    return reply.status(200).send(metrics);
  }
}

module.exports = HealthController;
