class Approval {
  constructor(container) {
    const { constants, approvalTypeRepository, associationAreaService } =
      container;

    this.constants = constants;
    this.approvalTypeRepository = approvalTypeRepository;
    this.associationAreaService = associationAreaService;
  }

  async getApprovalTypes(_request, reply) {
    const approvalTypes = await this.approvalTypeRepository.findAll({
      paginate: false,
    });

    return reply.status(200).send(approvalTypes);
  }

  async getApprovalAreas(_request, reply) {
    const areaNames = await this.associationAreaService.getAllAreaNames();
    const areas = Object.values(areaNames);

    return reply.status(200).send({
      data: areas,
      total: areas.length,
    });
  }
}

module.exports = Approval;
