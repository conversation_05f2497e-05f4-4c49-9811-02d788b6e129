class RequestHistoryController {
  constructor({ requestHistoryService, clientErrors }) {
    this.requestHistoryService = requestHistoryService;
    this.clientErrors = clientErrors;
  }

  async getRequestHistoryById(request, reply) {
    const { id, type } = request.params;
    const requestHistory =
      await this.requestHistoryService.getRequestHistoryfromRS(id, type);

    return reply.status(200).send(requestHistory);
  }
}

module.exports = RequestHistoryController;
