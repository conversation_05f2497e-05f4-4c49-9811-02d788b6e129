class TomItemController {
  constructor(container) {
    const { tomItemRepository, clientErrors } = container;

    this.tomItemRepository = tomItemRepository;
    this.clientErrors = clientErrors;
  }

  async removeTomItemById(request, reply) {
    const { id } = request.params;

    const tomItem = await this.tomItemRepository.getById(id);

    if (!tomItem) {
      throw this.clientErrors.NOT_FOUND({
        message: `Tom item not found for id ${id}`,
      });
    }

    const deleted = await this.tomItemRepository.destroyById(id);

    if (!deleted) {
      throw this.clientErrors.BAD_REQUEST({
        message: `Failed to delete tom item with id ${id}`,
      });
    }

    return reply.status(200).send({
      message: `Tom item with id ${id} deleted successfully`,
    });
  }

  async updateTomItem(request, reply) {
    const { id } = request.params;
    const { body } = request;

    const tomItem = await this.tomItemRepository.getById(id);

    if (!tomItem) {
      throw this.clientErrors.NOT_FOUND(`Tom item not found for id ${id}`);
    }

    const updated = await this.tomItemRepository.update({ id }, body);

    if (!updated) {
      throw this.clientErrors.BAD_REQUEST({
        message: `Failed to update tom item with id ${id}`,
      });
    }

    return reply.status(200).send({
      message: `Tom item with id ${id} updated successfully`,
      previousValue: tomItem,
    });
  }
}

module.exports = TomItemController;
