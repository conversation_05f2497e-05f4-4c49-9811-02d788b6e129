class RequisitionItemList {
  constructor(container) {
    const {
      db,
      requisitionItemListRepository,
      requisitionRepository,
      clientErrors,
      itemRepository,
      nonOfmItemRepository,
      requisitionItemListService,
    } = container;

    this.db = db;
    this.requisitionItemListRepository = requisitionItemListRepository;
    this.requisitionRepository = requisitionRepository;
    this.clientErrors = clientErrors;
    this.itemRepository = itemRepository;
    this.nonOfmItemRepository = nonOfmItemRepository;
    this.requisitionItemListService = requisitionItemListService;
  }

  async createRequisitionItemList(request, reply) {
    const { body, params } = request;
    const { requisitionId } = params;

    const requisition = await this.requisitionRepository.getById(requisitionId);

    if (!requisition) {
      throw this.clientErrors.NOT_FOUND({
        message: `Requisition with id of ${requisitionId} not found`,
      });
    }

    const requisitionItemList = await Promise.all(
      body.map(async (items) => {
        let item;
        if (requisition.type === 'ofm' || requisition.type === 'ofm-tom') {
          item = await this.itemRepository.getById(items.itemId);
        } else if (
          requisition.type === 'non-ofm' ||
          requisition.type === 'non-ofm-tom'
        ) {
          item = await this.nonOfmItemRepository.getById(items.itemId);
        }

        if (!item) {
          throw this.clientErrors.NOT_FOUND({
            message: `Item with id of ${items.itemId} not found`,
          });
        }

        return await this.requisitionItemListRepository.create({
          itemId: items.itemId,
          quantity: items.quantity,
          notes: items.notes,
          requisitionId,
          itemType: requisition.type,
          accountCode: items.accountCode,
        });
      }),
    );

    return reply.status(201).send(requisitionItemList);
  }

  async removeRequisitionItemList(request, reply) {
    const { id } = request.params;

    const requisitionItemList =
      await this.requisitionItemListRepository.getById(id);

    if (!requisitionItemList) {
      throw this.clientErrors.NOT_FOUND({
        message: `Requisition item list with id ${id} not found`,
      });
    }

    const deleted = await this.requisitionItemListRepository.destroyById(id);

    if (!deleted) {
      throw this.clientErrors.BAD_REQUEST({
        message: `Failed to delete requisition item list with id ${id}`,
      });
    }

    return reply.status(200).send({
      message: `Requisition item list with id ${id} deleted successfully`,
    });
  }

  async updateRequisitionItemList(request, reply) {
    const { id } = request.params;
    const { body } = request;

    const requisitionItemList =
      await this.requisitionItemListRepository.getById(id);

    if (!requisitionItemList) {
      throw this.clientErrors.NOT_FOUND({
        message: 'Requisition item list not found',
      });
    }

    const updated = await this.requisitionItemListRepository.update(
      { id },
      body,
    );

    if (!updated) {
      throw this.clientErrors.BAD_REQUEST({
        message: `Failed to update requisition item list with id ${id}`,
      });
    }

    return reply.status(200).send({
      message: `Requisition item list with id ${id} updated successfully`,
      previousValue: requisitionItemList,
    });
  }

  async deleteRequisitionItemList(request, reply) {
    const { userFromToken, params } = request;

    const deletedRsItem =
      await this.requisitionItemListService.deleteRequisitionItemList({
        userFromToken,
        params,
      });

    return reply.status(200).send({
      message: `Requisition item list with id ${params.itemId} : ${params.requisitionId} deleted successfully`,
    });
  }
}

module.exports = RequisitionItemList;
