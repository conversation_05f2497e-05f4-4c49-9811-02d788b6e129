class Department {
  constructor(container) {
    const {
      db,
      utils,
      entities,
      constants,
      userService,
      clientErrors,
      syncRepository,
      departmentService,
      departmentRepository,
      approvalTypeRepository,
      departmentApprovalService,
      departmentAssociationApprovalService,
    } = container;

    this.db = db;
    this.utils = utils;
    this.entities = entities;
    this.constants = constants;
    this.userService = userService;
    this.clientErrors = clientErrors;
    this.syncRepository = syncRepository;
    this.departmentService = departmentService;
    this.departmentRepository = departmentRepository;
    this.approvalTypeRepository = approvalTypeRepository;
    this.departmentApprovalService = departmentApprovalService;
    this.departmentAssociationApprovalService =
      departmentAssociationApprovalService;
  }

  async syncDepartments(request, reply) {
    const userDetails = {
      userId: request.userFromToken.id,
      username: request.userFromToken.username,
      role: request.userFromToken.role?.name,
    };
    const lastSyncedAt =
      await this.departmentService.syncDepartments(userDetails);

    return reply.status(200).send({
      lastSyncedAt,
    });
  }

  async getAllDepartments(request, reply) {
    let whereClause = {};
    const { sortBy, filterBy, ...queries } = request.query;
    const { departmentSortSchema, departmentFilterSchema } =
      this.entities.department;

    const parsedFilterBy = departmentFilterSchema.parse(filterBy);
    const parsedSortBy = departmentSortSchema.parse(sortBy);

    const { code, name } = this.utils.buildFilterWhereClause(parsedFilterBy);

    if (name) {
      whereClause.name = { [this.db.Sequelize.Op.iLike]: `%${name}%` };
    }

    if (code) {
      whereClause.code = {
        [this.db.Sequelize.Op.eq]: `${parseInt(code)}`,
      };
    }

    const departments = await this.departmentRepository.getAllDepartments({
      ...queries,
      order: parsedSortBy,
      whereClause,
    });

    const departmentSync = await this.syncRepository.findByModel('department');

    return reply.status(200).send({
      ...departments,
      lastSyncedAt: departmentSync?.lastSyncedAt || null,
    });
  }

  async getDepartmentById(request, reply) {
    const { id } = request.params;
    const department = await this.departmentService.getDepartmentById(id);
    return reply.status(200).send(department);
  }

  async getDepartmentApprovals(request, reply) {
    const existingDepartment = await this.departmentService.getDepartmentById(
      parseInt(request.params.id),
    );

    const departmentApprovals =
      await this.departmentApprovalService.getDepartmentApprovals(
        existingDepartment.id,
        request.query.type,
      );

    return reply.status(200).send(departmentApprovals);
  }

  async setupDepartmentApprovals(request, reply) {
    const { setupApprovalSchema } = this.entities.approval;
    const parsedBody = setupApprovalSchema.parse(request.body);
    const { approvalTypeCode, approvers, optionalApprovers } = parsedBody;

    const existingDepartment = await this.departmentService.getDepartmentById(
      parseInt(request.params.id),
    );

    /* Check if approvers exist / currently active */
    const allApproverIds = [
      ...approvers.map((a) => a.approverId),
      ...optionalApprovers.map((a) => a.approverId),
    ];

    const previousValue =
      await this.departmentApprovalService.getDepartmentApprovals(
        existingDepartment.id,
        approvalTypeCode,
      );

    await this.userService.validateMultipleUsers(allApproverIds, {
      roleNames: Object.values(this.constants.user.APPROVERS),
    });

    const departmentApprovers =
      await this.departmentApprovalService.setupDepartmentApprovals({
        departmentId: existingDepartment.id,
        approvalTypeCode,
        approvers,
        optionalApprovers,
      });

    return reply
      .status(200)
      .send({ departmentApprovers, previousValue: previousValue.data[0] });
  }

  async setupDepartmentAssociationApprovals(request, reply) {
    const assocApprovals = request.body;

    const approversByLevel = assocApprovals.approvers.reduce(
      (acc, approver) => {
        const key = approver.level === 1 ? 'levelOne' : 'otherLevels';
        acc[key].push(approver.approverId);

        return acc;
      },
      { levelOne: [], otherLevels: [] },
    );

    /* Validate approvers - and its role types*/
    await Promise.all([
      this.userService.validateMultipleUsers(approversByLevel.levelOne, {
        roleNames: [this.constants.user.USER_TYPES.AREA_STAFF],
      }),
      this.userService.validateMultipleUsers(approversByLevel.otherLevels, {
        roleNames: Object.values(this.constants.user.APPROVERS),
      }),
    ]);

    const previousValue =
      await this.departmentAssociationApprovalService.getDepartmentAssociationApprovals(
        assocApprovals.approvalTypeCode,
      );

    const departmentAssociationApprovals =
      await this.departmentAssociationApprovalService.setupDepartmentAssociationApprovals(
        assocApprovals,
      );

    return reply.status(200).send({
      ...departmentAssociationApprovals,
      previousValue,
    });
  }

  async getDepartmentAssociationApprovals(request, reply) {
    const assocApprovals =
      await this.departmentAssociationApprovalService.getDepartmentAssociationApprovals(
        request.query.approvalTypeCode,
      );

    return reply.status(200).send(assocApprovals);
  }
}

module.exports = Department;
