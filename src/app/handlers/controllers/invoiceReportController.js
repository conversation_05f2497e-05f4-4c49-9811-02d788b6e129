const {
  createUpdateInvoiceReportSchema,
} = require('../../../domain/entities/invoiceReportEntity');
const {
  PAGINATION_DEFAULTS,
} = require('../../../domain/constants/deliveryReceiptConstants');
const { convertDateToDDMMMYYYY } = require('../../../app/utils');
const fs = require('fs');

class InvoiceReportController {
  constructor({ invoiceReportService, templateService, utils, clientErrors }) {
    this.invoiceReportService = invoiceReportService;
    this.templateService = templateService;
    this.utils = utils;
    this.clientErrors = clientErrors;
  }

  async createInvoiceReport(request, reply) {
    const { body, userFromToken } = request;
    createUpdateInvoiceReportSchema.parse(body);

    const invoiceReport = await this.invoiceReportService.createInvoiceReport(
      body,
      userFromToken,
      request.transaction,
    );

    return reply.status(201).send({ ...invoiceReport });
  }

  async getInvoiceReportById(request, reply) {
    const { id } = request.params;
    const invoiceReport =
      await this.invoiceReportService.getInvoiceReportById(id);

    return reply.status(200).send(invoiceReport);
  }

  async getDeliveryReportsByInvoiceReportId(request, reply) {
    const { id } = request.params;
    const deliveryReports =
      await this.invoiceReportService.getDeliveryReportsByInvoiceReportId(
        id,
        request.query,
      );

    return reply.status(200).send(deliveryReports);
  }

  async getDeliveryReportItemsByInvoiceReportId(request, reply) {
    const { id } = request.params;
    const deliveryReportItems =
      await this.invoiceReportService.getDeliveryReportItemsByInvoiceReportId(
        id,
        request.query,
      );

    return reply.status(200).send(deliveryReportItems);
  }

  async updateInvoiceReport(request, reply) {
    const { body, params, userFromToken } = request;
    createUpdateInvoiceReportSchema.parse(body);
    const invoiceReport = await this.invoiceReportService.updateInvoiceReport(
      params.id,
      body,
      userFromToken,
      request.transaction,
    );

    return reply.status(200).send({ ...invoiceReport });
  }

  async getInvoiceReportFromRequisitionId(request, reply) {
    const { query, params } = request;
    const deliveryReturns =
      await this.invoiceReportService.getInvoiceReportsFromRequisitionId(
        params.requisitionId,
        {
          search: query.search,
          page: query.page,
          limit: query.limit,
          sortBy: query.sortBy,
        },
      );

    return reply.status(200).send({ ...deliveryReturns });
  }

  async generatePdf(request, reply) {
    const { id } = request.params;
    const invoiceReportId = parseInt(id);

    try {
      const [invoiceReport, deliveryReports] = await Promise.all([
        this.invoiceReportService.getInvoiceReportById(invoiceReportId),
        this.invoiceReportService.getDeliveryReportsByInvoiceReportId(
          invoiceReportId,
          { paginate: false },
        ),
      ]);

      if (!deliveryReports?.total) {
        throw this.clientErrors.BAD_REQUEST({
          message: 'No delivery reports found for this Invoice Report',
        });
      }

      const formatValue = (value) => value || '---';

      const headers = {
        status: formatValue(invoiceReport.status),
        invoiceNumber: formatValue(invoiceReport.irNumber),
        poNumber: formatValue(invoiceReport.purchaseOrder.poNumber),
        supplierInvoiceNo: formatValue(invoiceReport.supplierInvoiceNo),
        supplierInvoiceAmount: formatValue(invoiceReport.invoiceAmount)
          ? `₱${parseFloat(invoiceReport.invoiceAmount).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
          : null,
        issuedDate: invoiceReport.issuedInvoiceDate
          ? convertDateToDDMMMYYYY(invoiceReport.issuedInvoiceDate, true)
          : '---',
      };

      const allItems = deliveryReports.data.map((report, index) => ({
        supplierRRNo: formatValue(report.invoiceNumber),
        dateDelivered: report.issuedDate
          ? convertDateToDDMMMYYYY(report.issuedDate, true)
          : '---',
        status: formatValue(report.status),
      }));

      const { pagesData, totalPages, totalItems } =
        this.utils.paginateItemsWithDifferentSizes(allItems, 35, 39);

      const data = {
        ...headers,
        pagesData,
        totalPages,
        totalItems,
      };

      const result = await this.templateService.generateDynamicTemplate(
        data,
        'invoice-template.hbs',
        'invoice_report_downloads',
        'IR',
      );

      const fileStream = fs.createReadStream(result.filePath);

      return reply
        .header('Content-Type', 'application/pdf')
        .header(
          'Content-Disposition',
          `attachment; filename="${result.fileName}"`,
        )
        .send(fileStream);
    } catch (error) {
      throw error;
    }
  }
}
module.exports = InvoiceReportController;
