const pick = (obj, arr) => {
  if (!arr) {
    return;
  }
  return arr.reduce(
    (acc, record) => (record in obj && (acc[record] = obj[record]), acc),
    {},
  );
};

/**
 * Determines if the current request should be skipped for logging.
 * @param {object} request - The Fastify request object.
 * @param {object} constants - The application constants.
 * @returns {boolean} - True if logging should be skipped.
 */
function shouldSkipLog(request, constants) {
  const { method, routeOptions } = request;
  const url = routeOptions.config.url;

  // Skip GET requests
  if (method === 'GET') {
    return true;
  }

  // Skip if user is not authenticated for a route that should be logged
  if (!request.userFromToken) {
    return true;
  }

  const allowedRoutes = Object.values(constants.log.PRS_JOURNEY);
  const skipRoutes = ['non-requisitions'];

  const isRouteSkipped = skipRoutes.some((module) => url.includes(module));
  if (isRouteSkipped) {
    return true;
  }

  const isRouteAllowed = allowedRoutes.some((module) => url.includes(module));
  if (!isRouteAllowed) {
    return true;
  }

  return false;
}
/**
 * Modular configuration for fetching rsId from request parameters.
 */
const ID_FETCH_CONFIG = {
  canvass: { repository: 'canvassRequisitionRepository', idParam: 'id' },
  'purchase-orders': {
    repository: 'purchaseOrderRepository',
    idParam: 'purchaseOrderId',
  },
  'rs-payment-request': {
    repository: 'rsPaymentRequestRepository',
    idParam: 'id',
  },
};

/**
 * Extracts the core information needed for logging from the request.
 * requisitionId should never change otherwise it would break the entire hook & logging
 * @param {object} request - The Fastify request object.
 * @param {object} diScope - The dependency injection scope.
 * @returns {object|null} An object with { rsId, level, userId, username } or null if essential info is missing.
 */
async function extractCoreInfo(request, diScope) {
  const { params, body, userFromToken, url } = request;

  const urlParts = url.match(/\/v1\/([\w-]+)/i);
  const level = urlParts ? urlParts[1] : 'unknown';

  if (level === 'unknown') return null;

  let rsId = params?.requisitionId || body?.requisitionId;

  if (!rsId && ID_FETCH_CONFIG[level]) {
    try {
      const config = ID_FETCH_CONFIG[level];
      const entityId = params[config.idParam];

      if (entityId) {
        const repository = await diScope.resolve(config.repository);
        const entity = await repository.getById(entityId, {
          attributes: ['requisitionId'], // extend if need status
        });
        if (entity) rsId = entity.requisitionId;
      }
    } catch (dbError) {
      const fastify = await diScope.resolve('fastify');
      fastify.log.error(
        dbError,
        `Failed to fetch rsId from database for level: ${level}`,
      );
      return null;
    }
  }

  if (!rsId) return null;

  return {
    rsId: Number(rsId),
    level,
    userId: userFromToken.id,
    username: userFromToken.username,
  };
}

const transactionLogs = async function (request, _reply, response) {
  try {
    const transactionLogService = await this.diScope.resolve(
      'transactionLogService',
    );
    const constants = await this.diScope.resolve('constants');
    const fastify = await this.diScope.resolve('fastify');

    if (shouldSkipLog(request, constants)) {
      return;
    }

    const coreInfo = await extractCoreInfo(request, this.diScope);
    if (!coreInfo) {
      fastify.log.debug(
        `Skipping transaction log, no rsId found for URL: ${request.url}`,
      );
      return;
    }

    let parsedResponse;
    try {
      parsedResponse = JSON.parse(response);
    } catch (e) {
      fastify.log.error(
        { url: request.url, response },
        'Failed to parse JSON response in transaction logger.',
      );
      parsedResponse = { message: 'Response was not valid JSON.' };
    }

    const { userId, username, level, rsId } = coreInfo;
    const { params, ip, query, body } = request;

    const metadata = {
      version: '1.0',
      username,
      actionType: request.method,
      url: request.url,
      ip,
      params,
      query,
      // body, // Avoid for now because it might be too verbose
    };

    switch (request.method) {
      case 'POST':
        metadata.response = parsedResponse;
        break;

      case 'PUT':
        metadata.oldData = parsedResponse?.previousValue
          ? pick(parsedResponse.previousValue, Object.keys(request.body))
          : {};
        metadata.newData = parsedResponse?.newValue || request.body;

        if (parsedResponse?.status === 'failed') {
          metadata.error = `Transaction failed: ${parsedResponse?.message}`;
        }
        break;

      case 'DELETE':
        metadata.response = parsedResponse;
        break;
    }

    const payload = {
      userId,
      rsId,
      level,
      message: `${metadata.actionType}: ${level} - ${parsedResponse?.message || parsedResponse?.description || 'Action presumed successful.'}`, // extend the list of message sources into a formal function or data structure.
      metadata,
    };

    fastify.log.info('[Transaction Log Hook] Details for log creation.', {
      payload,
    });

    await transactionLogService.createLog(payload);
  } catch (error) {
    try {
      const fastify = await this.diScope.resolve('fastify');
      fastify.log.error(
        { err: error, requestUrl: request.url },
        '[Transaction Log Hook] CRITICAL: Failed to create transaction log.',
      );
    } catch (finalError) {
      console.error(
        '[Transaction Log Hook] CRITICAL: Could not even resolve fastify to log the error.',
        finalError,
        error,
      );
    }
  }
};

module.exports = transactionLogs;
