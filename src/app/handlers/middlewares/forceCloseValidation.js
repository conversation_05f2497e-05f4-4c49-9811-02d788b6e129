/**
 * Force Close Validation Middleware
 *
 * Prevents actions on requisitions that have been force closed or are in the process of being force closed.
 * This middleware should be applied to routes that modify requisitions, canvass sheets, or related documents.
 */

const forceCloseValidation = (options = {}) => {
  const {
    checkRequisition = true,
    checkCanvass = false,
    allowedActions = [],
    paramName = 'requisitionId',
  } = options;

  return async function (request, _reply) {
    const clientErrors = this.diScope.resolve('clientErrors');
    const requisitionRepository = this.diScope.resolve('requisitionRepository');
    const canvassRequisitionRepository = this.diScope.resolve(
      'canvassRequisitionRepository',
    );
    const { REQUISITION_STATUS } =
      this.diScope.resolve('constants').requisition;
    const { CANVASS_STATUS } = this.diScope.resolve('constants').canvass;

    let requisitionId = null;

    try {
      // Extract requisition ID from different sources
      if (request.params[paramName]) {
        requisitionId = request.params[paramName];
      } else if (request.body?.requisitionId) {
        requisitionId = request.body.requisitionId;
      } else if (checkCanvass && request.params.canvassId) {
        // Get requisition ID from canvass
        const canvass = await canvassRequisitionRepository.findOne({
          where: { id: request.params.canvassId },
          attributes: ['requisitionId'],
        });
        requisitionId = canvass?.requisitionId;
      }

      if (!requisitionId) {
        // If we can't find a requisition ID, let the request proceed
        // The actual endpoint will handle validation
        return;
      }

      // Check if requisition is force closed
      if (checkRequisition) {
        const requisition = await requisitionRepository.findOne({
          where: { id: requisitionId },
          attributes: ['id', 'status', 'forceClosedAt', 'forceClosedBy'],
        });

        if (!requisition) {
          throw clientErrors.NOT_FOUND({
            message: 'Requisition not found',
          });
        }

        // Check if requisition is force closed
        if (
          requisition.status === REQUISITION_STATUS.CLOSED ||
          requisition.forceClosedAt
        ) {
          // Check if this action is allowed for force closed requisitions
          const currentRoute = request.routeOptions?.config?.url || request.url;
          const isAllowedAction = allowedActions.some(
            (action) =>
              currentRoute.includes(action) || request.method === action,
          );

          if (!isAllowedAction) {
            throw clientErrors.BAD_REQUEST({
              message:
                'Cannot perform this action. Requisition has been force closed.',
              details: {
                requisitionId,
                status: requisition.status,
                forceClosedAt: requisition.forceClosedAt,
                action: 'FORCE_CLOSE_BLOCKED',
              },
            });
          }
        }
      }

      // Check canvass-specific validations
      if (checkCanvass && request.params.canvassId) {
        const canvass = await canvassRequisitionRepository.findOne({
          where: { id: request.params.canvassId },
          attributes: ['id', 'status', 'requisitionId'],
          include: [
            {
              association: 'requisition',
              attributes: ['id', 'status', 'forceClosedAt'],
            },
          ],
        });

        if (!canvass) {
          throw clientErrors.NOT_FOUND({
            message: 'Canvass not found',
          });
        }

        // Check if canvass is cancelled due to force close
        if (canvass.status === CANVASS_STATUS.CS_CANCELLED) {
          throw clientErrors.BAD_REQUEST({
            message:
              'Cannot perform this action. Canvass has been cancelled due to force close.',
            details: {
              canvassId: request.params.canvassId,
              requisitionId: canvass.requisitionId,
              status: canvass.status,
              action: 'CANVASS_FORCE_CLOSE_BLOCKED',
            },
          });
        }

        // Check if parent requisition is force closed
        if (
          canvass.requisition?.status === REQUISITION_STATUS.CLOSED ||
          canvass.requisition?.forceClosedAt
        ) {
          throw clientErrors.BAD_REQUEST({
            message:
              'Cannot perform this action. Parent requisition has been force closed.',
            details: {
              canvassId: request.params.canvassId,
              requisitionId: canvass.requisitionId,
              requisitionStatus: canvass.requisition.status,
              action: 'PARENT_RS_FORCE_CLOSE_BLOCKED',
            },
          });
        }
      }
    } catch (error) {
      // If it's already a client error, re-throw it
      if (
        error.statusCode &&
        error.statusCode >= 400 &&
        error.statusCode < 500
      ) {
        throw error;
      }

      // Log unexpected errors but don't block the request
      this.log.error(`Force close validation error: ${error.message}`, {
        requisitionId,
        route: request.routeOptions?.config?.url,
        method: request.method,
        error: error.message,
      });

      // Let the request proceed if there's an unexpected error
      // The actual endpoint will handle proper validation
    }
  };
};

module.exports = forceCloseValidation;
