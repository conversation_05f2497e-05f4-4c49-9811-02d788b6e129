const auditLogs = async function (request, _reply, response) {
  const auditLogService = await this.diScope.resolve('auditLogService');
  const fastify = await this.diScope.resolve('fastify');
  const allowedRoutes = [
    '/v1/requisitions',
    '/v1/requisitions/submit',
    '/v1/requisitions/:requisitionId',
    '/v1/requisitions/:requisitionId/items/:itemId',
  ];
  const skipRoutes = ['/v1/download/dashboard'];
  const { params } = request;

  if (
    request.method === 'GET' ||
    !allowedRoutes.includes(request.routeOptions.config.url) ||
    skipRoutes.includes(request.routeOptions.config.url)
  ) {
    return;
  }

  const parseResponse = await JSON.parse(response);

  let userDetails = {};

  if (request.userFromToken) {
    const {
      id,
      username,
      email,
      firstName,
      lastName,
      role,
      department,
      supervisor,
    } = request.userFromToken;

    userDetails = {
      userId: id,
      username,
      role: role?.name,
      email,
      department,
      supervisor,
      firstName,
      lastName,
    };
  }

  const pick = (obj, arr) => {
    if (!arr) {
      return;
    }
    return arr.reduce(
      (acc, record) => (record in obj && (acc[record] = obj[record]), acc),
      {},
    );
  };

  let payload = {};

  payload.module = request.url.match(/\/v1\/(\w+)/i)[1];

  payload.actionType = request.method;

  if (request.method === 'POST') {
    payload.description = `Create Data in ${payload.module}`;
    payload.metadata = {
      userDetails,
      insert: parseResponse,
      url: request.url,
    };
  }

  if (request.method === 'PUT') {
    payload.description =
      params.id || params.requisitionId
        ? //Since large factoring added or only
          //add other ids if different name in params
          `Edit Data in ${payload.module} with ID of ${params.id || params.requisitionId}`
        : `Edit Data in ${payload.module}`;

    let old;
    let keys;

    if (request.body) {
      keys = Object.keys(request.body) || null;
    }

    if (Array.isArray(parseResponse.previousValue) || parseResponse.newValue) {
      old = parseResponse.previousValue;
    }
    if (parseResponse.status) {
      old = `Transaction failed`;
      fastify.log.error(`Transaction Failed`);
    }
    if (parseResponse.previousValue === undefined) {
      old = {};
    } else {
      old = pick(parseResponse.previousValue, keys);
    }

    payload.metadata = {
      userDetails,
      oldData: old,
      newData: parseResponse.newValue ? parseResponse.newValue : request.body,
      url: request.url,
    };
  }

  if (request.method === 'DELETE') {
    payload.description = `Delete Data in ${payload.module}`;
    payload.metadata = {
      userDetails,
      delete: parseResponse,
      url: request.url,
    };
  }

  await auditLogService.createAuditLog(payload);
};

module.exports = auditLogs;
