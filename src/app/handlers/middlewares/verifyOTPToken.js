const verifyOTPToken = async function (request, _reply) {
  const userRepository = this.diScope.resolve('userRepository');
  const clientErrors = this.diScope.resolve('clientErrors');
  const authErrorMsg =
    'Invalid authorization token. Please provide a valid token';

  try {
    const { id, isForOTP, isForTempPass, isForRefresh } =
      await request.jwtVerify();

    const isInvalidTokenPayload =
      !id || !isForOTP || isForTempPass || isForRefresh;

    if (isInvalidTokenPayload) {
      throw new Error(authErrorMsg);
    }

    /* Get user via PK query -> without paranoid option (will disregard inactive users) */
    const userFromToken = await userRepository.getById(id);

    request.userFromToken = userFromToken;
  } catch (error) {
    this.log.error(
      `[VerifyOTPToken Error]: User ID:${request.body?.id} Error:${error?.message}`,
    );

    throw clientErrors.UNAUTHORIZED({
      message: authErrorMsg,
    });
  }
};

module.exports = verifyOTPToken;
