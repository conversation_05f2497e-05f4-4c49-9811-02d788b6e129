const uploadLimit = (options) => {
  const { route } = options;

  return async function (request, _reply) {
    const clientErrors = this.diScope.resolve('clientErrors');
    const attachmentRepository = this.diScope.resolve('attachmentRepository');
    const { UPLOAD_LIMIT } = this.diScope.resolve('constants').upload;

    const maxFileLimit = UPLOAD_LIMIT.MAX_UPLOAD_COUNT;
    const attachments = await attachmentRepository.getAttachmentByModelId({
      id: request.params.id,
      model: route,
    });

    if (!attachments) {
      throw clientErrors.BAD_REQUEST({
        message: 'Unable to upload Attachments',
      });
    }

    request.remainingFileLimit = maxFileLimit - attachments.total;
  };
};
module.exports = uploadLimit;
