const getClientIP = (request) => {
  return (
    request.headers['x-forwarded-for'] ||
    request.headers['x-real-ip'] ||
    request.connection?.remoteAddress ||
    request.socket?.remoteAddress ||
    request.ip ||
    'unknown'
  );
};

const sanitizeHeaders = (headers) => {
  const sanitized = { ...headers };
  // Remove sensitive headers
  delete sanitized.authorization;
  delete sanitized.cookie;
  delete sanitized['x-api-key'];
  delete sanitized['x-csrf-token'];
  return sanitized;
};

const sanitizeBody = (body) => {
  if (!body || typeof body !== 'object') return body;

  const sanitized = { ...body };
  // Remove sensitive fields
  const sensitiveFields = ['password', 'token', 'secret', 'key', 'otp'];

  Object.keys(sanitized).forEach((key) => {
    if (sensitiveFields.some((field) => key.toLowerCase().includes(field))) {
      sanitized[key] = '[REDACTED]';
    }
  });

  return sanitized;
};

const shouldLogRoute = (url, method) => {
  // Skip health checks and static files
  const skipRoutes = [
    '/health',
    '/metrics',
    '/status',
    '/upload/',
    '/favicon.ico',
  ];

  return !skipRoutes.some((route) => url.includes(route));
};

const requestResponseLogger = async function (request, reply) {
  const startTime = Date.now();

  // Skip logging for certain routes
  if (!shouldLogRoute(request.url, request.method)) {
    return;
  }

  const requestLog = {
    level: 'info',
    type: 'REQUEST',
    requestId: request.id,
    timestamp: new Date().toISOString(),
    method: request.method,
    url: request.url,
    userAgent: request.headers['user-agent'],
    clientIP: getClientIP(request),
    headers: sanitizeHeaders(request.headers),
    query: request.query,
    params: request.params,
    body: request.method !== 'GET' ? sanitizeBody(request.body) : undefined,
    user: request.userFromToken
      ? {
          id: request.userFromToken.id,
          username: request.userFromToken.username,
          role: request.userFromToken.role?.name,
        }
      : null,
    environment: process.env.NODE_ENV,
    service: 'prs-backend',
  };

  // Log the request
  request.log.info(requestLog);

  // Store start time for response logging
  request.startTime = startTime;
};

module.exports = requestResponseLogger;
