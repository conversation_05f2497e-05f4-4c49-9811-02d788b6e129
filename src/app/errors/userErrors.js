const HttpError = require('./httpError');

const FORBIDDEN = (payload = {}) => {
  const { message = 'Forbidden', description } = payload;

  return new HttpError({
    status: 403,
    message,
    description,
    errorCode: 'FORBIDDEN',
  });
};

const USER_NOT_FOUND = (payload = {}) => {
  const { message = 'User not found', description } = payload;

  return new HttpError({
    status: 404,
    message,
    description,
    errorCode: 'USER_NOT_FOUND',
  });
};

const EMAIL_ALREADY_USED = (payload = {}) => {
  const { message = 'Email already used', description } = payload;

  return new HttpError({
    status: 409,
    message,
    description,
    errorCode: 'EMAIL_ALREADY_USED',
  });
};

const USERNAME_ALREADY_USED = (payload = {}) => {
  const { message = 'Username already used', description } = payload;

  return new HttpError({
    status: 409,
    message,
    description,
    errorCode: 'USERNAME_ALREADY_USED',
  });
};

module.exports = {
  FORBIDDEN,
  USER_NOT_FOUND,
  EMAIL_ALREADY_USED,
  USERNAME_ALREADY_USED,
};
