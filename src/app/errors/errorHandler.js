const HttpError = require('./httpError');
const { Sequelize } = require('sequelize');
const serverErrors = require('./serverErrors');

const replyHttpError = (httpError, reply) => {
  const statusCode = httpError.status;
  return reply.status(statusCode).send(httpError);
};

const getClientIP = (request) => {
  return (
    request.headers['x-forwarded-for'] ||
    request.headers['x-real-ip'] ||
    request.connection?.remoteAddress ||
    request.socket?.remoteAddress ||
    request.ip ||
    'unknown'
  );
};

const sanitizeHeaders = (headers) => {
  const sanitized = { ...headers };
  // Remove sensitive headers
  delete sanitized.authorization;
  delete sanitized.cookie;
  delete sanitized['x-api-key'];
  return sanitized;
};

const errorhandler = function (error, _request, reply) {
  const errorType =
    error instanceof Sequelize.Error
      ? 'DATABASE_ERROR'
      : error?.errorCode || 'INTERNAL_SERVER_ERROR';

  // Enhanced error logging with more context
  const errorLog = {
    level: 'error',
    errorType,
    requestId: _request.id,
    message: error?.message || 'Unknown error occurred',
    stack: error?.stack,
    timestamp: new Date().toISOString(),
    request: {
      method: _request.method,
      url: _request.url,
      userAgent: _request.headers['user-agent'],
      clientIP: getClientIP(_request),
      headers: sanitizeHeaders(_request.headers),
      query: _request.query,
      params: _request.params,
    },
    user: _request.userFromToken
      ? {
          id: _request.userFromToken.id,
          username: _request.userFromToken.username,
          role: _request.userFromToken.role?.name,
        }
      : null,
    environment: process.env.NODE_ENV,
    service: 'prs-backend',
  };

  // Add database-specific error details
  if (error instanceof Sequelize.Error) {
    errorLog.database = {
      errorName: error.name,
      sql: error.sql,
      parameters: error.parameters,
      constraint: error.constraint,
      table: error.table,
      column: error.column,
    };
  }

  // Log the comprehensive error
  this.log.error(errorLog);

  // Also log to console in production for immediate visibility
  if (process.env.NODE_ENV === 'production') {
    console.error('🚨 PRODUCTION ERROR:', JSON.stringify(errorLog, null, 2));
  }

  if (error instanceof HttpError) {
    return replyHttpError(error, reply);
  }

  return replyHttpError(serverErrors.INTERNAL_SERVER_ERROR(), reply);
};

module.exports = errorhandler;
