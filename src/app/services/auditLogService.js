class AuditLogService {
  constructor(container) {
    const { auditLogRepository, clientErrors, fastify } = container;
    this.auditLogRepository = auditLogRepository;
    this.clientErrors = clientErrors;
    this.fastify = fastify;
  }

  async getAllAuditLogs(options) {
    try {
      return await this.auditLogRepository.getAllAuditLogs(options);
    } catch (error) {
      console.error('Error in getAllAuditLogs:', error);
      throw error;
    }
  }

  async getAuditLogById(id) {
    try {
      const auditLog = await this.auditLogRepository.getAuditLogDetailsById(id);

      if (!auditLog) {
        throw this.clientErrors.NOT_FOUND({
          message: 'Audit Log not found',
        });
      }

      return auditLog;
    } catch (error) {
      console.error('Error in getAuditLogById:', error);
      throw error;
    }
  }

  async createAuditLog(data) {
    this.fastify.log.info(`Creating Audit Logs with ${data}`);
    try {
      return (
        await this.auditLogRepository.create(data),
        this.fastify.log.info(`Created Audit Logs with ${data}`)
      );
    } catch (error) {
      console.error('Error in createAuditLog:', error);
      this.fastify.log.info(`Failed Creating Audit Logs with ${data}`);
      throw error;
    }
  }
}

module.exports = AuditLogService;
