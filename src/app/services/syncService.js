class SyncService {
  constructor ({
    db,
    clientErrors,
    fastify,
    entities,
    utils,
    syncQueue,
  }) {
    this.db = db;
    this.clientErrors = clientErrors;
    this.fastify = fastify;
    this.entities = entities;
    this.utils = utils;
    this.syncQueue = syncQueue;
  }

  async queueSync({
    userFromToken,
    type,
    withTimestamp = true
  }) {
    try {
      this.fastify.log.info(
        `QUEUE_SYNC_DATA: ${JSON.stringify({ userFromToken, type })}`,
      );
      const validTypes = ['supplier', 'item'];

      if (!validTypes.includes(type)) {
        throw this.clientErrors.BAD_REQUEST({
          message: `Invalid sync type.`,
        });
      }

      await this.#customHandling(type);

      const jobId = withTimestamp ? `prs-sync-${type}-${Date.now()}` : `prs-sync-${type}`;
      this.fastify.log.info(`QUEUE_SYNC_CREATING_JOB: ${jobId}`);
      const job = await this.syncQueue.add(
        'prs-sync',
        { type, userFromToken },
        { jobId },
      );

      this.fastify.log.info(`QUEUE_SYNC_CREATING_JOB_DONE: ${jobId}`);

      return {
        message: `PRS data sync (${type}) has been queued`,
        jobId: job.id,
      };
    } catch (error) {

      if (error.message == 'SYNC_ALREADY_RUNNING') {
        throw this.clientErrors.BAD_REQUEST({
          message: 'Sync already running',
          error: error.message,
        });
      }

      throw this.clientErrors.BAD_REQUEST({
        message: 'Failed to trigger sync',
        error: error.message,
      });
    }
  }

  async getJob(jobId) {
    return this.syncQueue.getJob(jobId);
  }

  async #customHandling(type) {
    switch (type) {
      case "supplier":
      case "item":
        // Check for active jobs instead of any job with the same ID
        const activeJobs = await this.syncQueue.getActive();
        const runningJobsOfType = activeJobs.filter(job =>
          job.data.type === type && job.isActive()
        );

        // Allow multiple syncs but limit to prevent overwhelming the system
        const maxConcurrentSyncs = parseInt(process.env.MAX_CONCURRENT_SYNCS_PER_TYPE) || 2;

        if (runningJobsOfType.length >= maxConcurrentSyncs) {
          throw this.clientErrors.BAD_REQUEST({
            message: `Maximum ${maxConcurrentSyncs} concurrent ${type} syncs already running`,
          });
        }

        break;
      default:
        return true;
    }

    return true;
  }
}

module.exports = SyncService;
