class ApproverService {
  constructor(container) {
    const {
      approvalTypeRepository,
      clientErrors,
      requisitionApproverRepository,
      canvassApproverRepository,
      purchaseOrderApproverRepository,
      nonRequisitionApproverRepository,
      rsPaymentRequestApproverRepository,
      notificationService,
      constants,
      db,
      fastify,
      rsPaymentRequestRepository,
      nonRequisitionRepository,
      purchaseOrderRepository,
      canvassRequisitionRepository,
      requisitionRepository,
    } = container;
    this.clientErrors = clientErrors;
    this.approvalTypeRepository = approvalTypeRepository;
    this.requisitionApproverRepository = requisitionApproverRepository;
    this.canvassApproverRepository = canvassApproverRepository;
    this.purchaseOrderApproverRepository = purchaseOrderApproverRepository;
    this.nonRequisitionApproverRepository = nonRequisitionApproverRepository;
    this.rsPaymentRequestApproverRepository =
      rsPaymentRequestApproverRepository;
    this.notificationService = notificationService;
    this.constants = constants;
    this.db = db;
    this.fastify = fastify;
    this.rsPaymentRequestRepository = rsPaymentRequestRepository;
    this.nonRequisitionRepository = nonRequisitionRepository;
    this.purchaseOrderRepository = purchaseOrderRepository;
    this.canvassRequisitionRepository = canvassRequisitionRepository;
    this.requisitionRepository = requisitionRepository;
  }

  async getLowestLevelApproverObject(approvers) {
    if (!approvers || approvers.length === 0) {
      return null;
    }

    const getEffectiveLevel = (approver) => {
      if ('approverLevel' in approver) {
        return approver.approverLevel;
      } else if ('level' in approver) {
        return approver.level;
      }

      console.warn(
        "Approver object has neither 'approver_level' nor 'level' property:",
        approver,
      );
      return Infinity;
    };

    let lowestLevelApprover = approvers[0];
    let lowestEffectiveLevel = getEffectiveLevel(approvers[0]);

    for (let i = 1; i < approvers.length; i++) {
      const currentApprover = approvers[i];
      const currentEffectiveLevel = getEffectiveLevel(currentApprover);

      if (currentEffectiveLevel < lowestEffectiveLevel) {
        lowestEffectiveLevel = currentEffectiveLevel;
        lowestLevelApprover = currentApprover;
      }
    }

    return lowestLevelApprover;
  }

  async overrideApprover({
    model,
    modelId,
    approverId,
    status,
    transaction,
    requisitionId,
  }) {
    this.fastify.log.info(
      `Processing ${status} override for ${model} ${modelId} by approver ${approverId}`,
    );

    // Find current approver and approvers to override
    const currentApprover = await this._findCurrentApprover(
      model,
      modelId,
      approverId,
    );
    const approversToOverride = await this._findApproversToOverride(
      model,
      modelId,
      currentApprover,
      approverId,
    );

    if (approversToOverride.length === 0) {
      this.fastify.log.info(`No approvers to override for ${model} ${modelId}`);
    } else {
      // Process overrides with proper status (approved/rejected)
      await this._processOverrides({
        approversToOverride,
        currentApprover,
        approverId,
        model,
        status,
        modelId,
        requisitionId,
        transaction,
      });
    }

    // Handle adhoc approver auto-approval of primary approver
    await this._handleAdhocApproverAutoApproval({
      currentApprover,
      model,
      modelId,
      status,
      transaction,
    });

    // Handle adhoc approver auto-rejection of primary approver
    await this._handleAdhocApproverAutoRejection({
      currentApprover,
      model,
      modelId,
      status,
      transaction,
    });

    // Notify adhoc approver creator if applicable
    await this._notifyAdhocApproverCreator({
      currentApprover,
      approverId,
      model,
      modelId,
      requisitionId,
      transaction,
    });
  }

  // Helper method to fetch approvers with includes
  async _fetchApprovers(model, modelId) {
    const { APPROVER } = this.constants.approver;
    const order =
      model === 'requisition'
        ? [['approver_level', 'ASC']]
        : [['level', 'ASC']];

    this.fastify.log.info(`Fetching of approvers for ${model}`);

    const { data: approvers } = await this[
      APPROVER.MODEL_APPROVER[model]
    ].findAll({
      where: APPROVER.WHERE_MODEL_ID_APPROVER[model](modelId),
      order,
      include: [
        {
          model: this.db.userModel,
          attributes: ['id', 'firstName', 'lastName', 'username'],
          as: 'altApprover',
          include: [
            { model: this.db.roleModel, attributes: ['name'], as: 'role' },
          ],
        },
        {
          model: this.db.userModel,
          attributes: ['id', 'firstName', 'lastName', 'username'],
          as: 'approver',
          include: [
            { model: this.db.roleModel, attributes: ['name'], as: 'role' },
          ],
        },
      ],
    });

    return approvers;
  }

  // Helper method to find current approver taking action
  async _findCurrentApprover(model, modelId, approverId) {
    const approvers = await this._fetchApprovers(model, modelId);
    return this._getCurrentApprover(approvers, approverId, model);
  }

  // Helper method to get the current approver from the list
  _getCurrentApprover(approvers, approverId, model) {
    return approvers.find((approver) => {
      const isApprover =
        approver.userId === approverId ||
        approver.altApproverId === approverId ||
        approver.approverId === approverId;

      const isPending = approver.status === 'pending';

      return isApprover && isPending;
    });
  }

  // Helper method to find all approvers that need to be overridden
  async _findApproversToOverride(model, modelId, currentApprover, approverId) {
    const approvers = await this._fetchApprovers(model, modelId);
    return this._getApproversToOverride(
      approvers,
      currentApprover,
      model,
      approverId,
    );
  }

  // Helper method to find all approvers that need to be overridden (renamed from existing method)
  _getApproversToOverride(approvers, currentApprover, model, approverId) {
    const currentLevel =
      model === 'requisition'
        ? currentApprover?.approverLevel
        : currentApprover?.level;

    return approvers.filter((approver) => {
      const approverLevel =
        model === 'requisition' ? approver.approverLevel : approver.level;

      // Find pending approvers at higher priority levels (lower numbers)
      const isHigherPriority = currentApprover?.isAdhoc
        ? approverLevel == currentLevel
        : approverLevel < currentLevel;
      const isPending = approver.status === 'pending';
      const isNotTheActingApprover =
        approver.userId !== approverId &&
        approver.altApproverId !== approverId &&
        approver.approverId !== approverId;

      return isHigherPriority && isPending && isNotTheActingApprover;
    });
  }

  // Helper method to process all overrides and send notifications
  async _processOverrides({
    approversToOverride,
    currentApprover,
    approverId,
    model,
    status,
    modelId,
    requisitionId,
    transaction,
  }) {
    const { APPROVER } = this.constants.approver;

    // Build override metadata once - works for both approval and rejection
    const overrideBy = {
      level: currentApprover?.isAdhoc
        ? '*'
        : model === 'requisition'
          ? currentApprover.approverLevel
          : currentApprover.level,
      firstName:
        currentApprover.altApprover?.firstName ||
        currentApprover.approver?.firstName,
      lastName:
        currentApprover.altApprover?.lastName ||
        currentApprover.approver?.lastName,
      username:
        currentApprover.altApprover?.username ||
        currentApprover.approver?.username,
      role:
        currentApprover.altApprover?.role?.name ||
        currentApprover.approver?.role?.name,
      approverId,
      isAdhoc:
        currentApprover?.isAdditionalApprover || currentApprover?.isAdhoc,
      status, // Include the status (approved/rejected) in override metadata
    };

    // Process each approver to override
    for (const approverToOverride of approversToOverride) {
      // Skip if same approver
      if (
        approverToOverride?.userId
          ? approverToOverride?.userId === currentApprover?.userId
          : approverToOverride?.approverId === currentApprover?.approverId
      ) {
        this.fastify.log.info(`Same approver, no need to override`);
        continue;
      }

      // Update approver status to overridden (approved/rejected)
      this.fastify.log.info(
        `Updating approver ${approverToOverride.id} for ${model} with status: ${status}`,
      );

      await this[APPROVER.MODEL_APPROVER[model]].update(
        { id: approverToOverride.id },
        { status, overrideBy },
        { transaction },
      );

      // Send notification to the overridden approver
      await this._notifyOverriddenApprover({
        approverToOverride,
        approverId,
        model,
        modelId,
        requisitionId,
        transaction,
      });
    }
  }

  // Helper method to notify adhoc approver creator
  async _notifyAdhocApproverCreator({
    currentApprover,
    approverId,
    model,
    modelId,
    requisitionId,
    transaction,
  }) {
    if (
      currentApprover &&
      currentApprover.isAdhoc &&
      currentApprover.addedBy &&
      currentApprover.addedBy !== approverId
    ) {
      this.fastify.log.info(
        `Sending notification to user who added adhoc approver: ${currentApprover.addedBy}`,
      );

      const metaData = await this._buildMetaData(
        model,
        modelId,
        approverId,
        requisitionId,
      );
      const { NOTIFICATION_DETAILS } = this.constants.notification;
      const { APPROVER } = this.constants.approver;

      await this.notificationService.sendNotification(
        {
          senderId: approverId,
          type: APPROVER.NOTIFICATION_TYPES[model],
          title: NOTIFICATION_DETAILS.APPROVER.title(model),
          message: NOTIFICATION_DETAILS.APPROVER.message(model),
          recipientUserIds: [currentApprover.addedBy],
          metaData,
        },
        { transaction },
      );
    }
  }

  // Helper method to notify overridden approver
  async _notifyOverriddenApprover({
    approverToOverride,
    approverId,
    model,
    modelId,
    requisitionId,
    transaction,
  }) {
    // Get the correct recipient - prioritize the actual approver over alt approver
    const recipientId =
      approverToOverride.userId ||
      approverToOverride.approverId ||
      approverToOverride.altApproverId;

    this.fastify.log.info(
      `Sending notification for overridden approver ${recipientId}`,
    );

    const metaData = await this._buildMetaData(
      model,
      modelId,
      approverId,
      requisitionId,
    );
    const { NOTIFICATION_DETAILS } = this.constants.notification;
    const { APPROVER } = this.constants.approver;

    await this.notificationService.sendNotification(
      {
        senderId: approverId,
        type: APPROVER.NOTIFICATION_TYPES[model],
        title: NOTIFICATION_DETAILS.APPROVER.title(model),
        message: NOTIFICATION_DETAILS.APPROVER.message(model),
        recipientUserIds: [recipientId],
        metaData,
      },
      { transaction },
    );
  }

  // Helper method to build metadata for notifications
  async _buildMetaData(model, modelId, approverId, requisitionId) {
    let metaData = { approverId };

    const models = ['canvass', 'purchaseOrder'];

    if (model === 'nonRequisition') {
      metaData.nonRsId = modelId;
    } else {
      metaData[`${model}Id`] = modelId;
    }

    // For rsPaymentRequest, ensure both paymentRequestId and requisitionId are included
    if (model === 'rsPaymentRequest') {
      metaData.paymentRequestId = modelId;

      // If requisitionId not provided, fetch it from the payment request
      if (!requisitionId) {
        const paymentRequest = await this.rsPaymentRequestRepository.findOne({
          where: { id: modelId },
          attributes: ['requisitionId'],
        });
        if (paymentRequest?.requisitionId) {
          metaData.requisitionId = paymentRequest.requisitionId;
        }
      } else {
        metaData.requisitionId = requisitionId;
      }
    }

    // For canvass, always include requisitionId
    if (models.includes(model) && requisitionId) {
      metaData.requisitionId = requisitionId;
    }

    return metaData;
  }

  // Helper method to handle adhoc approver auto-approval of primary approver
  async _handleAdhocApproverAutoApproval({
    currentApprover,
    model,
    modelId,
    status,
    transaction,
  }) {
    if (
      currentApprover?.isAdhoc &&
      currentApprover?.addedBy &&
      status === 'approved'
    ) {
      const { APPROVER } = this.constants.approver;

      // Find the primary approver who added this adhoc approver
      const { data: approvers } = await this[
        APPROVER.MODEL_APPROVER[model]
      ].findAll({
        where: {
          ...APPROVER.WHERE_MODEL_ID_APPROVER[model](modelId),
          userId: currentApprover.addedBy,
          level: currentApprover.level,
          isAdhoc: false,
          status: 'pending',
        },
        transaction,
      });

      if (approvers.length > 0) {
        const primaryApprover = approvers[0];
        this.fastify.log.info(
          `Auto-approving primary approver ${primaryApprover.id} since adhoc approver approved`,
        );

        await this[APPROVER.MODEL_APPROVER[model]].update(
          { id: primaryApprover.id },
          { status: 'approved' },
          { transaction },
        );
      }
    }
  }

  // Helper method to handle adhoc approver auto-rejection of primary approver
  async _handleAdhocApproverAutoRejection({
    currentApprover,
    model,
    modelId,
    status,
    transaction,
  }) {
    if (
      currentApprover?.isAdhoc &&
      currentApprover?.addedBy &&
      status === 'rejected'
    ) {
      const { APPROVER } = this.constants.approver;

      // Find the primary approver who added this adhoc approver
      const { data: approvers } = await this[
        APPROVER.MODEL_APPROVER[model]
      ].findAll({
        where: {
          ...APPROVER.WHERE_MODEL_ID_APPROVER[model](modelId),
          userId: currentApprover.addedBy,
          level: currentApprover.level,
          isAdhoc: false,
          status: 'pending',
        },
        transaction,
      });

      if (approvers.length > 0) {
        const primaryApprover = approvers[0];
        this.fastify.log.info(
          `Auto-rejecting primary approver ${primaryApprover.id} since adhoc approver rejected`,
        );

        await this[APPROVER.MODEL_APPROVER[model]].update(
          { id: primaryApprover.id },
          { status: 'rejected' },
          { transaction },
        );
      }
    }
  }

  async resubmitApprover(payload = {}) {
    const { model, modelId, userId } = payload;

    const { APPROVER } = this.constants.approver;
    const { NOTIFICATION_DETAILS } = this.constants.notification;

    this.fastify.log.info(`Fetching of approvers for ${model}`);
    const { data: approvers } = await this[
      APPROVER.MODEL_APPROVER[model]
    ].findAll({
      where: APPROVER.WHERE_MODEL_ID_APPROVER[model](modelId),
      order: [['level', 'ASC']],
    });

    const rejectedApprovers = approvers.filter((approver) => {
      return approver.status === 'rejected';
    });

    rejectedApprovers.forEach(async (rejectedApprover) => {
      await this[APPROVER.MODEL_APPROVER[model]].update(
        { id: rejectedApprover.id },
        { status: 'pending', overrideBy: null },
      );

      await this.notificationService.sendNotification({
        senderId: userId,

        type: APPROVER.NOTIFICATION_TYPES[model],

        title: NOTIFICATION_DETAILS.RESUBMIT_APPROVER.title(
          APPROVER.NOTIFICATION_FEATURE[model],
        ),

        message: NOTIFICATION_DETAILS.RESUBMIT_APPROVER.message(
          APPROVER.NOTIFICATION_FEATURE[model],
        ),

        recipientUserIds: [
          rejectedApprover.altApproverId ||
            rejectedApprover.approverId ||
            rejectedApprover.userId,
        ],

        metaData: {
          [`${model}Id`]: modelId,
        },
      });
    });

    let status;

    switch (model) {
      case 'requisition':
        status = 'for_rs_approval';
        break;
      default:
        status = 'for_approval';
    }

    await this[APPROVER.MODEL[model]].update(
      { id: modelId },
      {
        status,
      },
    );
  }
}

module.exports = ApproverService;
