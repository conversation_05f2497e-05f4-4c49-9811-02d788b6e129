class TradeService {
  constructor(container) {
    const { db, projectTradeRepository, clientErrors } = container;

    this.db = db;
    this.clientErrors = clientErrors;
    this.projectTradeRepository = projectTradeRepository;
  }

  async getAllEngineerProjectTrades(userId) {
    const projectTrade = await this.projectTradeRepository.findAll({
      attributes: ['tradeId', 'engineerId', 'projectId'],
      where: { engineerId: userId },
      paginate: false,
    });

    return projectTrade ? projectTrade : { data: [] };
  }
}

module.exports = TradeService;
