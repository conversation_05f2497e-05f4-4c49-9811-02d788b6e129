class WarrantyService {
  constructor(container) {
    const { db, warrantyRepository, clientErrors } = container;

    this.db = db;
    this.warrantyRepository = warrantyRepository;
    this.Sequelize = this.db.Sequelize;
    this.clientErrors = clientErrors;
  }

  async getWarranties(search) {
    let whereClause = {
      type: 'purchase_order',
    };

    if (search) {
      whereClause.name = { [this.Sequelize.Op.iLike]: `%${search}%` };
    }

    return this.warrantyRepository.findAll({
      paginate: false,
      where: whereClause,
      order: [['createdAt', 'DESC']],
    });
  }

  async createWarranty(body) {
    const { name, type } = body;

    if (!name || !type) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'Name and type are required',
      });
    }

    return this.warrantyRepository.create({
      name,
      type,
    });
  }
}

module.exports = WarrantyService;
