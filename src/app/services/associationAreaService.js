class AssociationAreaService {
  constructor(container) {
    const { associationAreaRepository, clientErrors } = container;
    this.clientErrors = clientErrors;
    this.associationAreaRepository = associationAreaRepository;
  }

  async getAllAreaNames(code, options = {}) {
    return this.associationAreaRepository.getAllAreaNames(code, options);
  }

  async getOneAreaName(code, options = {}) {
    return this.associationAreaRepository.getOneAreaName(code, options);
  }
}

module.exports = AssociationAreaService;
