const { convertToUSDateFormat, convertDateToDDMMMYYYY } = require('../utils');
const fs = require('fs');
const path = require('path');
const {
  attachment: ATTACHMENT,
  note: NOTE,
} = require('../../domain/constants');
const { note } = require('../../domain/entities');
const {
  DELIVERY_ITEM_STATUSES,
} = require('../../domain/constants/deliveryReceiptItemConstants');
const { STATUSES } = require('../../domain/constants/deliveryReceiptConstants');
const { isRegExp } = require('util/types');
class DeliveryReceiptService {
  constructor({
    attachmentService,
    deliveryReceiptRepository,
    deliveryReceiptItemRepository,
    deliveryReceiptItemHistoryRepository,
    requisitionDeliveryHistoryRepository,
    requisitionReturnHistoryRepository,
    noteRepository,
    requisitionService,
    deliveryReceiptItemService,
    db,
    clientErrors,
    constants,
  }) {
    this.attachmentService = attachmentService;
    this.deliveryReceiptRepository = deliveryReceiptRepository;
    this.deliveryReceiptItemRepository = deliveryReceiptItemRepository;
    this.deliveryReceiptItemHistoryRepository =
      deliveryReceiptItemHistoryRepository;
    this.requisitionDeliveryHistoryRepository =
      requisitionDeliveryHistoryRepository;
    this.requisitionReturnHistoryRepository =
      requisitionReturnHistoryRepository;
    this.noteRepository = noteRepository;
    this.requisitionService = requisitionService;
    this.deliveryReceiptItemService = deliveryReceiptItemService;
    this.db = db;
    this.Sequelize = db.Sequelize;
    this.clientErrors = clientErrors;
    this.constants = constants;
  }

  async createDeliveryReceipt(data, userFromToken, transaction) {
    const isUserCreatorOrAssignee =
      await this.requisitionService.isUserCreatorOrAssignee(
        data.requisitionId,
        userFromToken.id,
      );

    if (!isUserCreatorOrAssignee) {
      throw this.clientErrors.UNAUTHORIZED({
        message: 'You are not authorized to create a delivery receipt.',
      });
    }

    await this.deliveryReceiptItemService.validateQtyDelivered(data.items);

    // create delivery receipt
    const deliveryReceipt = await this.deliveryReceiptRepository.create(
      {
        ...data,
        attachmentIds: undefined,
      },
      {
        transaction,
        userId: userFromToken.id,
      },
    );

    // add attachments
    const { attachmentIds } = data;
    let files = [];
    if (attachmentIds) {
      files = await this.attachmentService.assignAttachmentsToModelId(
        attachmentIds,
        ATTACHMENT.MODELS.DELIVERY_RECEIPT,
        deliveryReceipt.id,
        userFromToken.id,
        transaction,
      );
    }

    await this.#addNoteToDeliveryReceipt(
      deliveryReceipt,
      userFromToken,
      data.isDraft === 'false',
      transaction,
    );

    const purchaseOrder = await this.db.sequelize
      .model('purchase_orders')
      .findOne({
        where: {
          id: data.poId,
        },
      });

    data.items.forEach(async (item) => {
      if (item.qtyReturned === 0) {
        await this.requisitionDeliveryHistoryRepository.create(
          {
            requisitionId: data.requisitionId,
            drNumber: deliveryReceipt.actualNumber,
            supplier: deliveryReceipt.supplier,
            dateOrdered: purchaseOrder.createdAt,
            quantityOrdered: item.qtyOrdered,
            quantityDelivered: item.qtyDelivered,
            dateDelivered: item.dateDelivered,
            status:
              item.qtyDelivered < item.qtyOrdered
                ? DELIVERY_ITEM_STATUSES.PARTIALLY_DELIVERED
                : DELIVERY_ITEM_STATUSES.FULLY_DELIVERED,
          },
          { transaction },
        );
      } else {
        await this.requisitionReturnHistoryRepository.create(
          {
            requisitionId: data.requisitionId,
            drNumber: deliveryReceipt.actualNumber,
            supplier: deliveryReceipt.supplier,
            item: item.itemDes,
            dateOrdered: purchaseOrder.createdAt,
            quantityOrdered: item.qtyOrdered,
            quantityReturned: item.qtyReturned,
            returnDate: item.dateDelivered,
            status:
              item.qtyReturned + item.qtyDelivered === item.qtyOrdered
                ? DELIVERY_ITEM_STATUSES.FULLY_DELIVERED_WITH_RETURNS
                : DELIVERY_ITEM_STATUSES.PARTIALLY_DELIVERED_WITH_RETURNS,
          },
          { transaction },
        );
      }
    });

    const result = this._formatDeliveryReceipt(
      deliveryReceipt,
      files?.length ? files[1] : [],
    );

    return result;
  }

  async getDeliveryReceiptById(id, type = null) {
    const deliveryReceipt =
      await this.deliveryReceiptRepository.getDeliveryReceiptById({ id, type });

    return this._formatDeliveryReceipt(
      deliveryReceipt,
      deliveryReceipt.attachments?.filter(
        (attachment) => attachment.model === ATTACHMENT.MODELS.DELIVERY_RECEIPT,
      ),
    );
  }

  async getDeliveryReceiptsFromRequisitionId(
    requisitionId,
    { search, page, limit, sortBy },
  ) {
    const deliveryReceipts =
      await this.deliveryReceiptRepository.getDeliveryReceiptsFromRequisitionId(
        requisitionId,
        { search, page, limit, sortBy },
      );

    return this._formatDeliveryReceiptForListing(deliveryReceipts);
  }

  async getDeliveryReturnsFromRequisitionId(
    requisitionId,
    { search, page, limit, sortBy },
  ) {
    const deliveryReturns = [];

    const deliveryReturnsResult =
      await this.deliveryReceiptRepository.getDeliveryReturnsFromRequisitionId(
        requisitionId,
        { search, page, limit, sortBy },
      );

    deliveryReturnsResult.data.forEach((dr) => {
      // Determine the latest delivery and returned dates for this delivery return
      const { latestDeliveryDate, latestReturnedDate } = dr.items.reduce(
        (latest, item) => {
          if (
            !latest.latestDeliveryDate ||
            new Date(item.dateDelivered) > new Date(latest.latestDeliveryDate)
          ) {
            latest.latestDeliveryDate = item.dateDelivered;
          }
          if (
            !latest.latestReturnedDate ||
            new Date(item.updatedAt) > new Date(latest.latestReturnedDate)
          ) {
            latest.latestReturnedDate = item.updatedAt;
          }
          return latest;
        },
        { latestDeliveryDate: null, latestReturnedDate: null },
      );

      // Find the latest approver for the purchase order
      const latestApprover = dr.purchaseOrders.purchaseOrderApprovers
        .sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt))
        .map(
          (approver) =>
            `${approver.approver.firstName} ${approver.approver.lastName}`,
        )[0];

      // Push the transformed data into the result array
      deliveryReturns.push({
        id: dr.id,
        drNumber: dr.drNumber,
        supplier: dr.supplier,
        latestDeliveryDate: convertDateToDDMMMYYYY(latestDeliveryDate),
        latestReturnedDate: convertDateToDDMMMYYYY(latestReturnedDate),
        latestApprover: latestApprover,
        latestDeliveryStatus: dr.latestDeliveryStatus,
        isDraft: dr.isDraft,
      });
    });

    return {
      data: deliveryReturns,
      total: deliveryReturnsResult.total,
    };
  }

  async getDeliveryReceiptsFromPurchaseOrderId(purchaseOrderId, options = {}) {
    const { noInvoice = false, attributes = [] } = options;

    const whereClause = {
      poId: purchaseOrderId,
      isDraft: false,
    };

    if (noInvoice) {
      whereClause.invoiceId = null;
    }

    const deliveryReceipts = await this.deliveryReceiptRepository.findAll({
      attributes: [
        'id',
        [
          this.db.Sequelize.fn(
            'CONCAT',
            'RR-',
            this.db.Sequelize.literal(
              "CASE WHEN \"delivery_receipts\".\"is_draft\" = 'true' THEN 'TMP-' ELSE '' END",
            ),
            this.db.Sequelize.fn(
              'COALESCE',
              this.db.Sequelize.col('dr_number'),
              this.db.Sequelize.col('draft_dr_number'),
            ),
          ),
          'drNumber',
        ],
        ...attributes,
      ],
      where: whereClause,
      paginate: false,
      order: [['id', 'ASC']],
    });

    return deliveryReceipts;
  }

  async getDeliveryReceiptItemHistory(itemId, { page, limit }) {
    const history =
      await this.deliveryReceiptItemHistoryRepository.getHistoryByDeliveryReceiptItemId(
        itemId,
        { page, limit },
      );
    if (history.count === 0) {
      throw this.clientErrors.NOT_FOUND({
        message: 'Delivery receipt item with id of ' + itemId + ' not found.',
      });
    }

    return {
      start: (page - 1) * limit + 1,
      end: Math.min(page * limit, history.count),
      total: history.count,
      rows: history.rows.map((row) => ({
        createdAt: row.createdAt,
        qtyOrdered: row.qtyOrdered,
        qtyDelivered: row.qtyDelivered,
        dateDelivered: convertToUSDateFormat(row.dateDelivered),
        status: row.status,
      })),
    };
  }

  async updateDeliveryReceiptById(id, data, userFromToken, transaction) {
    const deliveryReceipt = await this.deliveryReceiptRepository.getById(id, {
      include: [
        {
          association: 'items',
          required: true,
        },
        {
          association: 'invoiceReport',
          attributes: ['id'],
        },
      ],
    });

    if (!deliveryReceipt) {
      throw this.clientErrors.NOT_FOUND({
        message: 'Delivery receipt not found.',
      });
    }

    if (deliveryReceipt.invoiceReport) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'Cannot update delivery receipt with an invoice report.',
      });
    }

    const isUserCreatorOrAssignee =
      await this.requisitionService.isUserCreatorOrAssignee(
        data.requisitionId,
        userFromToken.id,
      );

    if (!isUserCreatorOrAssignee) {
      throw this.clientErrors.UNAUTHORIZED({
        message: 'You are not authorized to create a delivery receipt.',
      });
    }
    await this.#validateUpdateDeliveryReceipt(deliveryReceipt, data);

    // check if isDraft changed
    if (
      String(deliveryReceipt.isDraft) !== data.isDraft &&
      data.isDraft === 'false'
    ) {
      data.drNumber = await this.deliveryReceiptRepository.generateDRNumber(
        deliveryReceipt.companyCode,
        false,
        transaction,
      );
      data.status = STATUSES.DELIVERED;
    }

    // update the delivery receipt
    const updatedDeliveryReceipt = await this.deliveryReceiptRepository.update(
      deliveryReceipt,
      { ...data, attachmentIds: undefined },
      { transaction, userId: userFromToken.id },
    );

    // delete old items and create new items if poId changed for both receipt and items
    if (deliveryReceipt.poId !== Number(data.poId)) {
      await this.deliveryReceiptItemRepository.syncDeliveryReceiptItems(
        updatedDeliveryReceipt,
        data.items,
        { transaction },
      );
    } else if (data.items) {
      for (const updatedData of data.items) {
        const item = deliveryReceipt.items.find(
          (item) => item.id === Number(updatedData.id),
        );

        if (item) {
          await this.deliveryReceiptItemRepository.update(
            { id: item.id },
            {
              ...updatedData,
            },
            { transaction },
          );
        } else {
          await this.deliveryReceiptItemRepository.create(
            {
              ...updatedData,
              drId: updatedDeliveryReceipt.id,
            },
            { transaction },
          );
        }
      }
    }

    await this.#addNoteToDeliveryReceipt(
      {
        id: deliveryReceipt.id,
        note: data.note,
      },
      userFromToken,
      data.isDraft === 'false',
      transaction,
    );

    // update attachments
    await this._syncAttachments(
      deliveryReceipt,
      {
        deliveryReceipt: data.attachmentIds,
      },
      userFromToken,
      transaction,
    );

    return updatedDeliveryReceipt;
  }

  async _syncAttachments(
    deliveryReceipt,
    { deliveryReceipt: attachmentIds },
    userFromToken,
    transaction,
  ) {
    if (attachmentIds) {
      await this.attachmentService.assignAttachmentsToModelId(
        attachmentIds,
        ATTACHMENT.MODELS.DELIVERY_RECEIPT,
        deliveryReceipt.id,
        userFromToken.id,
        transaction,
      );
    }
  }

  _formatDeliveryReceiptForListing(deliveryReceipts) {
    return {
      data: deliveryReceipts.data?.map((deliveryReceipt) => ({
        id: deliveryReceipt.id,
        drNumber: deliveryReceipt.drNumber,
        supplier: deliveryReceipt.supplier,
        latestDeliveryDate: convertDateToDDMMMYYYY(
          deliveryReceipt.latestDeliveryDate,
        ),
        latestDeliveryStatus: deliveryReceipt.latestDeliveryStatus,
        isDraft: deliveryReceipt.isDraft,
        status: deliveryReceipt.status,
      })),
      total: deliveryReceipts.total,
    };
  }

  _formatDeliveryReceipt(deliveryReceipt, files = null) {
    return {
      id: deliveryReceipt.id,
      isDraft: deliveryReceipt.isDraft,
      status: deliveryReceipt.status,
      drNumber: deliveryReceipt.actualNumber,
      poId: deliveryReceipt.poId,
      poNumber: deliveryReceipt?.purchaseOrder?.poNumber,
      supplier: deliveryReceipt.supplier,
      updatedAt: deliveryReceipt.updatedAt,
      issuedDate: convertToUSDateFormat(deliveryReceipt.issuedDate),
      invoiceNumber: deliveryReceipt.invoiceNumber,
      supplierDeliveryIssuedDate: convertToUSDateFormat(
        deliveryReceipt.supplierDeliveryIssuedDate,
      ),
      note: deliveryReceipt.note,
      invoiceReport: deliveryReceipt.invoiceReport?.id || null,
      irNumber: deliveryReceipt.invoiceReport?.irNumber || null,
      attachments: files?.map((file) => ({
        id: file.id,
        model: file.model,
        modelId: file.modelId,
        userId: file.userId,
        fileName: file.fileName,
        path: file.path,
      })),
      items: deliveryReceipt.items.map((item) => ({
        ...(item.item?.acctCd && { accountCode: item.item.acctCd }),
        id: item.id,
        itemId: item.itemId,
        poId: item.poId,
        poItemId: item.poItemId,
        itemDes: item.itemDes,
        qtyOrdered: item.qtyOrdered,
        qtyDelivered: item.qtyDelivered,
        qtyReturned: item.qtyReturned,
        dateDelivered: convertToUSDateFormat(item.dateDelivered),
        unit: item.unit,
        deliveryStatus: item.deliveryStatus,
        notes: item.notes,
        hasReturns: item.hasReturns,
        unitPrice: item.purchaseOrderItem?.get('approvedPrice'),
      })),
      draftSavedAt: deliveryReceipt.isDraft ? deliveryReceipt.updated_at : null,
    };
  }

  async #validateUpdateDeliveryReceipt(deliveryReceipt, data) {
    if (deliveryReceipt.isDraft === 'false') {
      if (data.isDraft !== String(deliveryReceipt.isDraft)) {
        throw this.clientErrors.BAD_REQUEST({
          message: 'Cannot revert to delivery receipt as draft',
        });
      }
    }

    await this.deliveryReceiptItemService.validateQtyDelivered(data.items);

    if (
      deliveryReceipt.poId === Number(data.poId) &&
      data.items.every((item) => !item.id)
    ) {
      throw this.clientErrors.BAD_REQUEST({
        message:
          'For existing item record, all items must have an ID to be able to update.',
      });
    }
  }

  async #addNoteToDeliveryReceipt(
    deliveryReceipt,
    userFromToken,
    isFinalized,
    transaction,
  ) {
    if (deliveryReceipt.note !== '') {
      await this.noteRepository.create(
        {
          model: NOTE.MODELS.DELIVERY_RECEIPT,
          modelId: deliveryReceipt.id,
          userName: userFromToken.fullNameUser,
          userType: NOTE.USER_TYPES.REQUESTOR,
          commentType: NOTE.COMMENT_TYPES.NOTE,
          note: deliveryReceipt.note,
        },
        { transaction },
      );
    }
  }

  async getDeliveryReceiptByPOId(id) {
    const deliveryReceipt = await this.deliveryReceiptRepository.findOne({
      where: { poId: id },
    });

    return deliveryReceipt;
  }

  async getFullyDeliveredDRs(requisitionId) {
    const { DELIVERY_ITEM_STATUSES } = this.constants.deliveryReceiptItem;
    const results = await this.deliveryReceiptRepository.findAll({
      where: {
        requisitionId,
        latestDeliveryStatus: {
          [this.Sequelize.Op.in]: [
            DELIVERY_ITEM_STATUSES.PARTIALLY_DELIVERED,
            DELIVERY_ITEM_STATUSES.PARTIALLY_DELIVERED_WITH_RETURNS,
            DELIVERY_ITEM_STATUSES.FULLY_DELIVERED,
            DELIVERY_ITEM_STATUSES.FULLY_DELIVERED_WITH_RETURNS,
          ],
        },
      },
      order: [['id', 'ASC']],
    });

    return results.data.map((deliveryReceipt) => ({
      id: deliveryReceipt.id,
      drNumber: deliveryReceipt.actualNumber,
    }));
  }

  async getDeliveryReceiptItems(id, query) {
    const {
      limit,
      page,
      paginate,
      sortBy = JSON.stringify({ item: 'ASC' }),
    } = query;

    const formattedOrder = [];
    for (const [field, direction] of Object.entries(JSON.parse(sortBy))) {
      switch (field) {
        case 'item':
          formattedOrder.push(['itemDes', direction]);
          continue;
        case 'requestedQty':
          formattedOrder.push(['qtyOrdered', direction]);
          continue;
        case 'deliveredQty':
          formattedOrder.push(['qtyDelivered', direction]);
          continue;
        case 'returnedQty':
          formattedOrder.push(['qtyReturned', direction]);
          continue;
        case 'unit':
          formattedOrder.push(['unit', direction]);
          continue;
      }
    }

    const deliveryReceiptItems =
      await this.deliveryReceiptItemRepository.findAll({
        attributes: [
          'id',
          'itemDes',
          'qtyOrdered',
          'qtyDelivered',
          'qtyReturned',
          'unit',
          'itemId',
        ],
        include:
          paginate === false
            ? [
                {
                  model: this.db.itemModel,
                  as: 'item',
                  attributes: ['isSteelbars'],
                  required: false,
                },
              ]
            : [],
        where: { drId: id },
        order: formattedOrder,
        paginate,
        page,
        limit,
      });

    if (paginate === false && deliveryReceiptItems.data) {
      deliveryReceiptItems.data.sort((a, b) => {
        const aIsSteelbar = a.item?.isSteelbars || false;
        const bIsSteelbar = b.item?.isSteelbars || false;

        if (aIsSteelbar === bIsSteelbar) {
          return 0;
        }

        return aIsSteelbar ? 1 : -1;
      });
    }

    return deliveryReceiptItems;
  }
}

module.exports = DeliveryReceiptService;
