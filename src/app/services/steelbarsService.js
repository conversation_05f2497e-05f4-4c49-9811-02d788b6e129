class SteelbarsService {
  constructor({
    steelbarsRepository,
    steelbarMatrixRepository,
    itemRepository,
    clientErrors,
    fastify,
    utils,
    entities,
    db,
  }) {
    this.steelbarsRepository = steelbarsRepository;
    this.steelbarMatrixRepository = steelbarMatrixRepository;
    this.itemRepository = itemRepository;
    this.fastify = fastify;
    this.clientErrors = clientErrors;
    this.utils = utils;
    this.entities = entities;
    this.db = db;
  }

  async getAllSteelbars(params) {
    let whereClause = {};
    const { sortBy, filterBy, ...queries } = params;
    const { steelbarsSortSchema, steelbarsFilterSchema } =
      this.entities.steelbars;

    console.log(params, 'FILTER BY DETAILS');

    const parsedSortBy = steelbarsSortSchema.parse(sortBy);
    const parsedFilterBy = steelbarsFilterSchema.parse(filterBy);

    console.log(parsedFilterBy, 'FILTER BY DETAILS');
    const { grade, ...restFilter } = parsedFilterBy || {};
    const filterWhereClause = this.utils.buildFilterWhereClause(restFilter);

    if (grade) {
      whereClause.grade = {
        [this.db.Sequelize.Op.iLike]: `%${grade}%`,
      };
    }

    return await this.steelbarsRepository.getAllSteelbars({
      ...queries,
      order: parsedSortBy,
      whereClause: {
        ...whereClause,
        ...filterWhereClause,
      },
    });
  }

  async getSteelbarMatrix() {
    const records = await this.steelbarMatrixRepository.findAll({
      attributes: ['grade', 'diameter', 'weight', 'length'],
      paginate: false,
    });

    const groupedRecords = records.data.reduce((acc, record) => {
      const { grade, diameter, weight, length } = record;
      let gradeGroup = acc.find((group) => group.grade === grade);

      if (!gradeGroup) {
        gradeGroup = { grade, dimensions: [] };
        acc.push(gradeGroup);
      }

      let dimension = gradeGroup.dimensions.find(
        (dim) => dim.diameter === diameter && dim.weight === weight,
      );

      if (!dimension) {
        dimension = { diameter, weight, length: [] };
        gradeGroup.dimensions.push(dimension);
      }

      dimension.length.push(length);
      return acc;
    }, []);

    return groupedRecords;
  }
  async updateOfmAcctCd(steelbarsId, ofmItemAcctCd) {
    const steelbar = await this.steelbarsRepository.getById(steelbarsId);

    if (!steelbar) {
      throw this.clientErrors.NOT_FOUND({
        message: 'Steelbar not found',
      });
    }

    const item = await this.itemRepository.findOne({
      where: {
        acctCd: ofmItemAcctCd,
      },
    });

    if (!item) {
      throw this.clientErrors.NOT_FOUND({ message: 'Item not found' });
    }

    const result = await this.steelbarsRepository.update(
      { id: steelbarsId },
      { ofmAcctcd: ofmItemAcctCd },
    );

    return result;
  }

  async createSteelbar(payload, options = {}) {
    const kgPerMeter = Number((payload.weight / payload.length).toFixed(2));

    const steelbarData = {
      grade: payload.grade,
      diameter: Number(payload.diameter).toFixed(2),
      length: Number(payload.length).toFixed(2),
      weight: Number(payload.weight).toFixed(2),
      kgPerMeter,
      ofm_acctcd: payload.ofm_acctcd,
    };

    let result;
    let action = 'created';
    const unit = payload?.unit;

    if (payload.ofm_acctcd) {
      const item = await this.itemRepository.findOne({
        where: {
          acctCd: payload.ofm_acctcd,
        },
      });

      if (!item) {
        throw this.clientErrors.NOT_FOUND({ message: 'Item not found' });
      }

      const existingOfmSteelbar = await this.steelbarsRepository.findOne({
        where: {
          ofm_acctcd: payload.ofm_acctcd,
        },
      });

      if (existingOfmSteelbar) {
        await this.steelbarsRepository.update(
          { ofm_acctcd: payload.ofm_acctcd },
          steelbarData,
          options,
        );

        if (unit) {
          await this.itemRepository.update(
            { acctCd: payload.ofm_acctcd },
            { unit },
            options,
          );
        }

        result = await this.steelbarsRepository.findOne({
          where: { ofm_acctcd: payload.ofm_acctcd },
        });
        action = 'updated';
      }
    }

    if (!result) {
      result = await this.steelbarsRepository.create(steelbarData);
      action = 'created';
    }

    return { result, action };
  }
}

module.exports = SteelbarsService;
