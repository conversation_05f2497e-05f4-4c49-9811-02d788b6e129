class TransactionLogService {
  constructor(container) {
    const {
      db,
      utils,
      entities,
      transactionLogRepository,
      requisitionRepository,
      userRepository,
      clientErrors,
      fastify,
    } = container;

    this.db = db;
    this.entities = entities;
    this.utils = utils;
    this.transactionLogRepository = transactionLogRepository;
    this.requisitionRepository = requisitionRepository;
    this.userRepository = userRepository;
    this.clientErrors = clientErrors;
    this.fastify = fastify;
  }

  async getAllRequisitionIdJourney(rsId) {
    const rsAlive = await this.requisitionRepository.getById(rsId, {
      attributes: ['id'],
    });

    if (!rsAlive) {
      this.fastify.log.error('Requisition not found.');
      throw this.clientErrors.NOT_FOUND({ message: 'Requisition not found.' });
    }

    const logs =
      await this.transactionLogRepository.getAllRequisitionIdJourney(rsId);

    if (!logs) {
      this.fastify.log.error(`No logs found for ${rsId}`);
      throw this.clientErrors.NOT_FOUND({
        message: `No logs found for ${rsId}`,
      });
    }

    // Add attribute & logic if journey is complete (rs closed)

    return logs;
  }

  async createLog(logData) {
    const { rsId, userId, ...log } = logData;
    const now = new Date().toISOString();

    logData.time = now;

    const rsAlive = await this.requisitionRepository.getById(rsId, {
      attributes: ['id'],
    });

    const userAlive = await this.userRepository.getById(userId, {
      attributes: ['id'],
    });

    if (!rsAlive || !userAlive) {
      this.fastify.log.error('Requisition or user not found.');
      throw this.clientErrors.NOT_FOUND({
        message: 'Requisition or user not found.',
      });
    }

    this.fastify.log.debug('Creating Transaction Log', logData);
    return await this.transactionLogRepository.create(logData);
  }
}

module.exports = TransactionLogService;
