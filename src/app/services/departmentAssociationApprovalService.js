class DepartmentAssociationApprovalService {
  constructor({ db, constants, departmentAssociationApprovalRepository }) {
    this.db = db;
    this.constants = constants;
    this.departmentAssociationApprovalRepository =
      departmentAssociationApprovalRepository;
  }

  async setupDepartmentAssociationApprovals(data) {
    const { approvers, approvalTypeCode } = data;

    const deptAssociationTransaction = await this.db.sequelize.transaction();

    try {
      await this.departmentAssociationApprovalRepository.destroy(
        { approvalTypeCode },
        { transaction: deptAssociationTransaction },
      );

      const assocApprovalsToCreate = approvers.map((approver) => ({
        approvalTypeCode,
        level: approver.level,
        approverId: approver.approverId,
        ...(approver.areaCode && { areaCode: approver.areaCode }),
      }));

      const createdApprovals =
        await this.departmentAssociationApprovalRepository.bulkCreate(
          assocApprovalsToCreate,
          {
            transaction: deptAssociationTransaction,
          },
        );

      await deptAssociationTransaction.commit();
      return createdApprovals;
    } catch (error) {
      await deptAssociationTransaction.rollback();
      throw error;
    }
  }

  async getDepartmentAssociationApprovals(approvalTypeCode) {
    const whereClause = {};

    if (approvalTypeCode) {
      whereClause.approvalTypeCode = approvalTypeCode;
    }

    const allAssociationApprovals =
      await this.departmentAssociationApprovalRepository.getAllAssociationApprovals(
        {
          where: whereClause,
        },
      );

    const groupedAssociationApprovals = this.transformApprovals(
      allAssociationApprovals.data,
    );

    return {
      data: Object.values(groupedAssociationApprovals),
      total: allAssociationApprovals.total,
    };
  }

  transformApprovals(allApprovals = []) {
    const { ASSOCIATION_AREAS } = this.constants.area;
    const groupedApprovals = allApprovals.reduce((groups, approval) => {
      const type = approval.approvalTypeCode;
      if (!groups[type]) {
        groups[type] = {
          type,
          approvers: [],
        };
      }

      groups[type].approvers.push({
        id: approval.id,
        level: approval.level,
        approver: approval.approver,
        createdAt: approval.createdAt,
        updatedAt: approval.updatedAt,
        departmentId: approval.departmentId,
        area: ASSOCIATION_AREAS[approval.areaCode],
      });

      return groups;
    }, {});

    return groupedApprovals;
  }
}

module.exports = DepartmentAssociationApprovalService;
