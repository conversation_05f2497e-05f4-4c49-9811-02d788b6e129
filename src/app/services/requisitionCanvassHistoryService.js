class RequisitionCanvassHistoryService {
  constructor({
    db,
    fastify,
    constants,
    clientErrors,
    canvassService,
    canvassItemService,
    requisitionCanvassHistoryRepository,
    requisitionItemListRepository,
    supplierRepository,
    companyRepository,
    projectRepository,
  }) {
    this.db = db;
    this.Sequelize = db.Sequelize;
    this.constants = constants;
    this.clientErrors = clientErrors;
    this.fastify = fastify;
    this.canvassItemService = canvassItemService;
    this.canvassService = canvassService;
    this.requisitionCanvassHistoryRepository =
      requisitionCanvassHistoryRepository;
    this.requisitionItemListRepository = requisitionItemListRepository;
    this.supplierRepository = supplierRepository;
    this.companyRepository = companyRepository;
    this.projectRepository = projectRepository;
  }

  async createEntries(payload) {
    const { isDraft = false, canvassRequisitionId, transaction } = payload;

    if (isDraft) return;

    if (!canvassRequisitionId) {
      throw this.clientErrors.BAD_REQUEST(
        'Cannot create History: Canvass not found.',
      );
    }

    const { CANVASS_STATUS } = this.constants.canvass;

    const existingCanvass = await this.canvassService.getExistingCanvass(
      canvassRequisitionId,
      {
        transaction,
        include: [
          {
            association: 'requisition',
            as: 'requisition',
          },
        ],
      },
    );

    const {
      csLetter,
      draftCsNumber,
      csNumber,
      status: canvassStatus,
      requisition: { id: requisitionId, companyCode },
    } = existingCanvass;

    const { data: canvassItemListData } =
      await this.canvassItemService.getAllCanvassItems(
        {
          canvassId: canvassRequisitionId,
        },
        { transaction },
      );

    const canvassNumber =
      existingCanvass.status === CANVASS_STATUS.DRAFT
        ? `CS-TMP-${companyCode}${csLetter}${draftCsNumber}`
        : `CS-${companyCode}${csLetter}${csNumber}`;

    let updatedItemList = canvassItemListData.map((item) => {
      const itemName =
        item.requisitionItem.itemDetails.itmDes ||
        item.requisitionItem.itemDetails.itemName ||
        '';

      return {
        ...item,
        itemName: itemName,
      };
    });

    const bulkCreatePayload = updatedItemList.flatMap((item) =>
      item.suppliers.map((supplier) => {
        return {
          canvassRequisitionId,
          requisitionItemListId: item.requisitionItemListId,
          canvassNumber,
          requisitionId,
          status: canvassStatus,
          supplierId: supplier.supplierId,
          supplier: supplier.supplierName,
          price: supplier.unitPrice,
          discount: supplier.discountValue,
          item: item.itemName,
        };
      }),
    );

    await this.requisitionCanvassHistoryRepository.bulkCreate(
      bulkCreatePayload,
      {
        transaction,
      },
    );
  }
}

module.exports = RequisitionCanvassHistoryService;
