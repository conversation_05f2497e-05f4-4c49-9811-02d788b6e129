const fs = require('fs');
const path = require('path');
const juice = require('juice');
const nodemailer = require('nodemailer');
const handlebars = require('handlebars');

class EmailService {
  constructor({ fastify }) {
    this.fastify = fastify;
    this.transporter = nodemailer.createTransport({
      host: 'smtp.gmail.com',
      port: 465,
      secure: true,
      auth: {
        user: process.env.GMAIL_USER,
        pass: process.env.GMAIL_PASS,
      },
    });
  }

  async sendEmail(to, subject, contextValues = {}) {
    const templatesPath = path.join(path.resolve(), 'src', 'templates');
    const baseTemplatePath = path.join(templatesPath, 'base.hbs');
    const contextTemplatePath = path.join(
      templatesPath,
      'context',
      'accountCreated.hbs',
    );

    /* Load base template */
    const baseTemplateSource = fs.readFileSync(baseTemplatePath, 'utf-8');
    const baseTemplate = handlebars.compile(baseTemplateSource);

    /* Load context template */
    const contextTemplateSource = fs.readFileSync(contextTemplatePath, 'utf-8');
    const contextTemplate = handlebars.compile(contextTemplateSource);

    /* TODO: Apply context body to the base template */
    /* Generate the body using the context template */
    const contextBody = contextTemplate(contextValues);
    const html = baseTemplate(contextValues);

    const mailOptions = {
      from: process.env.GMAIL_USER,
      to,
      subject,
      html: juice(html),
      references: new Date().toISOString(),
      attachments: [
        {
          filename: 'building.jpeg',
          path: path.join(templatesPath, 'assets', 'building.jpeg'),
          cid: 'headerBgImageCid',
        },
        {
          filename: 'cityland-logo.png',
          path: path.join(templatesPath, 'assets', 'cityland-logo-white.png'),
          cid: 'headerImageCid',
        },
        {
          filename: 'footer-image.png',
          path: path.join(templatesPath, 'assets', 'footer-logo.png'),
          cid: 'footerImageCid',
        },
      ],
    };

    try {
      const info = await this.transporter.sendMail(mailOptions);
      this.fastify.log.info('Email sent: ' + info.response);
    } catch (error) {
      this.fastify.log.error(`Error sending email: ${JSON.stringify(error)}`);
      throw new Error('Email could not be sent');
    }
  }
}

module.exports = EmailService;
