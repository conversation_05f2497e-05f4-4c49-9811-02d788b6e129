class NotificationService {
  constructor(container) {
    const {
      db,
      constants,
      clientErrors,
      roleRepository,
      notificationRepository,
    } = container;

    this.db = db;
    this.constants = constants;
    this.clientErrors = clientErrors;
    this.roleRepository = roleRepository;
    this.notificationRepository = notificationRepository;
  }

  async getNotifications(userFromToken, filters = {}) {
    const { limit, page, paginate } = filters;

    const whereClause = {
      [this.db.Sequelize.Op.or]: [
        { recipientRoleId: userFromToken.role.id },
        {
          recipientUserIds: {
            [this.db.Sequelize.Op.contains]: [userFromToken.id],
          },
        },
      ],
    };

    const notificationList = await this.notificationRepository.findAll({
      page,
      limit,
      paginate,
      where: whereClause,
      attributes: [
        'id',
        'title',
        'message',
        'type',
        'viewedBy',
        'createdAt',
        'senderId',
        'metaData',
      ],
    });

    return {
      total: notificationList.total,
      isNotViewedCount: await this.#isNotificationViewed(userFromToken),
      data: notificationList.data.map(
        ({ viewedBy, ...notificationDetails }) => ({
          ...notificationDetails,
          isViewed: viewedBy.includes(userFromToken.id),
        }),
      ),
    };
  }

  async createPasswordResetNotification(requestingUser) {
    const { USER_TYPES } = this.constants.user;
    const { NOTIFICATION_TYPES } = this.constants.notification;

    /* Exclude root user from notification */
    if (requestingUser.role.name === USER_TYPES.ROOT_USER) {
      return;
    }

    /**
     * If requester is Admin, send to Root User
     * For all other users, send to Admin
     */
    const targetRoleName =
      requestingUser.role.name === USER_TYPES.ADMIN
        ? USER_TYPES.ROOT_USER
        : USER_TYPES.ADMIN;

    const recipientRole = await this.roleRepository.findOne({
      where: { name: targetRoleName },
    });

    /* Return if recipient role not found */
    if (!recipientRole) {
      return;
    }

    const emailContent = {
      title: 'A User has requested to reset their Password',
      message:
        'A User has requested to reset their Password. Click here to resolve the request.',
    };

    if (requestingUser.role.name === USER_TYPES.ADMIN) {
      emailContent.title = 'An IT Admin has requested to reset their Password';
      emailContent.message =
        'An IT Admin has requested to reset their Password. Click here to resolve the request.';
    }

    return this.notificationRepository.create({
      title: emailContent.title,
      message: emailContent.message,
      type: NOTIFICATION_TYPES.PASSWORD_RESET_REQUEST,
      recipientRoleId: recipientRole.id,
      senderId: requestingUser.id,
    });
  }

  async sendNotification({
    transaction,
    title,
    message,
    type,
    senderId,
    metaData,
    recipientRoleId = null,
    recipientUserIds = null,
  }) {
    return this.notificationRepository.create(
      {
        title,
        message,
        type,
        recipientRoleId,
        recipientUserIds,
        senderId,
        metaData,
      },
      { transaction },
    );
  }

  async seenNotification(id, userFromToken) {
    const notification = await this.notificationRepository.findOne({
      where: {
        id,
        [this.db.Sequelize.Op.or]: [
          {
            recipientUserIds: {
              [this.db.Sequelize.Op.contains]: [userFromToken.id],
            },
          },
          { recipientRoleId: userFromToken.role.id },
        ],
      },
    });

    if (!notification) {
      throw this.clientErrors.NOT_FOUND({
        message: 'Seen notification not allowed',
      });
    }

    const viewedBy =
      notification.viewedBy.length > 0
        ? [...notification.viewedBy, userFromToken.id]
        : [userFromToken.id];

    const updatedNotification = await this.notificationRepository.update(
      { id },
      { viewedBy },
    );

    if (updatedNotification[0] === 0) {
      throw this.clientErrors.NOT_FOUND({
        message: 'Failed updating notification',
      });
    }

    return updatedNotification;
  }

  async #isNotificationViewed(userFromToken) {
    const notificationList = await this.notificationRepository.count({
      where: {
        [this.db.Sequelize.Op.not]: {
          viewedBy: {
            [this.db.Sequelize.Op.contains]: [userFromToken.id],
          },
        },
        [this.db.Sequelize.Op.or]: [
          {
            recipientUserIds: {
              [this.db.Sequelize.Op.contains]: [userFromToken.id],
            },
          },
          {
            recipientRoleId: userFromToken.role.id,
          },
        ],
      },
    });

    return notificationList;
  }

  async sendAdditionalApproverNotification({
    transaction,
    userFromToken,
    approverId,
    requisitionId,
  }) {
    const { NOTIFICATION_TYPES } = this.constants.notification;
    return this.notificationRepository.create(
      {
        title: 'Assigned as an Additional Approver',
        message: `${userFromToken.firstName} ${userFromToken.lastName} has added you to review the Requisition Slip and have it Approved. 
        Click here or access the Dashboard to proceed in reviewing the Requisition Slip.`,
        type: NOTIFICATION_TYPES.REQUISITION_SLIP,
        recipientUserIds: [approverId],
        senderId: userFromToken.id,
        metaData: {
          addedBy: userFromToken.id,
          adhocApprover: approverId,
          requisitionId: requisitionId,
        },
      },
      { transaction },
    );
  }
}

module.exports = NotificationService;
