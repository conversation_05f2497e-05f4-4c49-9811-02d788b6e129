class RequisitionApproverService {
  constructor(container) {
    const {
      db,
      requisitionApproverRepository,
      clientErrors,
      fastify,
      constants,
      requisitionRepository,
      commentRepository,
      userService,
    } = container;

    this.db = db;
    this.requisitionApproverRepository = requisitionApproverRepository;
    this.clientErrors = clientErrors;
    this.fastify = fastify;
    this.requisitionRepository = requisitionRepository;
    this.commentRepository = commentRepository;
    this.constants = constants;
    this.userConstants = constants.user;
    this.userService = userService;
    this.Sequelize = db.Sequelize;
  }

  /**
   * Set the requisition status to assigning if the count of requisition approvers
   * is equal to the count of approved requisition approvers
   *
   * @param {string} requisitionId
   * @param {Object} transaction
   * @returns {Promise<boolean>}
   */
  async setRSStatusToAssigningWhenFullyApproved({
    requisitionId,
    transaction,
    approverId,
  }) {
    const { REQUISITION_STATUS } = this.constants.requisition;

    if (!requisitionId) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'Requisition ID is required in setting RS status to assigning',
      });
    }

    // skip RS assigning flow
    // since there is a new approver
    if (approverId) {
      return false;
    }

    const [rsForApprovalCount, approvedRsCount] = await Promise.all([
      this.requisitionApproverRepository.count({
        where: { requisitionId },
        transaction,
      }),
      this.requisitionApproverRepository.count({
        where: { requisitionId, status: 'approved' },
        transaction,
      }),
    ]);

    if (rsForApprovalCount === approvedRsCount) {
      this.fastify.log.info(
        'All requisition approvers have approved the requisition, updating requisition status to assigning',
      );

      await this.requisitionRepository.update(
        { id: requisitionId },
        { status: REQUISITION_STATUS.ASSIGNING },
        { transaction },
      );

      return true;
    }

    return false;
  }

  /**
   * Set the requisition status to rejected
   *
   * @param {string} requisitionId
   * @param {Object} transaction
   * @returns {Promise<boolean>}
   */
  async setRSStatusToRejected(requisitionId, transaction) {
    const { REQUISITION_STATUS } = this.constants.requisition;
    if (!requisitionId) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'Requisition ID is required in setting RS status to rejected',
      });
    }

    await this.requisitionRepository.update(
      { id: requisitionId },
      { status: REQUISITION_STATUS.REJECTED },
      { transaction },
    );

    return true;
  }

  async addRSComment({ comments, transaction, requisitionId, userId }) {
    const payload = comments.map((data) => ({
      comment: data.comment,
      commentedBy: userId,
      model: 'requisition',
      modelId: requisitionId,
    }));

    return await this.commentRepository.bulkCreate(payload, { transaction });
  }

  async addAdditionalApprover({
    approverId,
    requisitionId,
    transaction,
    approverLevel,
    userId,
    rsToApprove,
  }) {
    if (!approverId || !requisitionId || !approverLevel) {
      throw this.clientErrors.BAD_REQUEST({
        message:
          'Approver ID, requisition ID or approverLevel is required in adding additional approver',
      });
    }

    const addedBy = await this.requisitionApproverRepository.findOne({
      where: {
        requisitionId,
        approverId: userId,
        status: 'pending',
      },
      order: [
        ['createdAt', 'ASC'],
        ['level', 'ASC'],
      ],
    });

    await this.requisitionApproverRepository.update(
      {
        requisitionId,
        approverLevel: {
          [this.Sequelize.Op.gt]: rsToApprove.approverLevel,
        },
      },
      {
        approverLevel: this.Sequelize.literal('approver_level + 1'),
      },
      { transaction },
    );

    await this.requisitionApproverRepository.create(
      {
        requisitionId,
        approverId,
        modelId: approverId,
        level: approverLevel,
        isAdditionalApprover: true,
        modelType: addedBy.modelType,
        status: 'pending',
        addedBy: userId,
        optionalApproverItemIds: null,
        approverLevel: rsToApprove.approverLevel + 1,
      },
      { transaction },
    );
  }

  async editApprovers(request) {
    const { id } = request.params;
    const { body } = request;

    this.fastify.log.info(`Validating Requisition Approver`);
    const approver = await this.requisitionApproverRepository.getById(id);

    if (!approver) {
      throw this.clientErrors.NOT_FOUND({
        message: `Approver Record of ID: ${id} not found`,
      });
    }

    if (body.approverId === null) {
      this.fastify.log.info(`Removing Approver Record`);
      await this.requisitionApproverRepository.destroy({ id });

      await this.requisitionApproverRepository.update(
        {
          requisitionId: approver.requisitionId,
          approverLevel: {
            [this.Sequelize.Op.gt]: approver.approverLevel,
          },
        },
        {
          approverLevel: this.Sequelize.literal('approver_level - 1'),
        },
      );

      return this.fastify.log.info(
        `Successfully Removed approver Approver Record`,
      );
    }

    this.fastify.log.info(`Updating Approver Record`);
    await this.requisitionApproverRepository.update({ id }, { ...body });

    return this.fastify.log.info(`Successfully Updated Approver Record`);
  }

  async getRequisitionApprovers(request) {
    const { APPROVERS } = this.userConstants;
    const { requisitionId } = request.params;

    const requisition = await this.requisitionApproverRepository.findAll({
      where: {
        requisitionId,
      },
    });

    if (!requisition) {
      throw this.clientErrors.NOT_FOUND({
        message: `Requisition not found`,
      });
    }

    const existingRSApprover = requisition.data.map(
      (requisition) => requisition.approverId,
    );

    const approvers = await this.userService.getUsersByRoleName({
      roleNames: Object.values(APPROVERS),
      paranoid: true,
      where: {
        id: {
          [this.Sequelize.Op.notIn]: existingRSApprover,
        },
      },
      attributes: [
        'id',
        [
          this.Sequelize.fn(
            'CONCAT',
            this.Sequelize.col('first_name'),
            ' ',
            this.Sequelize.col('last_name'),
          ),
          'fullName',
        ],
      ],
    });

    return { ...approvers };
  }

  async approvedByPreviousApprover(rsToApprove, requisition) {
    if (
      rsToApprove.isAltApprover === false ||
      (rsToApprove.isAltApprover === true && rsToApprove.altApproverId !== null)
    ) {
      return false;
    }

    const date = new Date();

    const options = {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
      timeZone: 'Asia/Manila',
    };

    const dateToday = date.toLocaleDateString('en-GB', options);

    if (requisition.dateRequired !== dateToday) {
      return false;
    }

    const approvedByPreviousApprover =
      await this.requisitionApproverRepository.findOne({
        where: {
          requisitionId: rsToApprove.requisitionId,
          level: rsToApprove.level,
          status: 'approved',
          altApproverId: null,
        },
        order: [['createdAt', 'ASC']],
      });

    if (approvedByPreviousApprover) {
      return false;
    }

    return true;
  }

  isDefaultApprover(requisition, userFromToken) {
    try {
      return requisition.some((rs) => {
        return rs.addedBy === userFromToken.id;
      });
    } catch (error) {
      this.fastify.log.error(error);
      return false;
    }
  }
}

module.exports = RequisitionApproverService;
