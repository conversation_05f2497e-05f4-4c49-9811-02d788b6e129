class ProjectService {
  constructor(container) {
    const {
      utils,
      fastify,
      entities,
      clientErrors,
      syncRepository,
      projectRepository,
      projectTradeRepository,
    } = container;

    this.utils = utils;
    this.fastify = fastify;
    this.clientErrors = clientErrors;
    this.projectEntity = entities.project;
    this.syncRepository = syncRepository;
    this.projectRepository = projectRepository;
    this.projectTradeRepository = projectTradeRepository;
    this.httpClient = new utils.HTTPClient({
      endpoint: `${process.env.CITYLAND_API_URL}`,
    });
  }

  async syncProjects() {
    const authResult = await this.httpClient.post({
      path: '/token/',
      body: {
        username: process.env.CITYLAND_API_USERNAME,
        password: process.env.CITYLAND_API_PASSWORD,
      },
    });
    const projects = await this.httpClient.get({
      path: '/projects',
      headers: {
        Authorization: `Bearer ${authResult.access}`,
      },
    });
    const mappedProjects = projects.map((project) => ({
      code: project.PROJECT_CODE.trim(),
      name: project.PROJECT_TITLE.trim(),
      initial: project.PROJECT_INITIALS.trim(),
      companyCode: project.COMPANY_CODE.trim(),
      address: project.PROJECT_ADDRESS.trim(),
    }));

    await this.projectRepository.syncProjects(mappedProjects);

    return await this.syncRepository.updateLastSynced('project');
  }

  async validateUpdateProjectPayload(projectData) {
    const parsedProjectData = this.utils.parseDomain(
      this.projectEntity.updateProjectBusinessSchema,
      projectData,
    );

    if (Object.keys(parsedProjectData).length === 0) {
      throw this.clientErrors.UNPROCESSABLE_ENTITY({
        message: 'No project fields provided for update',
      });
    }

    return parsedProjectData;
  }

  async getExistingProject(projectId) {
    const existingProject = await this.projectRepository.getByProjectId(
      parseInt(projectId),
    );

    if (!existingProject) {
      throw this.clientErrors.NOT_FOUND({
        message: 'Project not found',
      });
    }

    return existingProject;
  }

  async addProjectEngineers(data) {
    const { tradeId, projectId, engineerIds } = data;

    const projectTradeEngineers = engineerIds.map((engineerId) => ({
      projectId,
      tradeId,
      engineerId,
    }));

    const result = await this.projectTradeRepository.bulkCreate(
      projectTradeEngineers,
      {
        validate: true,
        ignoreDuplicates: true,
      },
    );

    return result;
  }

  async getProjectEngineers(projectId, tradeId) {
    const engineers = await this.projectTradeRepository.getProjectEngineers(
      projectId,
      tradeId,
    );

    return engineers;
  }

  async getProjectTradeEngineer(payload = {}) {
    const { projectId, tradeId, engineerId } = payload;

    const engineer = await this.projectTradeRepository.findOne({
      where: {
        projectId,
        tradeId,
        engineerId,
      },
    });

    if (!engineer) {
      throw this.clientErrors.NOT_FOUND({
        message: 'Trade Engineer not found',
      });
    }

    return engineer;
  }

  async deleteProjectTradeEngineer(projectTradeId) {
    await this.projectTradeRepository.destroy({
      id: projectTradeId,
    });
  }

  async validateProjects(payload = {}) {
    const { projectIds = [] } = payload;
    const uniqueProjectIds = [...new Set(projectIds)];

    const { data: projects } = await this.projectRepository.findProjectByIds(
      uniqueProjectIds,
      {
        attributes: ['id'],
        paginate: false,
        paranoid: true,
      },
    );

    const missingProjectIds = uniqueProjectIds.filter(
      (id) => !projects.find((item) => item.id === id),
    );

    if (missingProjectIds.length > 0) {
      throw this.clientErrors.NOT_FOUND({
        message: `Unable to find Company with ID(s): ${missingProjectIds}`,
      });
    }

    return projects;
  }
}

module.exports = ProjectService;
