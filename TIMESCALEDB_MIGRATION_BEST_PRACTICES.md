# TimescaleDB Migration Best Practices

This guide explains how to safely create database migrations when using TimescaleDB with compression enabled.

## The Problem

When TimescaleDB hypertables have compression enabled (columnstore), certain database operations will fail with:

```
ERROR: operation not supported on hypertables that have columnstore enabled
```

This typically happens with:s
- `ALTER TABLE` operations (ADD COLUMN, DROP COLUMN, CHANGE COLUMN)
- Schema modifications on compressed hypertables
- Some index operations

## The Solution

Use the provided TimescaleDB migration helper utilities to automatically handle compression during migrations.

## Quick Start

### 1. Import the Helper

```javascript
const { withTimescaleDBCompression, withMultipleTimescaleDBCompression } = require('../utils/timescaledb-migration-helper');
```

### 2. Single Table Migration

```javascript
async up(queryInterface, Sequelize) {
  const transaction = await queryInterface.sequelize.transaction();
  
  try {
    await withTimescaleDBCompression(queryInterface, transaction, 'your_table_name', async () => {
      // Your schema modifications here
      await queryInterface.changeColumn('your_table_name', 'column_name', {
        type: Sequelize.DECIMAL(13, 3),
        allowNull: false,
      }, { transaction });
    });

    await transaction.commit();
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
}
```

### 3. Multiple Tables Migration

```javascript
async up(queryInterface, Sequelize) {
  const transaction = await queryInterface.sequelize.transaction();
  
  try {
    const tables = ['table1', 'table2', 'table3'];
    
    await withMultipleTimescaleDBCompression(queryInterface, transaction, tables, async () => {
      // Modify all tables here - compression is handled automatically
      await queryInterface.changeColumn('table1', 'column1', { ... }, { transaction });
      await queryInterface.changeColumn('table2', 'column2', { ... }, { transaction });
      await queryInterface.changeColumn('table3', 'column3', { ... }, { transaction });
    });

    await transaction.commit();
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
}
```

## Which Tables Are Affected?

The following tables are TimescaleDB hypertables with compression policies:

### High-Volume Tables (7-day compression)
- `notifications`
- `audit_logs`
- `histories`
- `comments`

### History Tables (14-day compression)
- `requisition_canvass_histories`
- `requisition_item_histories`
- `requisition_order_histories`
- `requisition_delivery_histories`
- `requisition_payment_histories`
- `requisition_return_histories`
- `delivery_receipt_items_history`
- `invoice_report_histories`
- `non_requisition_histories`

### Business Tables (30-day compression)
- `requisitions`
- `purchase_orders`
- `delivery_receipts`
- `delivery_receipt_items`
- `attachments`
- `notes`

### Workflow Tables (60-day compression)
- `requisition_approvers`
- `requisition_badges`
- `canvass_approvers`
- `canvass_items`
- `canvass_item_suppliers`
- `purchase_order_items`
- `purchase_order_approvers`
- `requisition_item_lists`

## What the Helper Does

1. **Checks if TimescaleDB extension exists** - skips handling if not present
2. **Checks if table is a hypertable** - skips handling for regular tables
3. **Checks if compression is enabled** - only acts if compression is active
4. **Decompresses compressed chunks** - safely prepares for schema changes
5. **Temporarily disables compression** - allows schema modifications
6. **Executes your migration code** - runs your changes safely
7. **Re-enables compression** - restores original compression settings

## Manual Approach (Not Recommended)

If you need to handle this manually:

```sql
-- 1. Decompress chunks
SELECT decompress_chunk(chunk_name) 
FROM timescaledb_information.chunks 
WHERE hypertable_name = 'your_table' 
AND is_compressed = true;

-- 2. Disable compression
ALTER TABLE your_table SET (timescaledb.compress = false);

-- 3. Make your changes
ALTER TABLE your_table ADD COLUMN new_column VARCHAR(100);

-- 4. Re-enable compression
ALTER TABLE your_table SET (
  timescaledb.compress = true,
  timescaledb.compress_segmentby = 'id'
);
```

## Testing Your Migration

1. **Test on a copy of production data** with TimescaleDB enabled
2. **Verify compression status** before and after migration
3. **Check that data is accessible** after migration completes
4. **Confirm compression policies** are still active

```sql
-- Check compression status
SELECT hypertable_name, compression_enabled 
FROM timescaledb_information.hypertables 
WHERE hypertable_schema = 'public';

-- Check compression policies
SELECT hypertable_name, job_id, config 
FROM timescaledb_information.jobs 
WHERE proc_name = 'policy_compression';
```

## Common Mistakes to Avoid

1. **Don't manually edit compression settings** - use the helper utilities
2. **Don't forget transactions** - always wrap in transactions for rollback safety
3. **Don't assume tables aren't hypertables** - always use the helper for safety
4. **Don't skip testing** - test migrations on TimescaleDB-enabled environments

## Troubleshooting

### Migration Still Fails?

1. Check if you're using the helper correctly
2. Verify the table name is correct
3. Check if there are foreign key constraints preventing changes
4. Look for other TimescaleDB features that might interfere

### Compression Not Re-enabled?

1. Check migration logs for errors during re-enabling
2. Manually check compression status with SQL queries above
3. Re-run compression setup if needed: `npm run timescaledb:setup-compression`

### Performance Issues After Migration?

1. Check if compression policies are still active
2. Verify chunk intervals are appropriate
3. Run TimescaleDB optimization: `npm run timescaledb:optimize`

## Example Files

- `src/infra/database/utils/timescaledb-migration-helper.js` - Helper utilities
- `src/infra/database/migrations/EXAMPLE-timescaledb-safe-migration.js` - Example usage
- `src/infra/database/migrations/20250429090135-change-qty-data-type-in-dr-item-history.js` - Real example

## Need Help?

1. Check existing migrations for examples
2. Review TimescaleDB documentation
3. Test on local TimescaleDB setup first
4. Use the helper utilities - they handle edge cases automatically
