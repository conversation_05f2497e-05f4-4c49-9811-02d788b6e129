#!/usr/bin/env node

/**
 * Force Close Test Runner
 *
 * This script runs comprehensive tests for Tasks 1.0 and 2.0:
 * - Task 1.0: Critical Infrastructure Tests
 * - Task 2.0: API Alignment Tests
 *
 * Usage: node test-force-close.js
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('🧪 Force Close Test Suite - Tasks 1.0 & 2.0');
console.log('================================================\n');

// Test files to run
const testFiles = [
  // Task 1.0 Infrastructure Tests
  'test/unit/src/app/services/forceCloseService.spec.js',
  'test/unit/src/infra/repositories/forceCloseRepository.spec.js',

  // Task 2.0 API Alignment Tests
  'test/unit/src/app/handlers/controllers/forceCloseController.spec.js',
  'test/unit/src/interfaces/forceCloseRoutes.spec.js',
];

// Test configuration
const mochaOptions = [
  '--recursive',
  '--timeout',
  '10000',
  '--reporter',
  'spec',
  '--require',
  'test/setup.js', // If setup file exists
  ...testFiles,
];

console.log('📋 Test Plan:');
console.log('- Task 1.0: Infrastructure Components');
console.log('  ✓ ForceCloseService (scenario validation, dual paths)');
console.log('  ✓ ForceCloseRepository (database operations, transactions)');
console.log('- Task 2.0: API Alignment');
console.log('  ✓ ForceCloseController (request handling, responses)');
console.log('  ✓ API Routes (endpoint structure, schema validation)');
console.log('');

// Run tests
console.log('🚀 Running tests...\n');

const mocha = spawn('npx', ['mocha', ...mochaOptions], {
  stdio: 'inherit',
  cwd: process.cwd(),
});

mocha.on('close', (code) => {
  console.log('\n================================================');

  if (code === 0) {
    console.log('✅ All Force Close Tests Passed!');
    console.log('');
    console.log('📊 Test Summary:');
    console.log('- Task 1.0 Infrastructure: ✅ PASSED');
    console.log('- Task 2.0 API Alignment: ✅ PASSED');
    console.log('');
    console.log('🎉 Tasks 1.0 and 2.0 are ready for production!');
    console.log('');
    console.log('Next Steps:');
    console.log('1. Run database migrations: npm run migrate');
    console.log('2. Test API endpoints manually with Bruno/Postman');
    console.log('3. Proceed to Task 3.0 implementation');
  } else {
    console.log('❌ Some tests failed!');
    console.log('');
    console.log('🔧 Troubleshooting:');
    console.log('1. Check test output above for specific failures');
    console.log('2. Verify all dependencies are installed: npm install');
    console.log('3. Ensure database is running for integration tests');
    console.log('4. Fix failing tests before proceeding to Task 3.0');
  }

  process.exit(code);
});

mocha.on('error', (err) => {
  console.error('❌ Failed to start test runner:', err.message);
  console.log('');
  console.log('🔧 Possible solutions:');
  console.log('1. Install dependencies: npm install');
  console.log('2. Check if mocha is installed: npx mocha --version');
  console.log('3. Verify test files exist in the specified paths');
  process.exit(1);
});
