openapi: 3.0.1
info:
  title: PRS API
  description: Purchase Requisition System API Documentation
  version: '1.0'
servers:
  - url: http://localhost:4000
    description: Local development server
  - url: http://*************/api
    description: Development server

tags:
  - name: Authentication
    description: Authentication related endpoints
  - name: Users
    description: User management
  - name: Suppliers
    description: Supplier management
  - name: Companies
    description: Company management
  - name: Projects
    description: Project management
  - name: Approvals
    description: Approval management
  - name: Departments
    description: Department management
  - name: Notifications
    description: Notification

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    # Auth Schemas
    LoginRequest:
      type: object
      required:
        - username
        - password
      properties:
        username:
          type: string
          example: 'username'
        password:
          type: string
          example: 'password'

    ForgotPasswordRequest:
      type: object
      required:
        - username
      properties:
        username:
          type: string
          minLength: 5
          maxLength: 50
          example: 'username'

    OTPRequest:
      type: object
      required:
        - otp
      properties:
        otp:
          type: string
          minLength: 6
          maxLength: 6
          example: '123456'

    SetupOTPRequest:
      type: object
      required:
        - otpSecret
        - otp
      properties:
        otpSecret:
          type: string
        otp:
          type: string
          minLength: 6
          maxLength: 6

    UpdatePasswordRequest:
      type: object
      required:
        - password
      properties:
        password:
          type: string
          minLength: 8
          example: 'password'

    # User Schemas
    CreateUserRequest:
      type: object
      required:
        - firstName
        - lastName
        - roleId
        - departmentId
        - username
        - email
      properties:
        firstName:
          type: string
          maxLength: 100
          pattern: "^[A-Za-z'.-]+$"
          example: 'firstName'
        lastName:
          type: string
          maxLength: 100
          pattern: "^[A-Za-z'.-]+$"
        roleId:
          type: number
          example: 2
        departmentId:
          type: number
          example: 27
        username:
          type: string
          minLength: 5
          maxLength: 50
          example: 'username'
        email:
          type: string
          format: email
          maxLength: 100
          example: '<EMAIL>'

    UpdateUserRequest:
      type: object
      properties:
        firstName:
          type: string
          maxLength: 100
          pattern: "^[A-Za-z'.-]+$"
          example: 'firstName'
        lastName:
          type: string
          maxLength: 100
          pattern: "^[A-Za-z'.-]+$"
          example: 'lastName'
        roleId:
          type: number
          example: 2
        departmentId:
          type: number
          example: 27
        email:
          type: string
          format: email
          maxLength: 100
          example: '<EMAIL>'
        status:
          type: string
          enum: [active, inactive]
          example: 'active'

    # Supplier Schemas
    GetAllSuppliersQuery:
      type: object
      properties:
        search:
          type: string
          example: 'search'
        sort:
          type: string
          enum: [asc, desc]
          example: '{"createdAt": "desc"}'
        page:
          type: string
          pattern: ^\d+$
          example: 1
          default: 1
        limit:
          type: string
          pattern: ^\d+$
          example: 10
          default: 10

    SupplierComment:
      type: object
      required:
        - comment
      properties:
        comment:
          type: string
          minLength: 1
          example: 'comment'

    SupplierCommentQuery:
      type: object
      properties:
        commentDateFrom:
          type: string
          format: date-time
          example: '2024-11-05T00:44:54.607Z'
        commentDateTo:
          type: string
          format: date-time
          example: '2024-11-05T00:44:54.607Z'

    SupplierAttachmentQuery:
      type: object
      properties:
        attachmentDateFrom:
          type: string
          format: date-time
          example: '2024-11-05T00:44:54.607Z'
        attachmentDateTo:
          type: string
          format: date-time
          example: '2024-11-05T00:44:54.607Z'

    # Supplier Schemas
    UpdateSupplierRequest:
      type: object
      properties:
        contactPerson:
          type: string
          maxLength: 100
          pattern: "^[A-Za-z\\s'.-]+$"
          example: 'contactPerson'
        contactNumber:
          type: string
          maxLength: 12
          pattern: "^[0-9'.+]+$"
          example: '+639999999999'
        status:
          type: string
          example: 'active'
      minProperties: 1

    #Supplier Schema
    UpdateSupplierAttachments:
      type: array
      items:
        type: object
        required:
          - id
          - paths
        properties:
          id:
            type: integer
          fileName:
            type: string
          model:
            type: string
          modelId:
            type: integer
          paths:
            type: string
          userAttachment:
            type: object
            properties:
              id:
                type: integer
              firstName:
                type: string
              lastName:
                type: string
      example:
        - id: 1
          fileName: 'example.jpeg'
          model: 'supplier'
          modelId: 2
          paths: /supplier/example.jpeg-1733308933959.jpeg
          userAttachment:
            id: 1
            firstName: 'hello'
            lastName: 'world'
        - id: 2
          path: /supplier/example2.jpeg-1733308933959.jpeg

    # Project Schemas
    UpdateProjectRequest:
      type: object
      properties:
        companyId:
          type: number
          example: 1
        startDate:
          type: string
          format: date-time
          example: '2024-11-05T00:44:54.607Z'
        endDate:
          type: string
          format: date-time
          example: '2024-11-05T00:44:54.607Z'
      minProperties: 1

    # Approver Schemas
    CreateApproverRequest:
      type: object
      required:
        - model
        - userId
        - approvalOrder
      properties:
        model:
          type: string
          minLength: 1
          maxLength: 100
          pattern: '^[a-zA-Z0-9_]*$'
          example: 'model'
        userId:
          type: integer
          minimum: 1
          example: 1
        approvalOrder:
          type: integer
          minimum: 1
          example: 1
        altApproverId:
          type: integer
          minimum: 1
          nullable: true
          example: 1

    UpdateApproverRequest:
      type: object
      minProperties: 1
      properties:
        model:
          type: string
          minLength: 1
          maxLength: 100
          pattern: '^[a-zA-Z0-9_]*$'
          example: 'model'
        userId:
          type: integer
          minimum: 1
          example: 1
        approvalOrder:
          type: integer
          minimum: 1
          example: 1
        altApproverId:
          type: integer
          minimum: 1
          nullable: true
          example: 1

    UserRoleListResponse:
      type: object
      properties:
        data:
          type: array
          items:
            type: object
            properties:
              id:
                type: integer
                example: 4
              firstName:
                type: string
                example: 'firstName'
              lastName:
                type: string
                example: 'lastName'
        total:
          type: integer
          description: Total number of users with this role
          example: 1

    # Requisition Schemas
    CreateRequisitionRequest:
      type: object
      required:
        - type
        - companyId
        - projectId
        - departmentId
        - dateRequired
        - deliverTo
        - purpose
        - chargeTo
        - chargeToId
        - attachments
        - comment
      properties:
        type:
          type: string
          example: 'ofm'
        companyId:
          type: integer
          example: 27
        projectId:
          type: integer
          example: 1
        departmentId:
          type: integer
          example: 6
        dateRequired:
          type: string
          format: date
          example: '2024-01-01'
        deliverTo:
          type: string
          example: 'Tondo, Manila'
        purpose:
          type: string
          example: 'Purchase of Materials'
        chargeTo:
          type: string
          example: 'project'
        chargeToId:
          type: integer
          example: 3
        comment:
          type: string
          example: 'this is a comment'
        itemList:
          type: array
          items:
            type: object
            required:
              - itemId
              - quantity
              - notes
            properties:
              itemId:
                type: integer
              quantity:
                type: integer
              notes:
                type: string
              accountCode:
                type: string
          example:
            - itemId: 580
              quantity: 2
              notes: note 1
              accountCode: 123
            - itemId: 581
              quantity: 3
              notes: note 2
              accountCode: 456
        attachments:
          type: array

    #Update Requisition Schema
    UpdateRequisitionSchema:
      type: object
      properties:
        type:
          type: string
          example: 'ofm'
        companyId:
          type: integer
          example: 27
        projectId:
          type: integer
          example: 1
        departmentId:
          type: integer
          example: 6
        dateRequired:
          type: string
          format: date-time
          example: '2024-01-01T00:00:00.000Z'
        purpose:
          type: string
          example: 'Purchase of Materials'
        chargeTo:
          type: string
          example: 'project'
        chargeToId:
          type: integer
          example: 1
        rsLetter:
          type: string
          example: 'AA'
        companyCode:
          type: string
          example: '27'
        deliveryAddress:
          type: string
          example: 'Tondo, Manila'
        status:
          type: string
          example: 'draft'
        assignedTo:
          type: integer
          nullable: true
          example: null
        attachments:
          type: array
          items:
            type: object
            required:
              - id
              - paths
            properties:
              id:
                type: integer
              fileName:
                type: string
              model:
                type: string
              modelId:
                type: integer
              paths:
                type: string
              userAttachment:
                type: object
                properties:
                  id:
                    type: integer
                  firstName:
                    type: string
                  lastName:
                    type: string
          example:
            - id: 1
              fileName: 'example.jpeg'
              model: 'requisition'
              modelId: 2
              paths: /requisitions/example.jpeg-1733308933959.jpeg
              userAttachment:
                id: 1
                firstName: 'hello'
                lastName: 'world'
            - id: 2
              path: /requisitions/example2.jpeg-1733308933959.jpeg

    CreateRequisitionItemListRequest:
      type: object
      required:
        - ofmItemId
        - quantity
        - requisitionId
      properties:
        ofmItemId:
          type: integer
          example: 1
        quantity:
          type: integer
          example: 1
        requisitionId:
          type: integer
          example: 1
        notes:
          type: string
          example: 'notes'

    CreateRequisitionTomItemListRequest:
      type: object
      required:
        - name
        - quantity
      properties:
        name:
          type: string
          example: 'name'
        quantity:
          type: integer
          example: 1
        comment:
          type: string
          example: 'comment'

    RequisitionComment:
      type: object
      required:
        - comment
      properties:
        comment:
          type: string
          minLength: 1
          example: 'comment'

    RejectRequisitionSchema:
      type: array
      items:
        type: object
        required:
          - comment
        properties:
          comment:
            type: string
            minLength: 1
            example: 'test'

    IdNameObject:
      type: object
      properties:
        id:
          type: string
          example: '1'
        name:
          type: string
          example: 'PR001'

    CreateDeliveryReceiptRequest:
      type: object
      required:
        - requisitionId
        - poId
        - supplier
        - isDraft
        - note
        - items
      properties:
        requisitionId:
          type: string
          example: '1'
        poId:
          type: string
          example: '1'
        supplier:
          type: string
          example: 'Supplier Company'
        isDraft:
          type: string
          enum:
            - 'true'
            - 'false'
        note:
          type: string
          example: 'This is a note for delivery receipt.'
        attachments:
          type: array
          items:
            type: string
            format: binary
        items:
          type: array
          items:
            $ref: '#/components/schemas/CreateDeliveryReceiptItemsRequest'

    CreateDeliveryReceiptItemsRequest:
      type: object
      required:
        - itemId
        - poId
        - itemDes
        - qtyOrdered
        - unit
      properties:
        itemId:
          type: string
          example: '1'
        poId:
          type: string
          example: '1'
        itemDes:
          type: string
          example: 'Item Name'
        qtyOrdered:
          type: string
          example: '20'
        qtyReceived:
          type: string
          example: '20'
        unit:
          type: string
          example: 'PCS.'
        notes:
          type: string
          example: 'This is a note for item.'

    UpdateDeliveryReceiptRequest:
      type: object
      required:
        - requisitionId
        - poId
        - supplier
        - isDraft
        - note
        - items
      properties:
        requisitionId:
          type: string
          example: '1'
        poId:
          type: string
          description: 'If poId is different from the existing one, the old delivery receipt items with the previous poId should be deleted.'
          example: '1'
        supplier:
          type: string
          example: 'Supplier Company'
        isDraft:
          type: boolean
          example: true
        note:
          type: string
          example: 'This is a note for delivery receipt.'
        currentAttachments:
          type: array
          description: 'The attachments that are currently attached to the delivery receipt. If this is present, it must compare to the existing attachments and delete the ones that are not in the currentAttachments.'
          items:
            type: object
            properties:
              id:
                type: integer
              modelId:
                type: integer
              fileName:
                type: string
        attachments:
          type: array
          description: 'The attachments that are to be attached to the delivery receipt. If this is present, a new attachment will be added to the delivery receipt.'
          items:
            type: string
            format: binary
        items:
          type: array
          items:
            $ref: '#/components/schemas/UpdateDeliveryReceiptItemsRequest'

    UpdateDeliveryReceiptItemsRequest:
      type: object
      required:
        - itemId
        - poId
        - itemDes
        - qtyOrdered
        - unit
      properties:
        id:
          type: string
          description: 'The id of the delivery receipt item. If this is present, it must find a delivery receipt item with this id, then update it.'
          example: '1'
        itemId:
          type: string
          example: '1'
        poId:
          type: string
          description: 'If poId is different from the existing one, the old delivery receipt items with the previous poId should be deleted.'
          example: '1'
        itemDes:
          type: string
          example: 'Item Name'
        qtyOrdered:
          type: integer
          example: 20
        qtyReceived:
          type: integer
          example: 20
        unit:
          type: string
          example: 'PCS.'
        notes:
          type: string
          example: 'This is a note for item.'

    DeliveryReceiptResponse:
      type: object
      properties:
        id:
          type: integer
          example: 1
        isDraft:
          type: boolean
          example: true
        drNumber:
          type: string
          example: 'RR-01AA00000001'
        poId:
          type: integer
          example: 1
        supplier:
          type: string
          example: 'Supplier Company'
        note:
          type: string
          example: 'This is a note for delivery receipt.'
        attachments:
          type: array
          items:
            type: object
            properties:
              id:
                type: integer
                example: 1
              model:
                type: string
                example: 'delivery_receipt'
              modelId:
                type: integer
                example: 2
              userId:
                type: integer
                example: 2
              fileName:
                type: string
                example: 'sample.pdf'
              path:
                type: string
                example: '/delivery_receipt/sample.pdf-1737119530037.pdf'
        items:
          type: array
          items:
            $ref: '#/components/schemas/DeliveryReceiptItemsResponse'
        draftSavedAt:
          type: string
          description: 'If isDraft is true, the value will be present. Else, the value will be null.'
          format: date
          example: '2024-12-03T00:00:00.000Z'

    DeliveryReceiptItemsResponse:
      type: object
      properties:
        id:
          type: integer
          description: 'ID of the delivery receipt item.'
          example: 1
        itemId:
          type: integer
          description: 'ID of the actual item.'
          example: 1
        poId:
          type: integer
          example: 1
        itemDes:
          type: string
          example: 'BI PIPES SCH 40 (1") 25 MM X 6M SUPREME'
        qtyOrdered:
          type: integer
          example: 100
        qtyDelivered:
          type: integer
          example: 100
        dateDelivered:
          type: string
          format: date
          example: '10 Nov 2024'
        unit:
          type: string
          example: 'PCS.'
        deliveryStatus:
          type: string
          enum:
            [
              Partially Delivered,
              Fully Delivered,
              Partially Delivered with Returns,
              Fully Delivered with Returns,
            ]
          example: 'Partially Delivered'
        notes:
          type: string
          example: 'This is a note.'

    ClientErrorResponse:
      type: object
      properties:
        status:
          type: integer
          enum: [400, 404, 422]
        message:
          type: string
        errorCode:
          type: string
          enum: ['BAD_REQUEST', 'NOT_FOUND', 'UNPROCESSABLE_ENTITY']
        timestamp:
          type: string
          format: date-time
      required:
        - status
        - message
        - errorCode
        - timestamp

    GetMockPurchaseOrderItems:
      type: object
      properties:
        id:
          type: integer
          example: 1
        accountCode:
          type: string
          example: 'AC-0001'
        itemDes:
          type: string
          example: 'Item 1'
        qtyPurchased:
          type: integer
          example: 100
        unit:
          type: string
          example: 'PCS.'

    GetMockPurchaseOrderByIdResponse:
      type: object
      properties:
        id:
          type: integer
          example: 1
        poNumber:
          type: string
          example: 'PO-2024-001'
        supplier:
          $ref: '#/components/schemas/IdNameObject'
        items:
          type: array
          items:
            $ref: '#/components/schemas/GetMockPurchaseOrderItems'

paths:
  /v1/auth/login:
    post:
      tags:
        - Authentication
      summary: User login
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginRequest'
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  accessToken:
                    type: string
                  refreshToken:
                    type: string
                  expiredAt:
                    type: integer
                    example: 1730782146
                    description: Unix timestamp in seconds
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 401
                  message:
                    type: string
                    example: 'Invalid username or password'
                  errorCode:
                    type: string
                    example: 'UNAUTHORIZED'
                  timestamp:
                    type: string
                    format: date-time
                    example: '2024-11-05T07:28:22.632Z'

  /v1/auth/forgot-password:
    post:
      tags:
        - Authentication
      summary: Request password reset
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ForgotPasswordRequest'
      responses:
        '200':
          description: Password reset successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: 'Password reset request sent, please wait for IT Admin to reset your password'
        '404':
          description: User not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 404
                  message:
                    type: string
                    example: 'User not found'
                  errorCode:
                    type: string
                    example: 'NOT_FOUND'
                  timestamp:
                    type: string
                    format: date-time
                    example: '2024-11-05T07:28:22.632Z'

  /v1/auth/verify-otp:
    post:
      tags:
        - Authentication
      summary: Verify OTP
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OTPRequest'
      responses:
        '200':
          description: OTP verified successfully

  /v1/auth/setup-otp:
    post:
      tags:
        - Authentication
      summary: Setup OTP
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SetupOTPRequest'
      responses:
        '200':
          description: OTP setup successful

  /v1/auth/update-temp-password:
    post:
      tags:
        - Authentication
      summary: Update temporary password
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdatePasswordRequest'
      responses:
        '200':
          description: Password updated successfully

  # User Routes
  /v1/users:
    get:
      tags:
        - Users
      summary: Get all users
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: page
          schema:
            type: string
            default: 1
        - in: query
          name: limit
          schema:
            type: string
            default: 10
        - in: query
          name: search
          schema:
            type: string
        - in: query
          name: sortBy
          schema:
            type: string
            example: '{"username": "desc"}'
        - in: query
          name: filterBy
          schema:
            type: string
            example: '{"status": "active"}'
        - in: query
          name: paginate
          schema:
            type: string
            default: true
      responses:
        '200':
          description: List of users retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          example: 24
                        username:
                          type: string
                          example: 'testing'
                        email:
                          type: string
                          format: email
                          example: '<EMAIL>'
                        tempPass:
                          type: string
                          description: Temporary password generated for the user
                          example: 'john.doe_TEMP123!'
                        firstName:
                          type: string
                          example: 'firstName'
                        lastName:
                          type: string
                          example: 'lastName'
                        status:
                          type: string
                          enum: [active, inactive]
                          example: 'active'
                        createdAt:
                          type: string
                          format: date-time
                          example: '2024-11-05T00:44:54.607Z'
                        updatedAt:
                          type: string
                          format: date-time
                          example: '2024-11-05T01:10:26.419Z'
                        deletedAt:
                          type: string
                          format: date-time
                          nullable: true
                          example: null
                        role:
                          type: object
                          properties:
                            id:
                              type: integer
                              example: 2
                            name:
                              type: string
                              example: 'Admin'
                        department:
                          type: object
                          properties:
                            id:
                              type: integer
                              example: 27
                            name:
                              type: string
                              example: "ENG'G. & DEVT. DEPT."
                  total:
                    type: integer
                    description: Total number of users
                    example: 1

    post:
      tags:
        - Users
      summary: Create new user
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUserRequest'
      responses:
        '201':
          description: User created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: integer
                    example: 25
                  firstName:
                    type: string
                    example: 'firstName'
                  lastName:
                    type: string
                    example: 'lastName'
                  roleId:
                    type: integer
                    example: 2
                  username:
                    type: string
                    example: 'username'
                  tempPass:
                    type: string
                    description: Temporary password generated for the user
                    example: 'john.doe_TEMP123!'
                  email:
                    type: string
                    format: email
                    example: '<EMAIL>'
                  status:
                    type: string
                    enum: [active, inactive]
                    example: 'active'
                  isPasswordTemporary:
                    type: boolean
                    example: true
                  createdAt:
                    type: string
                    format: date-time
                    example: '2024-11-05T08:11:10.570Z'
                  updatedAt:
                    type: string
                    format: date-time
                    example: '2024-11-05T08:11:10.570Z'
                  deletedAt:
                    type: string
                    format: date-time
                    nullable: true
                    example: null

  /v1/users/me:
    get:
      tags:
        - Users
      summary: Get current user details
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Current user details retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: integer
                    example: 1
                  username:
                    type: string
                    example: 'username'
                  email:
                    type: string
                    format: email
                    example: '<EMAIL>'
                  firstName:
                    type: string
                    example: 'firstName'
                  lastName:
                    type: string
                    example: 'lastName'
                  status:
                    type: string
                    enum: [active, inactive]
                    example: 'active'
                  isPasswordTemporary:
                    type: boolean
                    example: false
                  tempPass:
                    type: string
                    description: Temporary password generated for the user
                    example: 'john.doe_TEMP123!'
                  createdAt:
                    type: string
                    format: date-time
                    example: '2024-10-23T05:25:21.604Z'
                  updatedAt:
                    type: string
                    format: date-time
                    example: '2024-10-30T08:33:44.777Z'
                  deletedAt:
                    type: string
                    format: date-time
                    nullable: true
                    example: null
                  department:
                    type: object
                    properties:
                      id:
                        type: integer
                        example: 27
                      name:
                        type: string
                        example: "ENG'G. & DEVT. DEPT."
                  role:
                    type: object
                    properties:
                      id:
                        type: integer
                        example: 1
                      name:
                        type: string
                        example: 'Admin'
                      permissions:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: integer
                              example: 1
                            module:
                              type: string
                              example: 'users'
                            action:
                              type: string
                              example: 'create'

  /v1/users/roles:
    get:
      tags:
        - Users
      summary: Get available user roles
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: page
          schema:
            type: string
            default: 1
        - in: query
          name: limit
          schema:
            type: string
            default: 10
        - in: query
          name: paginate
          schema:
            type: string
            default: true
      responses:
        '200':
          description: User roles retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          example: 2
                        name:
                          type: string
                          example: 'Admin'
                        isPermanent:
                          type: boolean
                          example: true
                        createdAt:
                          type: string
                          format: date-time
                          example: '2024-10-23T05:25:03.706Z'
                        updatedAt:
                          type: string
                          format: date-time
                          example: '2024-10-23T05:25:03.706Z'
                  total:
                    type: integer
                    description: Total number of roles
                    example: 1

  /v1/users/approvers:
    get:
      tags:
        - Users
      summary: Get all users who can be approvers
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: page
          schema:
            type: string
            default: 1
        - in: query
          name: limit
          schema:
            type: string
            default: 10
        - in: query
          name: paginate
          schema:
            type: string
            default: false
      responses:
        '200':
          description: List of approvers retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          example: 15
                        firstName:
                          type: string
                          example: 'firstName'
                        lastName:
                          type: string
                          example: 'lastName'
                  total:
                    type: integer
                    example: 5

  /v1/users/area-staffs:
    get:
      tags:
        - Users
      summary: Get all area staff users
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: page
          schema:
            type: string
            default: 1
        - in: query
          name: limit
          schema:
            type: string
            default: 10
        - in: query
          name: paginate
          schema:
            type: string
            default: true
      responses:
        '200':
          description: List of area staff users retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          example: 15
                        firstName:
                          type: string
                          example: 'firstName'
                        lastName:
                          type: string
                          example: 'lastName'
                  total:
                    type: integer
                    example: 5

  /v1/users/supervisors:
    get:
      tags:
        - Users
      summary: Get all supervisors
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: page
          schema:
            type: string
            default: 1
        - in: query
          name: limit
          schema:
            type: string
            default: 10
        - in: query
          name: paginate
          schema:
            type: string
            default: true
      responses:
        '200':
          description: List of supervisors retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserRoleListResponse'

  /v1/users/department-division-heads:
    get:
      tags:
        - Users
      summary: Get all Department Division Heads
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: page
          schema:
            type: string
            default: 1
        - in: query
          name: limit
          schema:
            type: string
            default: 10
        - in: query
          name: paginate
          schema:
            type: string
            default: true
      responses:
        '200':
          description: List of supervisors retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserRoleListResponse'

  /v1/users/assistant-managers:
    get:
      tags:
        - Users
      summary: Get all assistant managers
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: page
          schema:
            type: string
            default: 1
        - in: query
          name: limit
          schema:
            type: string
            default: 10
        - in: query
          name: paginate
          schema:
            type: string
            default: true
      responses:
        '200':
          description: List of assistant managers retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserRoleListResponse'

  /v1/users/department-heads:
    get:
      tags:
        - Users
      summary: Get all department heads
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: page
          schema:
            type: string
            default: 1
        - in: query
          name: limit
          schema:
            type: string
            default: 10
        - in: query
          name: paginate
          schema:
            type: string
            default: true
      responses:
        '200':
          description: List of department heads retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserRoleListResponse'

  /v1/users/secretaries:
    get:
      tags:
        - Users
      summary: Get all department secretaries
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: page
          schema:
            type: string
            default: 1
        - in: query
          name: limit
          schema:
            type: string
            default: 10
        - in: query
          name: paginate
          schema:
            type: string
            default: true
      responses:
        '200':
          description: List of department secretaries retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserRoleListResponse'

  /v1/users/{id}:
    get:
      tags:
        - Users
      summary: Get user by ID
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
          description: User ID
      responses:
        '200':
          description: User details retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: integer
                    example: 24
                  username:
                    type: string
                    example: 'username'
                  email:
                    type: string
                    format: email
                    example: '<EMAIL>'
                  firstName:
                    type: string
                    example: 'firstName'
                  lastName:
                    type: string
                    example: 'lastName'
                  status:
                    type: string
                    enum: [active, inactive]
                    example: 'active'
                  createdAt:
                    type: string
                    format: date-time
                    example: '2024-11-05T00:44:54.607Z'
                  updatedAt:
                    type: string
                    format: date-time
                    example: '2024-11-05T01:10:26.419Z'
                  deletedAt:
                    type: string
                    format: date-time
                    nullable: true
                    example: null
                  role:
                    type: object
                    properties:
                      id:
                        type: integer
                        example: 2
                      name:
                        type: string
                        example: 'Admin'
                  department:
                    type: object
                    properties:
                      id:
                        type: integer
                        example: 27
                      name:
                        type: string
                        example: "ENG'G. & DEVT. DEPT."
        '404':
          description: User not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 404
                  message:
                    type: string
                    example: 'User not found'
                  errorCode:
                    type: string
                    example: 'NOT_FOUND'
                  timestamp:
                    type: string
                    format: date-time
                    example: '2024-11-05T07:28:22.632Z'
    put:
      tags:
        - Users
      summary: Update user
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateUserRequest'
      responses:
        '200':
          description: User updated successfully

  /v1/users/update-password:
    put:
      tags:
        - Users
      summary: Update user password
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdatePasswordRequest'
      responses:
        '200':
          description: Password updated successfully

  /v1/users/reset-password:
    post:
      summary: Reset user password
      description: Reset a user's password to default (username + secret)
      tags:
        - Users
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - userId
              properties:
                userId:
                  type: integer
                  description: ID of the user whose password needs to be reset
                  example: 123
      responses:
        '200':
          description: Password reset successful
          content:
            application/json:
              schema:
                type: object
                required:
                  - message
                  - tempPass
                properties:
                  message:
                    type: string
                    example: 'Password updated successfully'
                  tempPass:
                    type: string
                    description: Temporary password generated for the user
                    example: 'john.doe_TEMP123!'

  /v1/notifications:
    get:
      summary: Get user notifications
      description: Retrieve notifications for the authenticated user
      tags:
        - Notifications
      parameters:
        - in: query
          name: page
          schema:
            type: string
            default: 1
        - in: query
          name: limit
          schema:
            type: string
            default: 10
        - in: query
          name: paginate
          schema:
            type: string
            default: true
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                required:
                  - data
                  - total
                properties:
                  data:
                    type: array
                    items:
                      type: object
                      required:
                        - id
                        - title
                        - message
                        - type
                        - createdAt
                        - isViewed
                      properties:
                        id:
                          type: integer
                          example: 2
                        title:
                          type: string
                          example: 'Password Reset Request'
                        message:
                          type: string
                          example: 'Password reset requested by John Doe'
                        type:
                          type: string
                          example: 'password_reset_request'
                        createdAt:
                          type: string
                          format: date-time
                          example: '2024-11-13T03:27:11.025Z'
                        isViewed:
                          type: boolean
                          example: false
                  total:
                    type: integer
                    example: 1

  /v1/suppliers:
    get:
      tags:
        - Suppliers
      summary: Get all suppliers
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: page
          schema:
            type: string
            default: 1
        - in: query
          name: limit
          schema:
            type: string
            default: 10
        - in: query
          name: paginate
          schema:
            type: string
            default: true
      responses:
        '200':
          description: List of suppliers retrieved successfully

  /v1/suppliers/{id}:
    get:
      tags:
        - Suppliers
      summary: Get supplier by ID
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Supplier details retrieved successfully

    put:
      tags:
        - Suppliers
      summary: Update supplier
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Supplier updated successfully

    delete:
      tags:
        - Suppliers
      summary: Delete supplier
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Supplier deleted successfully

  /v1/suppliers/sync:
    post:
      tags:
        - Suppliers
      summary: Sync suppliers
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Suppliers synchronized successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  lastSyncedAt:
                    type: string
                    format: date-time
                    example: '2024-11-05T01:09:37.689Z'

  /v1/suppliers/{id}/comments:
    post:
      tags:
        - Suppliers
      summary: Create supplier comment
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SupplierComment'
      responses:
        '200':
          description: Supplier comment created successfully

    get:
      tags:
        - Suppliers
      summary: Get supplier comments
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
        - in: query
          name: commentDateFrom
          schema:
            type: string
            format: date-time
        - in: query
          name: commentDateTo
          schema:
            type: string
            format: date-time
      responses:
        '200':
          description: Supplier comments retrieved successfully

  /v1/suppliers/{id}/attachments:
    put:
      tags:
        - Suppliers
      summary: Update supplier attachments
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
          example: '1'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateSupplierAttachments'
      responses:
        '200':
          description: Supplier attachments updated successfully

    post:
      tags:
        - Suppliers
      summary: Create supplier attachments
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                attachments:
                  type: array
                  items:
                    type: string
                    format: binary
      responses:
        '200':
          description: Supplier attachments created successfully

    get:
      tags:
        - Suppliers
      summary: Get supplier attachments
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
        - in: query
          name: attachmentDateFrom
          schema:
            type: string
            format: date-time
        - in: query
          name: attachmentDateTo
          schema:
            type: string
            format: date-time
      responses:
        '200':
          description: Supplier attachments retrieved successfully

  # Company Routes
  /v1/companies:
    get:
      tags:
        - Companies
      summary: Get all companies
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: page
          schema:
            type: string
            default: 1
        - in: query
          name: limit
          schema:
            type: string
            default: 10
        - in: query
          name: paginate
          schema:
            type: string
            default: true
      responses:
        '200':
          description: List of companies retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          example: 3
                        code:
                          type: string
                          example: '12'
                        name:
                          type: string
                          example: 'A CITY & LAND DEVELOPERS, INCORPORATED'
                        initial:
                          type: string
                          example: 'CLDI'
                        tin:
                          type: string
                          example: '***********-000'
                        address:
                          type: string
                          example: 'test'
                        contactNumber:
                          type: string
                          example: '+639999999999'
                        createdAt:
                          type: string
                          format: date-time
                          example: '2024-10-31T07:39:32.817Z'
                        updatedAt:
                          type: string
                          format: date-time
                          example: '2024-11-05T01:09:20.240Z'
                  total:
                    type: integer
                    example: 1
                  lastSyncedAt:
                    type: string
                    format: date-time
                    nullable: true
                    example: '2024-11-05T01:09:37.689Z'
    post:
      tags:
        - Companies
      summary: Create a new company association
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - code
                - name
                - initial
                - tin
              properties:
                code:
                  type: integer
                  example: 151
                name:
                  type: string
                  example: 'Cityland Assoc 2'
                initial:
                  type: string
                  example: 'CA2'
                tin:
                  type: string
                  example: '1231234'
                areaCode:
                  type: string
                  example: 'AREA1'
                address:
                  type: string
                  nullable: true
                  example: 'Mandaluyong'
                contactNumber:
                  type: string
                  nullable: true
                  example: '+639999999999'
      responses:
        '201':
          description: Company created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: integer
                    example: 107
                  code:
                    type: integer
                    example: 151
                  name:
                    type: string
                    example: 'Cityland Assoc'
                  initial:
                    type: string
                    example: 'CA'
                  tin:
                    type: string
                    example: '123-123-1234'
                  address:
                    type: string
                    nullable: true
                    example: 'Mandaluyong'
                  contactNumber:
                    type: string
                    nullable: true
                    example: '+639999999999'
                  areaCode:
                    type: string
                    example: 'AREA1'
                  category:
                    type: string
                    example: 'association'
                  createdAt:
                    type: string
                    format: date-time
                    example: '2024-11-25T07:42:03.841Z'
                  updatedAt:
                    type: string
                    format: date-time
                    example: '2024-11-25T07:42:03.841Z'

  /v1/companies/sync:
    post:
      tags:
        - Companies
      summary: Sync companies
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Companies synchronized successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  lastSyncedAt:
                    type: string
                    format: date-time
                    example: '2024-11-05T01:09:37.689Z'

  /v1/companies/{companyId}:
    get:
      tags:
        - Companies
      summary: Get company details
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: companyId
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Company details retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: integer
                    example: 3
                  code:
                    type: string
                    example: '12'
                  name:
                    type: string
                    example: 'A CITY & LAND DEVELOPERS, INCORPORATED'
                  initial:
                    type: string
                    example: 'CLDI'
                  tin:
                    type: string
                    example: '***********-000'
                  address:
                    type: string
                    example: 'test'
                  contactNumber:
                    type: string
                    example: '+639999999999'
                  createdAt:
                    type: string
                    format: date-time
                    example: '2024-10-31T07:39:32.817Z'
                  updatedAt:
                    type: string
                    format: date-time
                    example: '2024-11-05T01:09:20.240Z'
                  area:
                    type: object
                    nullable: true
                    properties:
                      id:
                        type: integer
                        example: 24
                      code:
                        type: string
                        example: 'AREA1'
                      name:
                        type: string
                        example: 'Area 1'

    put:
      tags:
        - Companies
      summary: Update company details (association only)
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: companyId
          required: true
          schema:
            type: integer
          description: Company ID
          example: 107
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                code:
                  type: integer
                  example: 151
                name:
                  type: string
                  example: 'Cityland Assoc 2'
                initial:
                  type: string
                  example: 'CA2'
                tin:
                  type: string
                  example: '1231234'
                address:
                  type: string
                  nullable: true
                  example: 'Mandaluyong'
                contactNumber:
                  type: string
                  nullable: true
                  example: '+639999999999'
                areaCode:
                  type: string
                  example: 'AREA1'
              minProperties: 1
      responses:
        '200':
          description: Company updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: 'Company updated successfully'
    delete:
      tags:
        - Companies
      summary: Delete a company association
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: companyId
          required: true
          schema:
            type: integer
          description: ID of the company to delete
          example: 1
      responses:
        '200':
          description: Company deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: 'Company association deleted successfully'
        '404':
          description: Company not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 404
                  message:
                    type: string
                    example: 'Company not found'
                  errorCode:
                    type: string
                    example: 'NOT_FOUND'
                  timestamp:
                    type: string
                    format: date-time
                    example: '2024-12-09T08:15:30.000Z'

  # Project Routes
  /v1/projects:
    get:
      tags:
        - Projects
      summary: Get all projects
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: page
          schema:
            type: string
        - in: query
          name: limit
          schema:
            type: string
        - in: query
          name: paginate
          schema:
            type: string
            default: true
      responses:
        '200':
          description: List of projects retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          example: 8
                        code:
                          type: string
                          example: 'C74'
                        name:
                          type: string
                          example: 'CITY NORTH TOWER'
                        initial:
                          type: string
                          example: 'CNT'
                        address:
                          type: string
                          nullable: true
                          example: null
                        startDate:
                          type: string
                          format: date-time
                          nullable: true
                          example: null
                        endDate:
                          type: string
                          format: date-time
                          nullable: true
                          example: null
                        companyId:
                          type: integer
                          nullable: true
                          example: null
                        createdAt:
                          type: string
                          format: date-time
                          example: '2024-11-05T03:39:26.184Z'
                        updatedAt:
                          type: string
                          format: date-time
                          example: '2024-11-05T03:39:26.184Z'
                        modifier:
                          type: object
                          nullable: true
                          properties:
                            id:
                              type: integer
                              example: 24
                            firstName:
                              type: string
                              example: 'firstName'
                            lastName:
                              type: string
                              example: 'lastName'
                  total:
                    type: integer
                    example: 8
                  lastSyncedAt:
                    type: string
                    format: date-time
                    nullable: true
                    example: '2024-11-05T03:39:26.202Z'

  /v1/projects/sync:
    post:
      tags:
        - Projects
      summary: Sync projects
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Projects synchronized successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  lastSyncedAt:
                    type: string
                    format: date-time
                    example: '2024-11-05T01:09:37.689Z'

  /v1/projects/{projectId}:
    get:
      tags:
        - Projects
      summary: Get project details
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: projectId
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Project details retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: integer
                    example: 5
                  code:
                    type: string
                    example: 'C71'
                  name:
                    type: string
                    example: '101 XAVIERVILLE'
                  initial:
                    type: string
                    example: '101X'
                  startDate:
                    type: string
                    format: date-time
                    example: '2024-11-07T11:53:00.000Z'
                  endDate:
                    type: string
                    format: date-time
                    example: '2024-11-09T11:53:00.000Z'
                  createdAt:
                    type: string
                    format: date-time
                    example: '2024-11-05T03:39:26.184Z'
                  updatedAt:
                    type: string
                    format: date-time
                    example: '2024-11-07T05:35:31.742Z'
                  company:
                    type: object
                    properties:
                      id:
                        type: integer
                        example: 3
                      name:
                        type: string
                        example: 'A CITY & LAND DEVELOPERS, INCORPORATED'
                  modifier:
                    type: object
                    properties:
                      id:
                        type: integer
                        example: 1
                      firstName:
                        type: string
                        example: 'firstName'
                      lastName:
                        type: string
                        example: 'lastName'
    put:
      tags:
        - Projects
      summary: Update project
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: projectId
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateProjectRequest'
      responses:
        '200':
          description: Projects updated successfully

  /v1/projects/{projectId}/approvals:
    get:
      tags:
        - Projects
      summary: Get project approvals
      description: Retrieve the list of approvals for a specific project, including both mandatory and optional approvers.
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: projectId
          required: true
          schema:
            type: string
          description: Project ID
        - in: query
          name: type
          schema:
            type: string
            enum: [RS, CV, PO, PR]
          description: Filter by approval type code
      responses:
        '200':
          description: List of project approvals retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        type:
                          type: string
                          example: 'CV'
                        approvers:
                          type: array
                          items:
                            type: object
                            properties:
                              id:
                                type: integer
                                example: 5
                              projectId:
                                type: integer
                                example: 19
                              level:
                                type: integer
                                example: 1
                              createdAt:
                                type: string
                                format: date-time
                                example: '2024-11-27T06:57:05.321Z'
                              updatedAt:
                                type: string
                                format: date-time
                                example: '2024-11-27T06:57:05.321Z'
                              approver:
                                type: object
                                properties:
                                  id:
                                    type: integer
                                    example: 3
                                  firstName:
                                    type: string
                                    example: 'firstName'
                                  lastName:
                                    type: string
                                    example: 'lastName'
                                  role:
                                    type: object
                                    properties:
                                      id:
                                        type: integer
                                        example: 1
                                      name:
                                        type: string
                                        example: 'Supervisor'
                        optionalApprovers:
                          type: array
                          items:
                            type: object
                            properties:
                              id:
                                type: integer
                                example: 7
                              projectId:
                                type: integer
                                example: 19
                              level:
                                type: integer
                                example: 1
                              createdAt:
                                type: string
                                format: date-time
                                example: '2024-11-27T06:57:05.321Z'
                              updatedAt:
                                type: string
                                format: date-time
                                example: '2024-11-27T06:57:05.321Z'
                              approver:
                                type: object
                                properties:
                                  id:
                                    type: integer
                                    example: 2
                                  firstName:
                                    type: string
                                    example: 'firstName'
                                  lastName:
                                    type: string
                                    example: 'lastName'
                                  role:
                                    type: object
                                    properties:
                                      id:
                                        type: integer
                                        example: 1
                                      name:
                                        type: string
                                        example: 'Supervisor'
                  total:
                    type: integer
                    example: 4
    put:
      tags:
        - Projects
      summary: Setup project approvals
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: projectId
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - approvalTypeCode
                - approvers
              properties:
                approvalTypeCode:
                  type: string
                  example: 'RS'
                approvers:
                  type: array
                  items:
                    type: object
                    required:
                      - level
                      - approverId
                    properties:
                      level:
                        type: integer
                        example: 1
                      approverId:
                        type: integer
                        example: 3
                optionalApprovers:
                  type: array
                  items:
                    type: object
                    required:
                      - approverId
                    properties:
                      approverId:
                        type: integer
                        example: 5
      responses:
        '200':
          description: Project approvals setup successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    id:
                      type: integer
                      example: 43
                    projectId:
                      type: integer
                      example: 19
                    approvalTypeCode:
                      type: string
                      example: 'RS'
                    level:
                      type: integer
                      example: 1
                    approverId:
                      type: integer
                      example: 3
                    isOptional:
                      type: boolean
                      example: false
                    createdAt:
                      type: string
                      format: date-time
                      example: '2024-11-27T03:57:07.597Z'
                    updatedAt:
                      type: string
                      format: date-time
                      example: '2024-11-27T03:57:07.597Z'

  /v1/projects/trade-engineer/{projectTradeEngineerId}:
    delete:
      tags:
        - Projects
      summary: Remove engineer from project trade
      description: Delete an engineer assignment from a specific project trade
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: projectTradeEngineerId
          required: true
          schema:
            type: integer
          description: Project Trade Engineer ID to be removed
      responses:
        '200':
          description: Engineer successfully removed from project trade
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: 'Engineer deleted successfully'
        '404':
          description: Project Trade Engineer assignment not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 404
                  message:
                    type: string
                    example: 'Project Trade Engineer not found'
                  errorCode:
                    type: string
                    example: 'NOT_FOUND'
                  timestamp:
                    type: string
                    format: date-time
                    example: '2024-11-05T07:28:22.632Z'

  /v1/projects/{projectId}/trades/{tradeId}/engineers:
    get:
      tags:
        - Projects
      summary: Get project trade engineers
      description: Get all engineers assigned to the specified project trade
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: projectId
          required: true
          schema:
            type: integer
          description: Project ID
        - in: path
          name: tradeId
          required: true
          schema:
            type: integer
          description: Trade ID
      responses:
        '200':
          description: List of project trade engineers retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          example: 18
                        createdAt:
                          type: string
                          format: date-time
                          example: '2024-12-03T07:12:12.399Z'
                        updatedAt:
                          type: string
                          format: date-time
                          example: '2024-12-03T07:12:12.399Z'
                        engineer:
                          type: object
                          properties:
                            id:
                              type: integer
                              example: 13
                            firstName:
                              type: string
                              example: 'firstName'
                            lastName:
                              type: string
                              example: 'lastName'
                  total:
                    type: integer
                    example: 1
        '404':
          description: Project or Trade not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 404
                  message:
                    type: string
                    example: 'Project or Trade not found'
                  errorCode:
                    type: string
                    example: 'NOT_FOUND'
                  timestamp:
                    type: string
                    format: date-time
                    example: '2024-11-05T07:28:22.632Z'

    post:
      tags:
        - Projects
      summary: Assign engineers to project trade
      description: Assign one or more engineers to a specific project trade
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: projectId
          required: true
          schema:
            type: integer
          description: Project ID
        - in: path
          name: tradeId
          required: true
          schema:
            type: integer
          description: Trade ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - engineerIds
              properties:
                engineerIds:
                  type: array
                  items:
                    type: integer
                  example: [13]
                  description: Array of engineer IDs to assign
      responses:
        '200':
          description: Engineers successfully assigned
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: 'Engineers assigned successfully'
        '404':
          description: Project, Trade, or Engineer not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 404
                  message:
                    type: string
                    example: 'Project, Trade, or Engineer not found'
                  errorCode:
                    type: string
                    example: 'NOT_FOUND'
                  timestamp:
                    type: string
                    format: date-time
                    example: '2024-11-05T07:28:22.632Z'

  /v1/projects/{projectId}/trades/{tradeId}/available-engineers:
    get:
      tags:
        - Projects
      summary: Get available engineers for project trade
      description: Get all engineers that haven't been assigned to the specified project trade
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: projectId
          required: true
          schema:
            type: integer
          description: Project ID
        - in: path
          name: tradeId
          required: true
          schema:
            type: integer
          description: Trade ID
      responses:
        '200':
          description: List of available engineers retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          example: 8
                        firstName:
                          type: string
                          example: 'firstName'
                        lastName:
                          type: string
                          example: 'lastName'
                  total:
                    type: integer
                    example: 1
        '404':
          description: Project or Trade not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: integer
                    example: 404
                  message:
                    type: string
                    example: 'Project or Trade not found'
                  errorCode:
                    type: string
                    example: 'NOT_FOUND'
                  timestamp:
                    type: string
                    format: date-time
                    example: '2024-11-05T07:28:22.632Z'

  # Approver Routes
  /v1/approvals/types:
    get:
      tags:
        - Approvals
      summary: Get all approval types
      security:
        - bearerAuth: []
      responses:
        '200':
          description: List of approval types retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          example: 1
                        name:
                          type: string
                          example: 'Requisition Slip'
                        code:
                          type: string
                          example: 'RS'
                        createdAt:
                          type: string
                          format: date-time
                          example: '2024-11-26T06:30:38.457Z'
                        updatedAt:
                          type: string
                          format: date-time
                          example: '2024-11-26T06:30:38.457Z'
                  total:
                    type: integer
                    example: 4

  /v1/approvals/areas:
    get:
      tags:
        - Approvals
      summary: Get all approval areas
      security:
        - bearerAuth: []
      responses:
        '200':
          description: List of approval areas retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          example: 1
                        code:
                          type: string
                          example: 'AREA1'
                        name:
                          type: string
                          example: 'Area 1'
                  total:
                    type: integer
                    example: 4

  # Department Routes
  /v1/departments:
    get:
      tags:
        - Departments
      summary: Get all departments
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: page
          schema:
            type: string
        - in: query
          name: limit
          schema:
            type: string
        - in: query
          name: paginate
          schema:
            type: string
            default: true
      responses:
        '200':
          description: List of departments retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          example: 6
                        code:
                          type: integer
                          example: 5
                        name:
                          type: string
                          example: 'ADMINISTRATION'
                        createdAt:
                          type: string
                          format: date-time
                          example: '2024-11-15T01:40:28.170Z'
                        updatedAt:
                          type: string
                          format: date-time
                          example: '2024-11-15T01:40:28.170Z'
                        manager:
                          type: object
                          nullable: true
                          properties:
                            id:
                              type: integer
                              example: 5
                            firstName:
                              type: string
                              example: 'firstName'
                            lastName:
                              type: string
                              example: 'lastName'
                        assistantManager:
                          type: object
                          nullable: true
                          properties:
                            id:
                              type: integer
                              example: 4
                            firstName:
                              type: string
                              example: 'firstName'
                            lastName:
                              type: string
                              example: 'lastName'
                        departmentDivisionHead:
                          type: object
                          nullable: true
                          properties:
                            id:
                              type: integer
                              example: 15
                            firstName:
                              type: string
                              example: 'firstName'
                            lastName:
                              type: string
                              example: 'lastName'
                  total:
                    type: integer
                    example: 42
                  lastSyncedAt:
                    type: string
                    format: date-time
                    example: '2024-11-20T06:36:02.249Z'

  /v1/departments/{id}:
    get:
      tags:
        - Departments
      summary: Get department by ID
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Department details retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: integer
                    example: 6
                  code:
                    type: integer
                    example: 5
                  name:
                    type: string
                    example: 'ADMINISTRATION'
                  createdAt:
                    type: string
                    format: date-time
                    example: '2024-11-15T01:40:28.170Z'
                  updatedAt:
                    type: string
                    format: date-time
                    example: '2024-11-25T05:11:37.097Z'
                  manager:
                    type: object
                    nullable: true
                    properties:
                      id:
                        type: integer
                        example: 5
                      firstName:
                        type: string
                        example: 'firstName'
                      lastName:
                        type: string
                        example: 'lastName'
                  assistantManager:
                    type: object
                    nullable: true
                    properties:
                      id:
                        type: integer
                        example: 4
                      firstName:
                        type: string
                        example: 'firstName'
                      lastName:
                        type: string
                        example: 'lastName'
                  departmentDivisionHead:
                    type: object
                    nullable: true
                    properties:
                      id:
                        type: integer
                        example: 15
                      firstName:
                        type: string
                        example: 'firstName'
                      lastName:
                        type: string
                        example: 'lastName'

  /v1/departments/sync:
    post:
      tags:
        - Departments
      summary: Sync departments
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Departments synchronized successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  lastSyncedAt:
                    type: string
                    format: date-time
                    example: '2024-11-05T01:09:37.689Z'

  /v1/departments/association-approvals:
    get:
      tags:
        - Departments
      summary: Get department association approvals
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: approvalTypeCode
          schema:
            type: string
            example: 'RS'
      responses:
        '200':
          description: Department association approvals retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        type:
                          type: string
                          example: 'RS'
                        approvers:
                          type: array
                          items:
                            type: object
                            properties:
                              id:
                                type: integer
                                example: 6
                              level:
                                type: integer
                                example: 1
                              createdAt:
                                type: string
                                format: date-time
                                example: '2024-12-09T07:22:54.571Z'
                              updatedAt:
                                type: string
                                format: date-time
                                example: '2024-12-09T07:22:54.571Z'
                              approver:
                                type: object
                                properties:
                                  id:
                                    type: integer
                                    example: 20
                                  firstName:
                                    type: string
                                    example: 'firstName'
                                  lastName:
                                    type: string
                                    example: 'lastName'
                                  role:
                                    type: object
                                    properties:
                                      id:
                                        type: integer
                                        example: 11
                                      name:
                                        type: string
                                        example: 'Area Staff'
                              area:
                                type: object
                                properties:
                                  id:
                                    type: integer
                                    example: 1
                                  code:
                                    type: string
                                    example: 'AREA1'
                                  name:
                                    type: string
                                    example: 'Area 1'
                  total:
                    type: integer
                    example: 4
    put:
      tags:
        - Departments
      summary: Setup department association approvals
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - approvalTypeCode
                - approvers
              properties:
                approvalTypeCode:
                  type: string
                  example: 'RS'
                approvers:
                  type: array
                  items:
                    type: object
                    required:
                      - level
                      - approverId
                    properties:
                      level:
                        type: integer
                        example: 1
                        description: 'Level 1 requires areaCode, other levels do not'
                      approverId:
                        type: integer
                        example: 20
                      areaCode:
                        type: string
                        example: 'AREA1'
                        description: 'Required for level 1 approvers only'
              example:
                approvalTypeCode: 'RS'
                approvers:
                  [
                    { 'level': 1, 'approverId': 20, 'areaCode': 'AREA1' },
                    { 'level': 1, 'approverId': 22, 'areaCode': 'AREA2' },
                    { 'level': 2, 'approverId': 14 },
                  ]
      responses:
        '200':
          description: Department association approvals updated successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    id:
                      type: integer
                      example: 10
                    approvalTypeCode:
                      type: string
                      example: 'RS'
                    level:
                      type: integer
                      example: 1
                    approverId:
                      type: integer
                      example: 20
                    areaCode:
                      type: string
                      nullable: true
                      example: 'AREA1'
                    createdAt:
                      type: string
                      format: date-time
                      example: '2024-12-09T08:16:50.901Z'
                    updatedAt:
                      type: string
                      format: date-time
                      example: '2024-12-09T08:16:50.901Z'

  /v1/departments/{id}/approvals:
    get:
      tags:
        - Departments
      summary: Get department approvals
      description: Retrieve the list of approvals for a specific department, including both mandatory and optional approvers.
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
          description: Department ID
        - in: query
          name: type
          schema:
            type: string
            enum: [RS, CV, PO, PR]
          description: Filter by approval type code
      responses:
        '200':
          description: List of department approvals retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        type:
                          type: string
                          example: 'CV'
                        approvers:
                          type: array
                          items:
                            type: object
                            properties:
                              id:
                                type: integer
                                example: 5
                              departmentId:
                                type: integer
                                example: 19
                              level:
                                type: integer
                                example: 1
                              createdAt:
                                type: string
                                format: date-time
                                example: '2024-11-27T06:57:05.321Z'
                              updatedAt:
                                type: string
                                format: date-time
                                example: '2024-11-27T06:57:05.321Z'
                              approver:
                                type: object
                                properties:
                                  id:
                                    type: integer
                                    example: 3
                                  firstName:
                                    type: string
                                    example: 'firstName'
                                  lastName:
                                    type: string
                                    example: 'lastName'
                                  role:
                                    type: object
                                    properties:
                                      id:
                                        type: integer
                                        example: 1
                                      name:
                                        type: string
                                        example: 'Supervisor'
                        optionalApprovers:
                          type: array
                          items:
                            type: object
                            properties:
                              id:
                                type: integer
                                example: 7
                              departmentId:
                                type: integer
                                example: 19
                              level:
                                type: integer
                                example: 1
                              createdAt:
                                type: string
                                format: date-time
                                example: '2024-11-27T06:57:05.321Z'
                              updatedAt:
                                type: string
                                format: date-time
                                example: '2024-11-27T06:57:05.321Z'
                              approver:
                                type: object
                                properties:
                                  id:
                                    type: integer
                                    example: 2
                                  firstName:
                                    type: string
                                    example: 'firstName'
                                  lastName:
                                    type: string
                                    example: 'lastName'
                                  role:
                                    type: object
                                    properties:
                                      id:
                                        type: integer
                                        example: 1
                                      name:
                                        type: string
                                        example: 'Supervisor'
                  total:
                    type: integer
                    example: 4
    put:
      tags:
        - Departments
      summary: Setup department approvals
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - approvalTypeCode
                - approvers
              properties:
                approvalTypeCode:
                  type: string
                  example: 'RS'
                approvers:
                  type: array
                  items:
                    type: object
                    required:
                      - level
                      - approverId
                    properties:
                      level:
                        type: integer
                        example: 1
                      approverId:
                        type: integer
                        example: 3
                optionalApprovers:
                  type: array
                  items:
                    type: object
                    required:
                      - approverId
                    properties:
                      approverId:
                        type: integer
                        example: 5
      responses:
        '200':
          description: Department approvals setup successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    id:
                      type: integer
                      example: 43
                    departmentId:
                      type: integer
                      example: 19
                    approvalTypeCode:
                      type: string
                      example: 'RS'
                    level:
                      type: integer
                      example: 1
                    approverId:
                      type: integer
                      example: 3
                    isOptional:
                      type: boolean
                      example: false
                    createdAt:
                      type: string
                      format: date-time
                      example: '2024-11-27T03:57:07.597Z'
                    updatedAt:
                      type: string
                      format: date-time
                      example: '2024-11-27T03:57:07.597Z'

  # Requisition Routes
  /v1/requisitions:
    # Create requisition
    post:
      tags:
        - Requisitions
      summary: Create requisition
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/CreateRequisitionRequest'
      responses:
        '201':
          description: Requisition created successfully
          content:
            multipart/form-data:
              schema:
                type: object
                properties:
                  id:
                    type: integer
                    example: 46
                  type:
                    type: string
                    example: 'ofm'
                  companyId:
                    type: integer
                    example: 27
                  projectId:
                    type: integer
                    example: 1
                  departmentId:
                    type: integer
                    example: 6
                  dateRequired:
                    type: string
                    format: date-time
                    example: '2024-01-01T00:00:00.000Z'
                  purpose:
                    type: string
                    example: 'Purchase of Materials'
                  chargeTo:
                    type: string
                    example: 'project'
                  chargeToId:
                    type: integer
                    example: 1
                  draftRsNumber:
                    type: string
                    example: '00000010'
                  rsLetter:
                    type: string
                    example: 'AA'
                  companyCode:
                    type: string
                    example: '27'
                  createdBy:
                    type: integer
                    example: 48
                  deliveryAddress:
                    type: string
                    example: 'Tondo, Manila'
                  status:
                    type: string
                    example: 'draft'
                  rsNumber:
                    type: string
                    nullable: true
                    example: null
                  assignedTo:
                    type: integer
                    nullable: true
                    example: null
                  comment:
                    type: string
                    minLength: 1
                    example: 'comment'
                  itemList:
                    type: array
                    items:
                      type: object
                      required:
                        - itemId
                        - quantity
                        - notes
                      properties:
                        itemId:
                          type: integer
                        quantity:
                          type: integer
                        notes:
                          type: string
                        accountCode:
                          type: string
                    example:
                      - itemId: 580
                        quantity: 2
                        notes: note 1
                        accountCode: 123
                      - itemId: 581
                        quantity: 3
                        notes: note 2
                        accountCode: 456
                  attachments:
                    type: array
                    items:
                      type: string
                      format: binary

  # Requisition Item List Routes
  /v1/requisitions/{requisitionId}/items:
    post:
      tags:
        - Requisitions
      summary: Create requisition item list
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: requisitionId
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateRequisitionItemListRequest'
      responses:
        '201':
          description: Requisition item list created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  createdAt:
                    type: string
                    format: date-time
                    example: '2024-11-27T03:38:16.105Z'
                  updatedAt:
                    type: string
                    format: date-time
                    example: '2024-11-27T03:38:16.105Z'
                  id:
                    type: integer
                    example: 9
                  ofmItemId:
                    type: integer
                    example: 1
                  quantity:
                    type: integer
                    example: 1
                  notes:
                    type: string
                    example: 'notes'

  # Submit requisition
  /v1/requisitions/{requisitionId}/submit:
    post:
      tags:
        - Requisitions
      summary: Submit requisition
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: requisitionId
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Requisition submitted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: 'Requisition with id <requisitionId> submitted successfully'

  # get requisition by id
  /v1/requisitions/{requisitionId}:
    put:
      tags:
        - Requisitions
      summary: Update Requisition
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateRequisitionSchema'
      responses:
        '200':
          description: Requisition updated successfully

    get:
      tags:
        - Requisitions
      summary: Get requisition by ID
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: requisitionId
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Requisition details retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  requisition:
                    type: object
                    properties:
                      id:
                        type: integer
                        example: 2
                      rsNumber:
                        type: string
                        example: '00000001'
                      rsLetter:
                        type: string
                        example: 'AA'
                      companyCode:
                        type: string
                        example: '01'
                      createdBy:
                        type: integer
                        example: 1
                      companyId:
                        type: integer
                        example: 1
                      departmentId:
                        type: integer
                        example: 1
                      projectId:
                        type: integer
                        nullable: true
                        example: null
                      dateRequired:
                        type: string
                        format: date-time
                        example: '2024-01-01T00:00:00.000Z'
                      deliveryAddress:
                        type: string
                        example: 'Tondo, Manila'
                      purpose:
                        type: string
                        example: 'Purchase of Materials'
                      chargeTo:
                        type: string
                        example: 'Project 1'
                      status:
                        type: string
                        example: 'submitted'
                      assignedTo:
                        type: integer
                        nullable: true
                        example: null
                      type:
                        type: string
                        example: 'ofm'
                      createdAt:
                        type: string
                        format: date-time
                        example: '2024-11-27T02:09:46.575Z'
                      updatedAt:
                        type: string
                        format: date-time
                        example: '2024-11-27T02:09:46.575Z'
                      requisitionItemLists:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: integer
                              example: 9
                            requisitionId:
                              type: integer
                              example: 2
                            ofmItemId:
                              type: integer
                              example: 556
                            quantity:
                              type: integer
                              example: 1
                            notes:
                              type: string
                              example: 'test'
                            createdAt:
                              type: string
                              format: date-time
                              example: '2024-11-27T03:38:16.105Z'
                            updatedAt:
                              type: string
                              format: date-time
                              example: '2024-11-27T03:38:16.105Z'
                            item:
                              type: object
                              properties:
                                id:
                                  type: integer
                                  example: 556
                                itemCd:
                                  type: string
                                  example: 'BI001SU5'
                                itmDes:
                                  type: string
                                  example: 'BI PIPES SCH 40 (1") 25 MM X 6M SUPREME'
                                unit:
                                  type: string
                                  example: 'PCS.'
                                acctCd:
                                  type: string
                                  example: '2018C650015308'
                                gfq:
                                  type: integer
                                  example: 16
                                tradeCode:
                                  type: integer
                                  example: 15
                                createdAt:
                                  type: string
                                  format: date-time
                                  example: '2024-11-26T11:21:09.951Z'
                                updatedAt:
                                  type: string
                                  format: date-time
                                  example: '2024-11-26T11:21:09.951Z'
                                trade_code:
                                  type: integer
                                  example: 15

  # remove requisition item list
  /v1/requisitions/requisition-item-lists/{id}:
    delete:
      tags:
        - Requisitions
      summary: Delete requisition item list
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Requisition item list deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: 'Requisition item list with id <id> deleted successfully'

  # get delivery addresses
  /v1/requisitions/delivery-addresses:
    get:
      tags:
        - Requisitions
      summary: Get delivery addresses
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Delivery addresses retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  companies:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          example: 1
                        address:
                          type: string
                          example: 'Tondo, Manila'
                  projects:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          example: 1
                        address:
                          type: string
                          example: 'Tondo, Manila'

  # get charge to list
  /v1/requisitions/charge-to-lists:
    get:
      tags:
        - Requisitions
      summary: Get charge to list
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: category
          schema:
            type: string
            enum:
              - company
              - supplier
              - association
              - project
      responses:
        '200':
          description: Charge to list retrieved successfully

  # create requisition tom item list
  /v1/requisitions/{requisitionId}/tom-item-list:
    post:
      tags:
        - Requisitions
      summary: Create requisition tom item list
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: requisitionId
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateRequisitionTomItemListRequest'
      responses:
        '200':
          description: Tom item created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  tomItem:
                    type: object
                    properties:
                      id:
                        type: integer
                        example: 4
                      name:
                        type: string
                        example: 'Wire'
                      quantity:
                        type: integer
                        example: 1
                      unit:
                        type: string
                        example: 'yard'
                      comment:
                        type: string
                        example: 'test'
                      requisitionId:
                        type: integer
                        example: 2
                      updatedAt:
                        type: string
                        format: date-time
                        example: '2024-11-28T10:12:23.557Z'
                      createdAt:
                        type: string
                        format: date-time
                        example: '2024-11-28T10:12:23.557Z'

  # delete requisition tom item
  /v1/requisitions/tom-items/{id}:
    delete:
      tags:
        - Requisitions
      summary: Delete requisition tom item
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Tom item deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: 'Tom item with id {id} deleted successfully'

    # Requisition: Uploading attachments
  /v1/requisitions/{id}/attachments:
    post:
      tags:
        - Requisitions
      summary: Create requisition attachments
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                attachments:
                  type: array
                  items:
                    type: string
                    format: binary
      responses:
        '200':
          description: Requisition attachments created successfully
    get:
      tags:
        - Requisitions
      summary: Get requisition attachments
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
        - in: query
          name: attachmentDateFrom
          schema:
            type: string
            format: date-time
        - in: query
          name: attachmentDateTo
          schema:
            type: string
            format: date-time
      responses:
        '200':
          description: Requisition attachments retrieved successfully

    # Requisition: Uploading comments
  /v1/requisitions/{id}/comments:
    post:
      tags:
        - Requisitions
      summary: Create requisition comments
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RequisitionComment'
      responses:
        '200':
          description: Requisition comments created successfully
    get:
      tags:
        - Requisitions
      summary: Get requisition comments
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
        - in: query
          name: commentDateFrom
          schema:
            type: string
            format: date-time
        - in: query
          name: commentDateTo
          schema:
            type: string
            format: date-time
      responses:
        '200':
          description: Requisition comments retrieved successfully

  # approve requisition
  /v1/requisitions/{requisitionId}/approve:
    put:
      tags:
        - Requisitions
      summary: Approve requisition
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: requisitionId
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Requisition approved successfully

  # reject requisition
  /v1/requisitions/{requisitionId}/reject:
    put:
      tags:
        - Requisitions
      summary: Reject requisition
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RejectRequisitionSchema'
      responses:
        '200':
          description: Requisition rejected successfully

  #Item history
  /v1/items/history/{id}:
    get:
      tags:
        - Items
      summary: Get Item History
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
          description: Item ID
        - in: query
          name: page
          schema:
            type: string
            default: 1
        - in: query
          name: limit
          schema:
            type: string
            default: 10
        - in: query
          name: paginate
          schema:
            type: string
            default: true
      responses:
        '200':
          description: List of requisition histories for the item

  /v1/delivery-receipts:
    post:
      tags:
        - Delivery Receipts
      summary: Create delivery receipt
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/CreateDeliveryReceiptRequest'
      responses:
        '201':
          description: Delivery receipt created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeliveryReceiptResponse'
        '404':
          description: Requisition not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClientErrorResponse'
              example:
                status: 404
                message: 'Requisition with id {id} not found.'
                errorCode: 'NOT_FOUND'
                timestamp: '2024-11-05T07:28:22.632Z'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClientErrorResponse'
              example:
                status: 422
                message: '{errorMessage}'
                errorCode: 'UNPROCESSABLE_ENTITY'
                timestamp: '2024-11-05T07:28:22.632Z'

  /v1/delivery-receipts/{id}:
    get:
      tags:
        - Delivery Receipts
      summary: Get delivery receipt by ID
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Delivery receipt retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeliveryReceiptResponse'
        '404':
          description: Delivery receipt not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClientErrorResponse'
              example:
                status: 404
                message: 'Delivery receipt with id {id} not found.'
                errorCode: 'NOT_FOUND'
                timestamp: '2024-11-05T07:28:22.632Z'
    put:
      tags:
        - Delivery Receipts
      summary: Update delivery receipt by ID
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          multipart/form-data::
            schema:
              $ref: '#/components/schemas/UpdateDeliveryReceiptRequest'
      responses:
        '200':
          description: Delivery receipt updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeliveryReceiptResponse'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClientErrorResponse'
              example:
                status: 400
                message: '{errorMessage}'
                errorCode: 'BAD_REQUEST'
                timestamp: '2024-11-05T07:28:22.632Z'
        '404':
          description: Delivery receipt not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClientErrorResponse'
              example:
                status: 404
                message: 'Delivery receipt with id {id} not found.'
                errorCode: 'NOT_FOUND'
                timestamp: '2024-11-05T07:28:22.632Z'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClientErrorResponse'
              example:
                status: 422
                message: '{errorMessage}'
                errorCode: 'UNPROCESSABLE_ENTITY'
                timestamp: '2024-11-05T07:28:22.632Z'

  /v1/delivery-receipts/{id}/items/{itemId}/history:
    get:
      tags:
        - Delivery Receipts
      summary: Get delivery receipt item's history
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: string
        - in: path
          name: itemId
          required: true
          schema:
            type: string
        - in: query
          name: page
          schema:
            type: integer
            default: 1
        - in: query
          name: limit
          schema:
            type: integer
            default: 10
      responses:
        '200':
          description: Delivery receipt item history retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  start:
                    type: integer
                    example: 1
                  end:
                    type: integer
                    example: 10
                  total:
                    type: integer
                    example: 20
                  rows:
                    type: array
                    items:
                      type: object
                      properties:
                        createdAt:
                          type: string
                          format: date-time
                          example: '2024-11-05T07:28:22.632Z'
                        qtyOrdered:
                          type: integer
                          example: 100
                        qtyDelivered:
                          type: integer
                          example: 50
                        dateDelivered:
                          type: string
                          format: date
                          example: '2024-11-05'
                        status:
                          type: string
                          enum:
                            - Fully Delivered
                            - Partially Delivered
                          example: 'Fully Delivered'
        '404':
          description: Delivery receipt item not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClientErrorResponse'
              example:
                status: 404
                message: 'Delivery receipt item with id {id} not found.'
                errorCode: 'NOT_FOUND'
                timestamp: '2024-11-05T07:28:22.632Z'

  /v1/mock-purchase-orders:
    get:
      tags:
        - Mock Purchase Orders
      summary: Get mock purchase orders
      security:
        - bearerAuth: []
      responses:
        '200':
          description: List of mock purchase orders
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    id:
                      type: integer
                      example: 1
                    name:
                      type: string
                      example: 'PO-2024-001'

  /v1/mock-purchase-orders/{id}:
    get:
      tags:
        - Mock Purchase Orders
      summary: Get mock purchase order by ID
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Mock purchase order retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetMockPurchaseOrderByIdResponse'
        '404':
          description: Mock purchase order not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClientErrorResponse'
              example:
                status: 404
                message: 'Mock purchase order with id {id} not found.'
                errorCode: 'NOT_FOUND'
                timestamp: '2024-11-05T07:28:22.632Z'

  /v1/leaves:
    get:
      parameters:
        - in: query
          name: page
          schema:
            default: 1
            type: string
        - in: query
          name: limit
          schema:
            default: 10
            type: string
        - in: query
          name: paginate
          schema:
            default: true
            type: string
      responses:
        '200':
          description: List the Leave of Current User
      security:
        - bearerAuth: []
      summary: Get Leaves of Current User
      tags:
        - Leaves
    post:
      requestBody:
        content:
          application/json:
            schema:
              properties:
                endDate:
                  example: '2025-05-17'
                  type: string
                startDate:
                  example: '2025-05-13'
                  type: string
              required:
                - startDate
                - endDate
              type: object
        required: true
      responses:
        '200':
          description: Created Leave Case successfully
      security:
        - bearerAuth: []
      summary: Create Leave Case
      tags:
        - Leaves

  /v1/leaves/alt-approver:
    put:
      requestBody:
        content:
          application/json:
            schema:
              properties:
                id:
                  description: Approver Data ID
                  example: 1
                  type: integer
                toChange:
                  description:
                    Please provide toChange Value = requisition, canvass,
                    po, delivery
                  example: requisition
                  type: string
                userId:
                  description: User ID you want to be Alternate Approver
                  example: 2
                  type: integer
              type: object
      responses:
        '200':
          description: Added Alternate Approver
      security:
        - bearerAuth: []
      summary: Add Alternate Approver
      tags:
        - Leaves

  /v1/leaves/{id}:
    delete:
      parameters:
        - in: path
          name: id
          required: true
          schema:
            oneOf:
              - type: string
              - type: integer
      responses:
        '200':
          description: Deleted Leave Case successfully
      security:
        - bearerAuth: []
      summary: Delete Leave Case
      tags:
        - Leaves
    get:
      parameters:
        - in: path
          name: id
          required: true
          schema:
            oneOf:
              - type: string
              - type: integer
      responses:
        '200':
          description: Retrieved Leave Case successfully including affected work flow
      security:
        - bearerAuth: []
      summary: Get Leave Case with Affected Work Flow
      tags:
        - Leaves
    put:
      parameters:
        - in: path
          name: id
          required: true
          schema:
            oneOf:
              - type: string
              - type: integer
      requestBody:
        content:
          application/json:
            schema:
              properties:
                endDate:
                  example: '2025-05-19'
                  type: string
                startDate:
                  example: '2025-05-13'
                  type: string
              type: object
      responses:
        '200':
          description: Edited Leave Case successfully
      security:
        - bearerAuth: []
      summary: Edit Leave Case
      tags:
        - Leaves

  /v1/rs-payment-request:
    post:
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                purchaseOrderId:
                  type: integer
                  example: 1
                terms:
                  type: string
                  enum:
                    [
                      'COD',
                      '10% DP, Balance upon delivery',
                      '20% DP, Balance upon delivery',
                      '30% DP, Balance upon delivery',
                      '50% DP, Balance upon delivery',
                      '80% DP, Balance upon delivery',
                      '10% DP, PB, 10% RETENTION',
                      '20% DP, PB, 10% RETENTION',
                      '30% DP, PB, 10% RETENTION',
                      'CIA',
                      'NET 15',
                      'NET 30',
                    ]
                  example: '80% DP, Balance upon delivery'
                isDraft:
                  type: boolean
                  example: true
                requisitionId:
                  type: integer
                  example: 1
                payableDate:
                  type: string
                  example: '2025-08-25'
                discountIn:
                  type: string
                  enum: ['Percentage', 'Fixed Amount']
                  example: 'Fixed Amount'
                discountAmount:
                  type: number
                  multipleOf: 0.01
                  example: 10.00
                withholdingTax:
                  type: number
                  multipleOf: 0.01
                  example: 10.00
                tip:
                  type: number
                  multipleOf: 0.01
                  example: 10.00
                deliveryFee:
                  type: number
                  multipleOf: 0.01
                  example: 10.00
                extraCharges:
                  type: number
                  multipleOf: 0.01
                  example: 10.00
                deliveryInvoiceId:
                  type: integer
                  example: 1
                comments:
                  type: string
                  example: i am a comment
                attachments:
                  type: array
                  items:
                    type: string
                    format: binary

      responses:
        '201':
          description: Payment Request created successfully
      security:
        - bearerAuth: []
      summary: Create Payment Request
      tags:
        - RS Payment Request

  /v1/rs-payment-request/po-details/{purchaseOrderId}:
    get:
      parameters:
        - in: path
          name: purchaseOrderId
          required: true
          schema:
            oneOf:
              - type: string
                example: '1'
              - type: integer
                example: 1
      responses:
        '200':
          description: Retrieved Purchase Order Details
      security:
        - bearerAuth: []
      summary: Get Purchase Order details for RS Payment Request
      tags:
        - RS Payment Request

  /v1/rs-payment-request/submit:
    put:
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                id:
                  type: integer
                  example: 1
                purchaseOrderId:
                  type: integer
                  example: 1
                terms:
                  type: string
                  enum:
                    [
                      'COD',
                      '10% DP, Balance upon delivery',
                      '20% DP, Balance upon delivery',
                      '30% DP, Balance upon delivery',
                      '50% DP, Balance upon delivery',
                      '80% DP, Balance upon delivery',
                      '10% DP, PB, 10% RETENTION',
                      '20% DP, PB, 10% RETENTION',
                      '30% DP, PB, 10% RETENTION',
                      'CIA',
                      'NET 15',
                      'NET 30',
                    ]
                  example: '80% DP, Balance upon delivery'
                isDraft:
                  type: boolean
                  example: false
                requisitionId:
                  type: integer
                  example: 1
                payableDate:
                  type: string
                  example: '2025-08-25'
                discountIn:
                  type: string
                  enum: ['Percentage', 'Fixed Amount']
                  example: 'Fixed Amount'
                discountAmount:
                  type: number
                  multipleOf: 0.01
                  example: 10.00
                withholdingTax:
                  type: number
                  multipleOf: 0.01
                  example: 10.00
                tip:
                  type: number
                  multipleOf: 0.01
                  example: 10.00
                deliveryFee:
                  type: number
                  multipleOf: 0.01
                  example: 10.00
                extraCharges:
                  type: number
                  multipleOf: 0.01
                  example: 10.00
                deliveryInvoiceId:
                  type: integer
                  example: 1
                comments:
                  type: string
                  example: i am a comment
                attachments:
                  type: array
                  items:
                    type: string
                    format: binary

      responses:
        '201':
          description: Payment Request created successfully/submitted
      security:
        - bearerAuth: []
      summary: Create Payment Request/submit draft
      tags:
        - RS Payment Request

  /v1/rs-payment-request/po-lists:
    get:
      responses:
        '200':
          description: Retrieved Purchase Order Lists with Delivery Status of Fully Delivered
      security:
        - bearerAuth: []
      summary: Retrieved Purchase Order Lists with Delivery Status of Fully Delivered
      tags:
        - RS Payment Request
  /v1/download/dashboard:
    get:
      parameters:
        - in: query
          name: requestType
          schema:
            default: null
            enum: ['my_approval', 'my_request']
            type: string
      responses:
        '200':
          description: Download Dashboard Data
      security:
        - bearerAuth: []
      summary: Download Dashboard data (All, My Approvals, My Requests)
      tags:
        - Download

  /v1/requisitions/{requisitionsId}/approver/{id}:
    put:
      parameters:
        - in: path
          name: requisitionId
          required: true
          schema:
            oneOf:
              - type: string
              - type: integer
        - in: path
          name: id
          required: true
          schema:
            oneOf:
              - type: string
              - type: integer
      requestBody:
        content:
          application/json:
            schema:
              properties:
                approverId:
                  nullable: true
                  example: 1
                  oneOf:
                    - type: string
                    - type: integer
                altApproverId:
                  nullable: true
                  example: 1
                  oneOf:
                    - type: string
                    - type: integer
              type: object
      responses:
        '200':
          description: Edited Requisition Approver
      security:
        - bearerAuth: []
      summary: Edit Requisition Approver
      tags:
        - Requisitions
