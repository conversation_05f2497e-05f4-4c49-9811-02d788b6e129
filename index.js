const container = require('./src/container');

const app = container.resolve('server');
const fastify = container.resolve('fastify');
const puppeteerBrowser = container.resolve('puppeteerBrowser');

// Initialize the browser before starting the server
(async () => {
  try {
    console.log('Starting application initialization...');

    // Pre-initialize the browser
    console.log('Initializing Puppeteer browser...');
    await puppeteerBrowser.getInstance();
    console.log('Puppeteer browser initialized successfully');

    // Start the server after browser is ready
    console.log('Starting Fastify server...');
    await app.startFastify();
    console.log('Application startup completed successfully');
  } catch (error) {
    console.error(`Error during startup: ${error}`);
    fastify.log.error(`Error: ${error}`);
    process.exit(1);
  }
})();
