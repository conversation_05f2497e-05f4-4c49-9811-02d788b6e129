/**
 * Force Close Acceptance Criteria Validation Tests
 * Tests to verify the implementation matches the acceptance criteria:
 *
 * Should have a Force Close Button
 *   a. Should update the Cancel Button to Force Close
 *     i. Only when the RS Status is RS In Progress and PO Status of For Delivery
 *   b. Should only be allowed when
 *     i. Created Purchase Order has been Partially Delivered, Invoiced, and Paid in Partially
 *     i) Payment must be made for all Delivered Items
 */

const {
  describe,
  it,
  expect,
  beforeEach,
  afterEach,
} = require('@jest/globals');

describe('Force Close Acceptance Criteria Validation', () => {
  let forceCloseService;
  let mockContainer;
  let mockRequisitionRepository;
  let mockPurchaseOrderRepository;
  let mockDeliveryReceiptRepository;
  let mockRsPaymentRequestRepository;

  beforeEach(() => {
    // Mock repositories and dependencies
    mockRequisitionRepository = {
      getById: jest.fn(),
    };

    mockPurchaseOrderRepository = {
      findAll: jest.fn(),
    };

    mockDeliveryReceiptRepository = {
      findAll: jest.fn(),
    };

    mockRsPaymentRequestRepository = {
      findAll: jest.fn(),
    };

    mockContainer = {
      db: {
        deliveryReceiptItemModel: {
          findAll: jest.fn(),
        },
      },
      utils: {},
      entities: {},
      constants: {
        forceClose: {
          FORCE_CLOSE_BUTTON_RULES: {
            SHOW_WHEN_IN_PROGRESS: 'rs_in_progress',
            REQUIRED_PO_STATUS: 'for_delivery',
            REQUIRES_ACTIVE_PO_FOR_DELIVERY: true,
          },
          FORCE_CLOSE_ERRORS: {
            REQUISITION_NOT_FOUND: 'Requisition not found',
            RS_NOT_APPROVED: 'RS not approved',
            RS_STILL_CANCELLABLE: 'RS still cancellable',
            NO_DELIVERIES_YET: 'No deliveries yet',
            INVALID_PO_STATUS: 'Invalid PO status',
            UNPAID_DELIVERIES: 'Unpaid deliveries',
          },
          FORCE_CLOSE_DELIVERY_VALIDATION: {
            VALID_PO_STATUSES: [
              'for_delivery',
              'closed',
              'closed_po',
              'cancelled_po',
            ],
          },
          FORCE_CLOSE_PAYMENT_VALIDATION: {
            AMOUNT_TOLERANCE: 0.01,
            VALID_PR_STATUSES: ['Closed', 'APPROVED'],
          },
        },
        requisition: {
          REQUISITION_STATUS: {
            APPROVED: 'approved',
            RS_IN_PROGRESS: 'rs_in_progress',
          },
        },
      },
      clientErrors: {},
      serverErrors: {},
      fastify: {
        log: {
          info: jest.fn(),
          error: jest.fn(),
          warn: jest.fn(),
        },
      },
      requisitionRepository: mockRequisitionRepository,
      purchaseOrderRepository: mockPurchaseOrderRepository,
      deliveryReceiptRepository: mockDeliveryReceiptRepository,
      rsPaymentRequestRepository: mockRsPaymentRequestRepository,
      canvassRequisitionRepository: {},
      itemRepository: {},
      commentRepository: {},
      historyRepository: {},
      notificationService: {},
      userRepository: {},
      forceCloseRepository: {},
    };

    // Import and instantiate the service
    const ForceCloseService = require('../src/app/services/forceCloseService');
    forceCloseService = new ForceCloseService(mockContainer);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Acceptance Criteria: Force Close Button Visibility', () => {
    it('should show button when RS Status is "RS In Progress" AND PO Status is "For Delivery"', async () => {
      // Arrange
      const requisitionId = 'test-req-123';
      const rsStatus = 'rs_in_progress';

      // Mock PO status check to return active POs with for_delivery status
      mockPurchaseOrderRepository.findAll.mockResolvedValue([
        {
          id: 'po-123',
          status: 'for_delivery',
          amount: 1000,
          quantity: 10,
        },
      ]);

      // Act
      const result = await forceCloseService._shouldShowForceCloseButton(
        requisitionId,
        rsStatus,
      );

      // Assert
      expect(result).toBe(true);
      expect(mockPurchaseOrderRepository.findAll).toHaveBeenCalledWith({
        where: { requisitionId },
        include: expect.any(Array),
        paginate: false,
      });
    });

    it('should NOT show button when RS Status is NOT "RS In Progress"', async () => {
      // Arrange
      const requisitionId = 'test-req-123';
      const rsStatus = 'approved'; // Different status

      // Act
      const result = await forceCloseService._shouldShowForceCloseButton(
        requisitionId,
        rsStatus,
      );

      // Assert
      expect(result).toBe(false);
      // Should not even check PO status if RS status is wrong
      expect(mockPurchaseOrderRepository.findAll).not.toHaveBeenCalled();
    });

    it('should NOT show button when RS is "RS In Progress" but NO POs have "For Delivery" status', async () => {
      // Arrange
      const requisitionId = 'test-req-123';
      const rsStatus = 'rs_in_progress';

      // Mock PO status check to return POs without for_delivery status
      mockPurchaseOrderRepository.findAll.mockResolvedValue([
        {
          id: 'po-123',
          status: 'closed_po', // Different status
          amount: 1000,
          quantity: 10,
        },
      ]);

      // Act
      const result = await forceCloseService._shouldShowForceCloseButton(
        requisitionId,
        rsStatus,
      );

      // Assert
      expect(result).toBe(false);
      expect(mockPurchaseOrderRepository.findAll).toHaveBeenCalled();
    });

    it('should NOT show button when no POs exist', async () => {
      // Arrange
      const requisitionId = 'test-req-123';
      const rsStatus = 'rs_in_progress';

      // Mock PO status check to return empty array
      mockPurchaseOrderRepository.findAll.mockResolvedValue([]);

      // Act
      const result = await forceCloseService._shouldShowForceCloseButton(
        requisitionId,
        rsStatus,
      );

      // Assert
      expect(result).toBe(false);
      expect(mockPurchaseOrderRepository.findAll).toHaveBeenCalled();
    });
  });

  describe('Acceptance Criteria: Force Close Eligibility', () => {
    it('should validate that PO has been partially delivered', async () => {
      // This test would verify the partial delivery validation logic
      // Implementation would depend on the specific test setup for delivery validation
      expect(true).toBe(true); // Placeholder - actual implementation would test _checkPODeliveryStatus
    });

    it('should validate that all delivered items have been paid', async () => {
      // This test would verify the payment validation logic
      // Implementation would depend on the specific test setup for payment validation
      expect(true).toBe(true); // Placeholder - actual implementation would test _checkPaymentPrerequisite
    });
  });

  describe('Enhanced Multiple DR/PR Validation (Test Scenario 8)', () => {
    it('should validate total approved PR amount equals total DR amount for multiple DRs and PRs', async () => {
      // Arrange - Mock PO with multiple DRs and PRs
      const mockPO = {
        id: 'po-123',
        po_number: 'TEST-PO-008',
        deliveryReceipts: [
          {
            id: 'dr-1',
            dr_number: 'TEST-DR-008-1',
            status: 'Delivered',
            deliveryReceiptInvoices: [
              {
                id: 'inv-1',
                invoice_no: 'INV-001',
                total_sales: 5000.0,
                vat_amount: 600.0,
              },
            ],
            items: [
              {
                id: 'dri-1',
                item_id: 7,
                qty_delivered: 30,
                po_item_id: 'poi-1',
              },
            ],
          },
          {
            id: 'dr-2',
            dr_number: 'TEST-DR-008-2',
            status: 'Delivered',
            deliveryReceiptInvoices: [
              {
                id: 'inv-2',
                invoice_no: 'INV-002',
                total_sales: 3000.0,
                vat_amount: 360.0,
              },
            ],
            items: [
              {
                id: 'dri-2',
                item_id: 7,
                qty_delivered: 20,
                po_item_id: 'poi-1',
              },
            ],
          },
        ],
        rsPaymentRequests: [
          {
            id: 'pr-1',
            pr_number: 'FC-PR-08-1',
            status: 'Closed',
            amount: 5000.0,
            delivery_invoice_id: 'inv-1',
          },
          {
            id: 'pr-2',
            pr_number: 'FC-PR-08-2',
            status: 'Closed',
            amount: 3000.0,
            delivery_invoice_id: 'inv-2',
          },
        ],
        purchaseOrderItems: [
          { id: 'poi-1', canvass_item_supplier_id: 'cis-1' },
        ],
      };

      // Mock database call for canvass item supplier
      mockContainer.db.canvassItemSupplierModel = {
        findByPk: jest.fn().mockResolvedValue({
          unit_price: 100.0,
          discount_type: 'percentage',
          discount: 10, // 10% discount
        }),
      };

      // Act
      const result = await forceCloseService._validatePOPaymentBalance(mockPO);

      // Assert
      expect(result.isValid).toBe(true);
      expect(result.totalDRAmount).toBe(8000.0); // 5000 + 3000 from invoices
      expect(result.totalPRAmount).toBe(8000.0); // 5000 + 3000 from PRs
      expect(result.difference).toBeLessThanOrEqual(0.01); // Within tolerance
      expect(result.drDetails).toHaveLength(2);
      expect(result.prDetails).toHaveLength(2);
      expect(result.unpaidDRs).toHaveLength(0); // All DRs should be paid
    });

    it('should identify unpaid deliveries when PR amount is less than DR amount', async () => {
      // Arrange - Mock PO with unpaid delivery
      const mockPO = {
        id: 'po-123',
        po_number: 'TEST-PO-008',
        deliveryReceipts: [
          {
            id: 'dr-1',
            dr_number: 'TEST-DR-008-1',
            status: 'Delivered',
            deliveryReceiptInvoices: [
              {
                id: 'inv-1',
                invoice_no: 'INV-001',
                total_sales: 5000.0,
                vat_amount: 600.0,
              },
            ],
          },
          {
            id: 'dr-2',
            dr_number: 'TEST-DR-008-2',
            status: 'Delivered',
            deliveryReceiptInvoices: [
              {
                id: 'inv-2',
                invoice_no: 'INV-002',
                total_sales: 3000.0,
                vat_amount: 360.0,
              },
            ],
          },
        ],
        rsPaymentRequests: [
          {
            id: 'pr-1',
            pr_number: 'FC-PR-08-1',
            status: 'Closed',
            amount: 5000.0,
            delivery_invoice_id: 'inv-1',
          },
          // Missing PR for dr-2 (unpaid delivery)
        ],
      };

      // Act
      const result = await forceCloseService._validatePOPaymentBalance(mockPO);

      // Assert
      expect(result.isValid).toBe(false);
      expect(result.totalDRAmount).toBe(8000.0); // 5000 + 3000 from invoices
      expect(result.totalPRAmount).toBe(5000.0); // Only 5000 from single PR
      expect(result.difference).toBe(3000.0); // 8000 - 5000
      expect(result.unpaidDRs).toHaveLength(1); // dr-2 should be unpaid
      expect(result.unpaidDRs[0].drId).toBe('dr-2');
    });

    it('should calculate DR amount from items when invoice data is not available', async () => {
      // Arrange - Mock PO with DR without invoice data
      const mockPO = {
        id: 'po-123',
        po_number: 'TEST-PO-008',
        deliveryReceipts: [
          {
            id: 'dr-1',
            dr_number: 'TEST-DR-008-1',
            status: 'Delivered',
            deliveryReceiptInvoices: [], // No invoice data
            items: [
              {
                id: 'dri-1',
                item_id: 7,
                qty_delivered: 50,
                po_item_id: 'poi-1',
                item_des: 'Test Item',
              },
            ],
          },
        ],
        rsPaymentRequests: [
          {
            id: 'pr-1',
            pr_number: 'FC-PR-08-1',
            status: 'Closed',
            amount: 4500.0, // 50 qty × 100 unit price × 0.9 (10% discount)
            delivery_invoice_id: 'inv-1',
          },
        ],
        purchaseOrderItems: [
          { id: 'poi-1', canvass_item_supplier_id: 'cis-1' },
        ],
      };

      // Mock database call for canvass item supplier
      mockContainer.db.canvassItemSupplierModel = {
        findByPk: jest.fn().mockResolvedValue({
          unit_price: 100.0,
          discount_type: 'percentage',
          discount: 10, // 10% discount = 90.00 per unit
        }),
      };

      // Act
      const result = await forceCloseService._validatePOPaymentBalance(mockPO);

      // Assert
      expect(result.drDetails[0].calculationMethod).toBe('items_calculation');
      expect(result.totalDRAmount).toBe(4500.0); // 50 × 90.00
      expect(result.totalPRAmount).toBe(4500.0);
      expect(result.isValid).toBe(true);
    });
  });
});
