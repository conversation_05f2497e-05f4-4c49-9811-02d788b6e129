const chai = require('chai');
const sinonChai = require('sinon-chai');
const sinon = require('sinon');

chai.use(sinonChai);

const { expect } = chai;
const { restore, stub, createStubInstance } = sinon;

describe('ForceCloseRepository', () => {
  let forceCloseRepository;
  let mockContainer;
  let mockFastify;
  let mockTransaction;

  beforeEach(() => {
    // Mock fastify logger
    mockFastify = {
      log: {
        info: stub(),
        warn: stub(),
        error: stub(),
      },
    };

    // Mock transaction
    mockTransaction = {
      commit: stub(),
      rollback: stub(),
    };

    // Mock container
    mockContainer = {
      fastify: mockFastify,
      historyRepository: {
        create: stub(),
      },
    };

    // Import and instantiate ForceCloseRepository
    const ForceCloseRepository = require('../../../../../src/infra/repositories/forceCloseRepository');
    forceCloseRepository = new ForceCloseRepository(mockContainer);
  });

  afterEach(() => {
    restore();
  });

  describe('Constructor', () => {
    it('should initialize with container dependencies', () => {
      expect(forceCloseRepository.fastify).to.equal(mockFastify);
      expect(forceCloseRepository.historyRepository).to.equal(
        mockContainer.historyRepository,
      );
    });
  });

  describe('executeForceCloseWorkflow', () => {
    const mockParams = {
      requisitionId: 123,
      userId: 1,
      scenario: 'ACTIVE_PO_PARTIAL_DELIVERY',
      notes: 'Test force close',
      transaction: mockTransaction,
    };

    beforeEach(() => {
      // Mock private methods
      stub(forceCloseRepository, '_updateRequisitionStatus').resolves();
      stub(
        forceCloseRepository,
        '_handleActivePOPartialDeliveryScenario',
      ).resolves();
      stub(forceCloseRepository, '_addForceCloseAuditTrail').resolves();
    });

    it('should execute ACTIVE_PO_PARTIAL_DELIVERY scenario successfully', async () => {
      const result =
        await forceCloseRepository.executeForceCloseWorkflow(mockParams);

      expect(result.requisitionId).to.equal(123);
      expect(result.scenario).to.equal('ACTIVE_PO_PARTIAL_DELIVERY');
      expect(result.documentsUpdated).to.have.length(1);
      expect(result.documentsUpdated[0].type).to.equal('requisition');

      expect(
        forceCloseRepository._updateRequisitionStatus,
      ).to.have.been.calledWith(123, 'CLOSED', 1, mockTransaction);
      expect(forceCloseRepository._handleActivePOPartialDeliveryScenario).to
        .have.been.called;
      expect(
        forceCloseRepository._addForceCloseAuditTrail,
      ).to.have.been.calledWith(
        123,
        1,
        'ACTIVE_PO_PARTIAL_DELIVERY',
        'Test force close',
        mockTransaction,
      );
    });

    it('should execute CLOSED_PO_WITH_REMAINING_CANVASS_QTY scenario successfully', async () => {
      const params = {
        ...mockParams,
        scenario: 'CLOSED_PO_WITH_REMAINING_CANVASS_QTY',
      };
      stub(
        forceCloseRepository,
        '_handleClosedPORemainingQtyScenario',
      ).resolves();

      const result =
        await forceCloseRepository.executeForceCloseWorkflow(params);

      expect(result.scenario).to.equal('CLOSED_PO_WITH_REMAINING_CANVASS_QTY');
      expect(forceCloseRepository._handleClosedPORemainingQtyScenario).to.have
        .been.called;
    });

    it('should execute CLOSED_PO_PENDING_CS scenario successfully', async () => {
      const params = { ...mockParams, scenario: 'CLOSED_PO_PENDING_CS' };
      stub(forceCloseRepository, '_handleClosedPOPendingCSScenario').resolves();

      const result =
        await forceCloseRepository.executeForceCloseWorkflow(params);

      expect(result.scenario).to.equal('CLOSED_PO_PENDING_CS');
      expect(forceCloseRepository._handleClosedPOPendingCSScenario).to.have.been
        .called;
    });

    it('should throw error for unknown scenario', async () => {
      const params = { ...mockParams, scenario: 'UNKNOWN_SCENARIO' };

      try {
        await forceCloseRepository.executeForceCloseWorkflow(params);
        expect.fail('Should have thrown error');
      } catch (error) {
        expect(error.message).to.include('Unknown force close scenario');
      }
    });

    it('should handle repository errors gracefully', async () => {
      forceCloseRepository._updateRequisitionStatus.rejects(
        new Error('Database error'),
      );

      try {
        await forceCloseRepository.executeForceCloseWorkflow(mockParams);
        expect.fail('Should have thrown error');
      } catch (error) {
        expect(error.message).to.include('Database error');
        expect(mockFastify.log.error).to.have.been.called;
      }
    });
  });

  describe('_handleActivePOPartialDeliveryScenario', () => {
    const mockParams = {
      requisitionId: 123,
      userId: 1,
      transaction: mockTransaction,
      result: {
        documentsUpdated: [],
        quantitiesReturned: {},
        poAdjustments: [],
      },
    };

    beforeEach(() => {
      stub(forceCloseRepository, '_getPurchaseOrdersForRequisition').resolves([
        {
          id: 1,
          amount: 1000,
          quantity: 10,
        },
      ]);
      stub(forceCloseRepository, '_getDeliveredQuantitiesForPO').resolves({
        totalDelivered: 5,
        totalAmount: 500,
        totalQuantity: 5,
      });
      stub(forceCloseRepository, '_updatePOAmountAndQuantity').resolves();
      stub(forceCloseRepository, '_addPOSystemNotes').resolves();
      stub(forceCloseRepository, '_updatePOStatus').resolves();
      stub(
        forceCloseRepository,
        '_returnUnfulfilledQuantitiesToGFQ',
      ).resolves();
    });

    it('should handle active PO partial delivery scenario', async () => {
      await forceCloseRepository._handleActivePOPartialDeliveryScenario(
        mockParams.requisitionId,
        mockParams.userId,
        mockParams.transaction,
        mockParams.result,
      );

      expect(forceCloseRepository._getPurchaseOrdersForRequisition).to.have.been
        .called;
      expect(forceCloseRepository._getDeliveredQuantitiesForPO).to.have.been
        .called;
      expect(
        forceCloseRepository._updatePOAmountAndQuantity,
      ).to.have.been.calledWith(1, 500, 5, 1, mockTransaction);
      expect(forceCloseRepository._updatePOStatus).to.have.been.calledWith(
        1,
        'CLOSED',
        1,
        mockTransaction,
      );
      expect(mockParams.result.poAdjustments).to.have.length(1);
      expect(mockParams.result.documentsUpdated).to.have.length(1);
    });
  });

  describe('_handleClosedPORemainingQtyScenario', () => {
    const mockParams = {
      requisitionId: 123,
      userId: 1,
      transaction: mockTransaction,
      result: {
        documentsUpdated: [],
        quantitiesReturned: {},
        poAdjustments: [],
      },
    };

    beforeEach(() => {
      stub(forceCloseRepository, '_getRemainingQuantitiesToCanvass').resolves([
        {
          id: 1,
          name: 'Test Item',
          remainingQty: 5,
          type: 'OFM',
        },
      ]);
      stub(forceCloseRepository, '_returnQuantityToGFQ').resolves();
      stub(forceCloseRepository, '_zeroOutRemainingQuantity').resolves();
    });

    it('should handle closed PO remaining quantity scenario', async () => {
      await forceCloseRepository._handleClosedPORemainingQtyScenario(
        mockParams.requisitionId,
        mockParams.userId,
        mockParams.transaction,
        mockParams.result,
      );

      expect(forceCloseRepository._getRemainingQuantitiesToCanvass).to.have.been
        .called;
      expect(forceCloseRepository._returnQuantityToGFQ).to.have.been.called;
      expect(forceCloseRepository._zeroOutRemainingQuantity).to.have.been
        .called;
      expect(mockParams.result.quantitiesReturned['1']).to.deep.equal({
        itemName: 'Test Item',
        returnedQty: 5,
        type: 'OFM',
      });
    });
  });

  describe('_handleClosedPOPendingCSScenario', () => {
    const mockParams = {
      requisitionId: 123,
      userId: 1,
      transaction: mockTransaction,
      result: {
        documentsUpdated: [],
        quantitiesReturned: {},
        poAdjustments: [],
      },
    };

    beforeEach(() => {
      stub(forceCloseRepository, '_getPendingCanvassSheets').resolves([
        { id: 1, status: 'CS_PENDING_APPROVAL' },
      ]);
      stub(forceCloseRepository, '_cancelCanvassSheet').resolves();
      stub(
        forceCloseRepository,
        '_handleClosedPORemainingQtyScenario',
      ).resolves();
    });

    it('should handle closed PO pending CS scenario', async () => {
      await forceCloseRepository._handleClosedPOPendingCSScenario(
        mockParams.requisitionId,
        mockParams.userId,
        mockParams.transaction,
        mockParams.result,
      );

      expect(forceCloseRepository._getPendingCanvassSheets).to.have.been.called;
      expect(forceCloseRepository._cancelCanvassSheet).to.have.been.calledWith(
        1,
        1,
        mockTransaction,
      );
      expect(forceCloseRepository._handleClosedPORemainingQtyScenario).to.have
        .been.called;
      expect(mockParams.result.documentsUpdated).to.have.length(1);
      expect(mockParams.result.documentsUpdated[0].type).to.equal(
        'canvass_sheet',
      );
    });
  });

  describe('_addForceCloseAuditTrail', () => {
    it('should add audit trail entry', async () => {
      await forceCloseRepository._addForceCloseAuditTrail(
        123,
        1,
        'ACTIVE_PO_PARTIAL_DELIVERY',
        'Test notes',
        mockTransaction,
      );

      expect(mockContainer.historyRepository.create).to.have.been.calledWith(
        {
          requisitionId: 123,
          userId: 1,
          action: 'FORCE_CLOSE',
          description:
            'Requisition force closed - Scenario: ACTIVE_PO_PARTIAL_DELIVERY',
          notes: 'Test notes',
          metadata: sinon.match.string,
        },
        { transaction: mockTransaction },
      );
    });
  });
});
