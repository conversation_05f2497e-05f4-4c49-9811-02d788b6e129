const chai = require('chai');
const sinonChai = require('sinon-chai');
const sinon = require('sinon');

chai.use(sinonChai);

const { expect } = chai;
const { restore, stub, createStubInstance } = sinon;

describe('ForceCloseRepository', () => {
  let forceCloseRepository;
  let mockContainer;
  let mockFastify;
  let mockDb;
  let mockTransaction;
  let mockRepositories;

  beforeEach(() => {
    // Mock fastify logger
    mockFastify = {
      log: {
        info: stub(),
        warn: stub(),
        error: stub(),
      },
    };

    // Mock transaction
    mockTransaction = {
      commit: stub(),
      rollback: stub(),
    };

    // Mock database models
    mockDb = {
      requisitionModel: {
        findByPk: stub(),
        update: stub(),
      },
      purchaseOrderModel: {
        findAll: stub(),
        findByPk: stub(),
        update: stub(),
      },
      deliveryReceiptModel: {
        findAll: stub(),
      },
      canvassRequisitionModel: {
        findAll: stub(),
        update: stub(),
      },
      forceCloseLogModel: {
        create: stub(),
        findAll: stub(),
      },
      userModel: {},
      itemModel: {},
    };

    // Mock repositories
    mockRepositories = {
      requisitionRepository: {
        update: stub(),
      },
      historyRepository: {
        create: stub(),
        findAll: stub(),
      },
    };

    // Mock container
    mockContainer = {
      fastify: mockFastify,
      db: mockDb,
      ...mockRepositories,
    };

    // Import and instantiate ForceCloseRepository
    const ForceCloseRepository = require('../../../../../src/infra/repositories/forceCloseRepository');
    forceCloseRepository = new ForceCloseRepository(mockContainer);
  });

  afterEach(() => {
    restore();
  });

  describe('Constructor', () => {
    it('should initialize with container dependencies', () => {
      expect(forceCloseRepository.fastify).to.equal(mockFastify);
      expect(forceCloseRepository.db).to.equal(mockDb);
      expect(forceCloseRepository.requisitionRepository).to.equal(
        mockRepositories.requisitionRepository,
      );
    });
  });

  describe('executeForceCloseWorkflow', () => {
    const mockParams = {
      requisitionId: 123,
      userId: 1,
      scenario: 'ACTIVE_PO_PARTIAL_DELIVERY',
      notes: 'Test force close notes',
      transaction: mockTransaction,
    };

    beforeEach(() => {
      // Mock scenario-specific methods
      stub(forceCloseRepository, '_updateRequisitionStatus').resolves();
      stub(
        forceCloseRepository,
        '_handleActivePOPartialDeliveryScenario',
      ).resolves();
      stub(
        forceCloseRepository,
        '_handleClosedPORemainingQtyScenario',
      ).resolves();
      stub(forceCloseRepository, '_handleClosedPOPendingCSScenario').resolves();
      stub(forceCloseRepository, '_addForceCloseAuditTrail').resolves({
        id: 456,
      });
    });

    it('should execute ACTIVE_PO_PARTIAL_DELIVERY scenario successfully', async () => {
      const result =
        await forceCloseRepository.executeForceCloseWorkflow(mockParams);

      expect(
        forceCloseRepository._updateRequisitionStatus,
      ).to.have.been.calledWith(123, 'CLOSED', 1, mockTransaction);
      expect(
        forceCloseRepository._handleActivePOPartialDeliveryScenario,
      ).to.have.been.calledWith(123, 1, mockTransaction, sinon.match.object);
      expect(
        forceCloseRepository._addForceCloseAuditTrail,
      ).to.have.been.calledWith(
        123,
        1,
        'ACTIVE_PO_PARTIAL_DELIVERY',
        'Test force close notes',
        mockTransaction,
      );

      expect(result.requisitionId).to.equal(123);
      expect(result.scenario).to.equal('ACTIVE_PO_PARTIAL_DELIVERY');
      expect(result.documentsUpdated).to.include.deep.members([
        { type: 'requisition', id: 123, status: 'CLOSED' },
      ]);
    });

    it('should execute CLOSED_PO_WITH_REMAINING_CANVASS_QTY scenario successfully', async () => {
      const params = {
        ...mockParams,
        scenario: 'CLOSED_PO_WITH_REMAINING_CANVASS_QTY',
      };

      const result =
        await forceCloseRepository.executeForceCloseWorkflow(params);

      expect(
        forceCloseRepository._handleClosedPORemainingQtyScenario,
      ).to.have.been.calledWith(123, 1, mockTransaction, sinon.match.object);
      expect(result.scenario).to.equal('CLOSED_PO_WITH_REMAINING_CANVASS_QTY');
    });

    it('should execute CLOSED_PO_PENDING_CS scenario successfully', async () => {
      const params = { ...mockParams, scenario: 'CLOSED_PO_PENDING_CS' };

      const result =
        await forceCloseRepository.executeForceCloseWorkflow(params);

      expect(
        forceCloseRepository._handleClosedPOPendingCSScenario,
      ).to.have.been.calledWith(123, 1, mockTransaction, sinon.match.object);
      expect(result.scenario).to.equal('CLOSED_PO_PENDING_CS');
    });

    it('should throw error for unknown scenario', async () => {
      const params = { ...mockParams, scenario: 'UNKNOWN_SCENARIO' };

      try {
        await forceCloseRepository.executeForceCloseWorkflow(params);
        expect.fail('Should have thrown error');
      } catch (error) {
        expect(error.message).to.equal(
          'Unknown force close scenario: UNKNOWN_SCENARIO',
        );
      }
    });

    it('should handle workflow execution errors', async () => {
      const error = new Error('Database error');
      forceCloseRepository._updateRequisitionStatus.rejects(error);

      try {
        await forceCloseRepository.executeForceCloseWorkflow(mockParams);
        expect.fail('Should have thrown error');
      } catch (thrownError) {
        expect(thrownError).to.equal(error);
        expect(mockFastify.log.error).to.have.been.calledWith(
          'Force close workflow failed for RS: 123 - Database error',
        );
      }
    });
  });

  describe('_getPurchaseOrdersForRequisition', () => {
    it('should retrieve purchase orders with items successfully', async () => {
      const mockPOs = [
        {
          id: 1,
          requisitionId: 123,
          status: 'FOR_DELIVERY',
          amount: 1000,
          purchaseOrderItems: [
            {
              id: 1,
              item: { id: 1, itemName: 'Test Item', type: 'OFM' },
            },
          ],
        },
      ];

      mockDb.purchaseOrderModel.findAll.resolves(mockPOs);

      const result =
        await forceCloseRepository._getPurchaseOrdersForRequisition(
          123,
          mockTransaction,
        );

      expect(mockDb.purchaseOrderModel.findAll).to.have.been.calledWith({
        where: { requisitionId: 123 },
        include: sinon.match.array,
        transaction: mockTransaction,
      });
      expect(result).to.deep.equal(mockPOs);
    });

    it('should handle database errors gracefully', async () => {
      const error = new Error('Database connection error');
      mockDb.purchaseOrderModel.findAll.rejects(error);

      try {
        await forceCloseRepository._getPurchaseOrdersForRequisition(
          123,
          mockTransaction,
        );
        expect.fail('Should have thrown error');
      } catch (thrownError) {
        expect(thrownError).to.equal(error);
        expect(mockFastify.log.error).to.have.been.calledWith(
          'Failed to get purchase orders for requisition 123: Database connection error',
        );
      }
    });
  });

  describe('_getDeliveredQuantitiesForPO', () => {
    it('should calculate delivered quantities correctly', async () => {
      const mockDeliveryReceipts = [
        {
          id: 1,
          purchaseOrderId: 1,
          status: 'APPROVED',
          deliveryReceiptItems: [
            {
              quantity: 5,
              unitPrice: 100,
              item: { unitPrice: 100 },
            },
            {
              quantity: 3,
              unitPrice: 150,
              item: { unitPrice: 150 },
            },
          ],
        },
      ];

      mockDb.deliveryReceiptModel.findAll.resolves(mockDeliveryReceipts);

      const result = await forceCloseRepository._getDeliveredQuantitiesForPO(
        1,
        mockTransaction,
      );

      expect(result.totalDelivered).to.equal(8);
      expect(result.totalQuantity).to.equal(8);
      expect(result.totalAmount).to.equal(950); // (5 * 100) + (3 * 150)
    });

    it('should return zero values when no deliveries found', async () => {
      mockDb.deliveryReceiptModel.findAll.resolves([]);

      const result = await forceCloseRepository._getDeliveredQuantitiesForPO(
        1,
        mockTransaction,
      );

      expect(result.totalDelivered).to.equal(0);
      expect(result.totalQuantity).to.equal(0);
      expect(result.totalAmount).to.equal(0);
    });

    it('should handle calculation errors gracefully', async () => {
      const error = new Error('Calculation error');
      mockDb.deliveryReceiptModel.findAll.rejects(error);

      const result = await forceCloseRepository._getDeliveredQuantitiesForPO(
        1,
        mockTransaction,
      );

      expect(result.totalDelivered).to.equal(0);
      expect(result.totalQuantity).to.equal(0);
      expect(result.totalAmount).to.equal(0);
      expect(mockFastify.log.error).to.have.been.calledWith(
        'Failed to get delivered quantities for PO 1: Calculation error',
      );
    });
  });

  describe('_updatePOAmountAndQuantity', () => {
    it('should update PO amount and quantity with original values', async () => {
      const mockCurrentPO = {
        id: 1,
        amount: 1000,
        quantity: 10,
        originalAmount: null,
        originalQuantity: null,
      };

      mockDb.purchaseOrderModel.findByPk.resolves(mockCurrentPO);
      mockDb.purchaseOrderModel.update.resolves();

      await forceCloseRepository._updatePOAmountAndQuantity(
        1,
        500,
        5,
        1,
        mockTransaction,
      );

      expect(mockDb.purchaseOrderModel.update).to.have.been.calledWith(
        {
          amount: 500,
          quantity: 5,
          updatedBy: 1,
          updatedAt: sinon.match.date,
          originalAmount: 1000,
          originalQuantity: 10,
        },
        {
          where: { id: 1 },
          transaction: mockTransaction,
        },
      );
    });

    it('should preserve existing original values', async () => {
      const mockCurrentPO = {
        id: 1,
        amount: 500,
        quantity: 5,
        originalAmount: 1000,
        originalQuantity: 10,
      };

      mockDb.purchaseOrderModel.findByPk.resolves(mockCurrentPO);
      mockDb.purchaseOrderModel.update.resolves();

      await forceCloseRepository._updatePOAmountAndQuantity(
        1,
        300,
        3,
        1,
        mockTransaction,
      );

      expect(mockDb.purchaseOrderModel.update).to.have.been.calledWith(
        {
          amount: 300,
          quantity: 3,
          updatedBy: 1,
          updatedAt: sinon.match.date,
        },
        {
          where: { id: 1 },
          transaction: mockTransaction,
        },
      );
    });

    it('should handle PO not found error', async () => {
      mockDb.purchaseOrderModel.findByPk.resolves(null);

      try {
        await forceCloseRepository._updatePOAmountAndQuantity(
          1,
          500,
          5,
          1,
          mockTransaction,
        );
        expect.fail('Should have thrown error');
      } catch (error) {
        expect(error.message).to.equal('Purchase Order 1 not found');
      }
    });
  });

  describe('getForceCloseHistory', () => {
    it('should retrieve comprehensive force close history', async () => {
      const mockForceCloseLogs = [
        {
          id: 1,
          requisitionId: 123,
          scenarioType: 'ACTIVE_PO_PARTIAL_DELIVERY',
          createdAt: new Date(),
          user: { id: 1, username: 'testuser' },
          requisition: { id: 123, rsNumber: 'RS-001' },
        },
      ];

      const mockAuditTrail = [
        {
          id: 1,
          requisitionId: 123,
          action: 'FORCE_CLOSE',
          createdAt: new Date(),
        },
      ];

      mockDb.forceCloseLogModel.findAll.resolves(mockForceCloseLogs);
      mockRepositories.historyRepository.findAll.resolves(mockAuditTrail);

      const result = await forceCloseRepository.getForceCloseHistory({
        requisitionId: 123,
        userId: 1,
      });

      expect(result.history).to.deep.equal(mockForceCloseLogs);
      expect(result.auditTrail).to.deep.equal(mockAuditTrail);
      expect(result.summary.totalForceCloses).to.equal(1);
      expect(result.summary.scenarios).to.include('ACTIVE_PO_PARTIAL_DELIVERY');
    });

    it('should handle history retrieval errors', async () => {
      const error = new Error('History retrieval error');
      mockDb.forceCloseLogModel.findAll.rejects(error);

      try {
        await forceCloseRepository.getForceCloseHistory({
          requisitionId: 123,
          userId: 1,
        });
        expect.fail('Should have thrown error');
      } catch (thrownError) {
        expect(thrownError).to.equal(error);
        expect(mockFastify.log.error).to.have.been.calledWith(
          'Force close history retrieval failed in repository for RS: 123 - History retrieval error',
        );
      }
    });
  });
});
