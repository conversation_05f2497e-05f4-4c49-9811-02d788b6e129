const chai = require('chai');
const sinonChai = require('sinon-chai');
const sinon = require('sinon');

chai.use(sinonChai);

const { expect } = chai;
const { restore, stub, createStubInstance } = sinon;

// Following the exact pattern from UserController.spec.js
describe('ForceCloseController', () => {
  const expectedResponse = {
    timestamp: new Date(),
    results: 'Success',
  };

  const req = {
    params: { requisitionId: '123' },
    body: { notes: 'Test force close notes' },
    userFromToken: { id: 1, username: 'testuser' },
  };

  const reply = {
    status: stub().returnsThis(),
    send: stub(),
  };

  // Mock all required dependencies - EXACTLY what ForceCloseController expects
  const mockContainer = {
    db: {
      sequelize: {
        transaction: stub().resolves({
          commit: stub().resolves(),
          rollback: stub().resolves(),
        }),
      },
    },
    utils: {},
    entities: {
      forceClose: {
        validateForceCloseRequest: stub(),
        validateForceCloseResponse: stub(),
      },
    },
    constants: {},
    clientErrors: {
      BAD_REQUEST: stub().returns(new Error('Bad Request')),
      VALIDATION_ERROR: stub().returns(new Error('Validation Error')),
    },
    serverErrors: {
      INTERNAL_SERVER_ERROR: stub().returns(new Error('Internal Server Error')),
    },
    fastify: {
      log: {
        info: stub(),
        error: stub(),
        warn: stub(),
      },
    },
    forceCloseService: {
      validateForceCloseEligibility: stub(),
      executeForceClose: stub(),
      getForceCloseHistory: stub(),
    },
  };

  // Import and instantiate controller following UserController pattern
  const ForceCloseController = require('../../../../../../src/app/handlers/controllers/forceCloseController');
  let forceCloseController = new ForceCloseController(mockContainer);

  beforeEach(() => {
    // Reset stubs before each test
    mockContainer.forceCloseService.validateForceCloseEligibility.reset();
    mockContainer.forceCloseService.executeForceClose.reset();
    mockContainer.forceCloseService.getForceCloseHistory.reset();
    reply.status.reset();
    reply.send.reset();
  });

  afterEach(() => {
    restore();
  });

  describe('checkForceCloseEligibility()', () => {
    context('When requisition is eligible for force close', () => {
      it('Should return eligibility response', async () => {
        const mockValidationResult = {
          isEligible: true,
          scenario: 'ACTIVE_PO_PARTIAL_DELIVERY',
          details: { activePOs: [{ id: 1 }] },
        };

        const forceCloseCtrl = stub(
          forceCloseController,
          'checkForceCloseEligibility',
        );
        const checkEligibility = forceCloseCtrl
          .withArgs(req, reply)
          .returns(expectedResponse);

        expect(checkEligibility()).to.be.equal(expectedResponse);
        expect(checkEligibility())
          .to.be.an('object')
          .include({ timestamp: expectedResponse.timestamp });
      });
    });

    context('When requisition is not eligible', () => {
      it('Should return ineligible response', async () => {
        const ineligibleMsg = 'Not eligible for force close';
        expectedResponse.results = ineligibleMsg;

        const forceCloseCtrl = stub(
          forceCloseController,
          'checkForceCloseEligibility',
        );
        const checkEligibility = forceCloseCtrl
          .withArgs(req, reply)
          .returns(expectedResponse);

        expect(checkEligibility()).to.be.equal(expectedResponse);
        expect(checkEligibility())
          .to.be.an('object')
          .include({ results: ineligibleMsg });
      });
    });

    context('When it has an error encountered', () => {
      it('Should return an error', async () => {
        const msg = 'Expected to throw an error message';
        try {
          const forceCloseCtrl = stub(
            forceCloseController,
            'checkForceCloseEligibility',
          );
          forceCloseCtrl.withArgs(req, reply).returns(expectedResponse);
          expect.fail(msg);
        } catch (error) {
          expect(error.message).to.be.equal(msg);
        }
      });
    });
  });

  describe('forceCloseRequisition()', () => {
    context('When force close executes successfully', () => {
      it('Should return success response', async () => {
        const successMsg = 'Force close executed successfully';
        expectedResponse.results = successMsg;

        const forceCloseCtrl = stub(
          forceCloseController,
          'forceCloseRequisition',
        );
        const executeForceClose = forceCloseCtrl
          .withArgs(req, reply)
          .returns(expectedResponse);

        expect(executeForceClose()).to.be.equal(expectedResponse);
        expect(executeForceClose())
          .to.be.an('object')
          .include({ results: successMsg });
      });
    });

    context('When force close execution fails', () => {
      it('Should return failure response', async () => {
        const failedMsg = 'Force close execution failed';
        expectedResponse.results = failedMsg;

        const forceCloseCtrl = stub(
          forceCloseController,
          'forceCloseRequisition',
        );
        const executeForceClose = forceCloseCtrl
          .withArgs(req, reply)
          .returns(expectedResponse);

        expect(executeForceClose()).to.be.equal(expectedResponse);
        expect(executeForceClose())
          .to.be.an('object')
          .include({ results: failedMsg });
      });
    });

    context('When it has an error encountered', () => {
      it('Should return an error', async () => {
        const msg = 'Expected to throw an error message';
        try {
          const forceCloseCtrl = stub(
            forceCloseController,
            'forceCloseRequisition',
          );
          forceCloseCtrl.withArgs(req, reply).returns(expectedResponse);
          expect.fail(msg);
        } catch (error) {
          expect(error.message).to.be.equal(msg);
        }
      });
    });
  });

  describe('getForceCloseHistory()', () => {
    context('When history exists', () => {
      it('Should return force close history', async () => {
        const historyData = { forceCloseLog: { id: 1 }, auditTrail: [] };
        expectedResponse.results = historyData;

        const forceCloseCtrl = stub(
          forceCloseController,
          'getForceCloseHistory',
        );
        const getHistory = forceCloseCtrl
          .withArgs(req, reply)
          .returns(expectedResponse);

        expect(getHistory()).to.be.equal(expectedResponse);
        expect(getHistory())
          .to.be.an('object')
          .include({ results: historyData });
      });
    });

    context('When no history found', () => {
      it('Should return no history message', async () => {
        const noHistoryMsg = 'No force close history found';
        expectedResponse.results = noHistoryMsg;

        const forceCloseCtrl = stub(
          forceCloseController,
          'getForceCloseHistory',
        );
        const getHistory = forceCloseCtrl
          .withArgs(req, reply)
          .returns(expectedResponse);

        expect(getHistory()).to.be.equal(expectedResponse);
        expect(getHistory())
          .to.be.an('object')
          .include({ results: noHistoryMsg });
      });
    });

    context('When it has an error encountered', () => {
      it('Should return an error', async () => {
        const msg = 'Expected to throw an error message';
        try {
          const forceCloseCtrl = stub(
            forceCloseController,
            'getForceCloseHistory',
          );
          forceCloseCtrl.withArgs(req, reply).returns(expectedResponse);
          expect.fail(msg);
        } catch (error) {
          expect(error.message).to.be.equal(msg);
        }
      });
    });
  });
});
