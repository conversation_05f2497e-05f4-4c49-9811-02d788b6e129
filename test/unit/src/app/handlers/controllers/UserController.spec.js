const chai = require('chai');
const sinon = require('sinon');
const sinonChai = require('sinon-chai');

chai.use(sinonChai);

const { expect } = chai;
const { restore, stub } = sinon;

const UserController = require('../../../../../../src/app/handlers/controllers/userController');
const { userErrors, InfraError } = require('../../../../../../src/app/errors');

describe('Src :: App :: Handlers :: Controller :: UsersController', () => {
  const req = {};
  const reply = {};
  const usersObj = {
    id: 1,
    name: 'Name',
    description: 'Description',
    tweets: 'Tweets',
  };
  const message = 'Message Here..';
  const results = [usersObj];
  const data = {
    status: true,
    message,
    results,
  };
  const date = new Date();
  const expectedResponse = {
    timestamp: date,
    status: data.status,
    message: data.message,
    data: data.results,
  };
  const userRepository = {};
  const utils = {};
  let userController = new UserController({
    userRepository,
    utils,
    userErrors,
    InfraError,
  });
  beforeEach(() => {});
  afterEach(() => {
    restore();
  });

  describe('listAllUsers()', () => {
    context('When it has lists of users', () => {
      it('Should return users', async () => {
        const userCtrl = stub(userController, 'listAllUsers');
        const listAllUsers = userCtrl
          .withArgs(req, reply)
          .returns(expectedResponse);
        expect(listAllUsers()).to.be.equal(expectedResponse);
        expect(listAllUsers()).to.be.an('object').include({ timestamp: date });
      });
    });
    context('When it has an error encountered', () => {
      it('Should return an error', async () => {
        const msg = 'Expected to throw an error message';
        try {
          const userCtrl = stub(userController, 'listAllUsers');
          userCtrl.withArgs(req, reply).returns(expectedResponse);
          expect.fail(msg);
        } catch (error) {
          expect(error.message).to.be.equal(msg);
        }
      });
    });
  });

  describe('accountRegistration()', () => {
    context('When it has created users', () => {
      it('Should return a successfully added user', async () => {
        const successMsg = 'Successfully Added User';
        expectedResponse.results = successMsg;

        const userCtrl = stub(userController, 'accountRegistration');
        const accountRegistration = userCtrl
          .withArgs(req, reply)
          .returns(expectedResponse);
        expect(accountRegistration()).to.be.equal(expectedResponse);
        expect(accountRegistration())
          .to.be.an('object')
          .include({ results: successMsg });
      });
    });
    context('When it fails to add user', () => {
      it('Should return a failed to add user', async () => {
        const failedMsg = 'Failed to add user';
        expectedResponse.results = failedMsg;

        const userCtrl = stub(userController, 'accountRegistration');
        const accountRegistration = userCtrl
          .withArgs(req, reply)
          .returns(expectedResponse);
        expect(accountRegistration()).to.be.equal(expectedResponse);
        expect(accountRegistration())
          .to.be.an('object')
          .include({ results: failedMsg });
      });
    });
    context('When it has an error encountered', () => {
      it('Should return an error', async () => {
        const msg = 'Expected to throw an error message';
        try {
          const userCtrl = stub(userController, 'accountRegistration');
          userCtrl.withArgs(req, reply).returns(expectedResponse);
          expect.fail(msg);
        } catch (error) {
          expect(error.message).to.be.equal(msg);
        }
      });
    });
  });

  describe('getUserById()', () => {
    context('When it has existing user', () => {
      it('Should return a user', async () => {
        const id = 1;
        const userCtrl = stub(userController, 'getUserById');
        const getUserById = userCtrl.withArgs(id).returns(expectedResponse);
        expect(getUserById()).to.be.equal(expectedResponse);
        expect(getUserById()).to.be.an('object');
      });
    });
    context('When user is not existing', () => {
      it('Should return a no record found', async () => {
        const id = 1;
        const failedMsg = 'No record found';
        expectedResponse.results = failedMsg;

        const userCtrl = stub(userController, 'getUserById');
        const getUserById = userCtrl.withArgs(id).returns(expectedResponse);
        expect(getUserById()).to.be.equal(expectedResponse);
        expect(getUserById())
          .to.be.an('object')
          .include({ results: failedMsg });
      });
    });
    context('When it has an error encountered', () => {
      it('Should return an error', async () => {
        const id = 1;
        const msg = 'Expected to throw an error message';
        try {
          const userCtrl = stub(userController, 'getUserById');
          userCtrl.withArgs(id).returns(expectedResponse);
          expect.fail(msg);
        } catch (error) {
          expect(error.message).to.be.equal(msg);
        }
      });
    });
  });

  describe('updateUserId()', () => {
    context('When user has successfully updated', () => {
      it('Should return a user', async () => {
        const successMsg = 'Successfully update user';
        expectedResponse.results = successMsg;

        const userCtrl = stub(userController, 'updateUserId');
        const updateUserId = userCtrl
          .withArgs(usersObj)
          .returns(expectedResponse);
        expect(updateUserId(usersObj)).to.be.equal(expectedResponse);
        expect(updateUserId(usersObj))
          .to.be.an('object')
          .include({ results: successMsg });
      });
    });
    context('When user is not updated', () => {
      it('Should return a failed message', async () => {
        const failedMsg = 'Failed to update record';
        expectedResponse.results = failedMsg;

        const userCtrl = stub(userController, 'updateUserId');
        const updateUserId = userCtrl
          .withArgs(usersObj)
          .returns(expectedResponse);
        expect(updateUserId(usersObj)).to.be.equal(expectedResponse);
        expect(updateUserId(usersObj))
          .to.be.an('object')
          .include({ results: failedMsg });
      });
    });
    context('When it has an error encountered', () => {
      it('Should return an error', async () => {
        const msg = 'Expected to throw an error message';
        try {
          const userCtrl = stub(userController, 'updateUserId');
          userCtrl.withArgs(usersObj).returns(expectedResponse);
          expect.fail(msg);
        } catch (error) {
          expect(error.message).to.be.equal(msg);
        }
      });
    });
  });

  describe('deleteUserId()', () => {
    context('When user has successfully deleted', () => {
      it('Should return a user', async () => {
        const id = 1;
        const successMsg = 'Successfully delete user';
        expectedResponse.results = successMsg;

        const userCtrl = stub(userController, 'deleteUserId');
        const deleteUserId = userCtrl.withArgs(id).returns(expectedResponse);
        expect(deleteUserId(id)).to.be.equal(expectedResponse);
        expect(deleteUserId(id))
          .to.be.an('object')
          .include({ results: successMsg });
      });
    });
    context('When user is not updated', () => {
      it('Should return a failed message', async () => {
        const id = 1;
        const failedMsg = 'Failed to delete record';
        expectedResponse.results = failedMsg;

        const userCtrl = stub(userController, 'deleteUserId');
        const deleteUserId = userCtrl.withArgs(id).returns(expectedResponse);
        expect(deleteUserId(id)).to.be.equal(expectedResponse);
        expect(deleteUserId(id))
          .to.be.an('object')
          .include({ results: failedMsg });
      });
    });
    context('When it has an error encountered', () => {
      it('Should return an error', async () => {
        const msg = 'Expected to throw an error message';
        try {
          const userCtrl = stub(userController, 'deleteUserId');
          userCtrl.withArgs(usersObj).returns(expectedResponse);
          expect.fail(msg);
        } catch (error) {
          expect(error.message).to.be.equal(msg);
        }
      });
    });
  });

  // describe('loginAccount()', () => {
  //   context('Accepted User', () => {
  //     it('Should return a user and status 200', async () => {
  //       const params = {param1: 'parameter value'};
  //       const successMsg = 'Log in User Access';
  //       expectedResponse.results = successMsg;
  //       const response = {
  //         message: successMsg,
  //         status: 200,
  //       };

  //       const userCtrl = stub(userController, 'loginAccount');
  //       const loginAccount = userCtrl.withArgs(params).returns(response);
  //       expect(loginAccount(params)).to.be.equal(response);
  //       expect(loginAccount(params)).to.be.an('object').include({message: successMsg});
  //     });

  //     it('Should return a user and status 400', async () => {
  //       const params = {param1: 'parameter value'};
  //       const successMsg = 'Log in User Access';
  //       expectedResponse.results = successMsg;
  //       const response = {
  //         message: successMsg,
  //         status: 400,
  //       };

  //       const userCtrl = stub(userController, 'loginAccount');
  //       const loginAccount = userCtrl.withArgs(params).returns(response);
  //       expect(loginAccount(params)).to.be.equal(response);
  //       expect(loginAccount(params)).to.be.an('object').include({message: successMsg});
  //     });
  //   });
  //   context('When it has an error encountered', () => {
  //     it('Should return an error', async () => {
  //       const msg = 'Expected to throw an error message';
  //       try {
  //         const userCtrl = stub(userController, 'loginAccount');
  //         userCtrl.withArgs(usersObj).returns(expectedResponse);
  //         expect.fail(msg);
  //       } catch (error) {
  //         expect(error.message).to.be.equal(msg);
  //       }
  //     });
  //   });
  // });
});
