const chai = require('chai');
const sinon = require('sinon');
const sinonChai = require('sinon-chai');

chai.use(sinonChai);

const { expect } = chai;
const { restore, stub } = sinon;

const authJwt = require('../../../../../../src/infra/middleware/authJwt');

describe('Src :: App :: Infra :: Middleware :: AuthJwt', () => {
  beforeEach(() => {});
  afterEach(() => {
    restore();
  });

  describe('verifyToken()', () => {
    context('When token is not existing', () => {
      it('Should return no provided token', async () => {
        const req = {};
        const res = {};
        const expectedResponse = 'No token provided';
        const checkDuplicateEmail = stub(authJwt, 'verifyToken');

        try {
          checkDuplicateEmail.withArgs(req, res).returns(expectedResponse);
          expect.fail(expectedResponse);
        } catch (error) {
          expect(error.message).to.be.equal(expectedResponse);
        }
      });
    });
    context('When not authorized', () => {
      it('Should return unauthorized', async () => {
        const req = {};
        const res = {};
        const expectedResponse = 'Unauthorized';
        const checkDuplicateEmail = stub(authJwt, 'verifyToken');

        try {
          checkDuplicateEmail.withArgs(req, res).returns(expectedResponse);
          expect.fail(expectedResponse);
        } catch (error) {
          expect(error.message).to.be.equal(expectedResponse);
        }
      });
    });
    context('When authorized', () => {
      it('Should return true', async () => {
        const token = 'valid token';
        const req = { token };
        const res = {};
        const expectedResponse = true;
        const checkDuplicateEmail = stub(authJwt, 'verifyToken');

        const response = checkDuplicateEmail
          .withArgs(req, res)
          .callsFake(() => expectedResponse);
        expect(response()).to.be.equal(true);
      });
    });
  });
});
