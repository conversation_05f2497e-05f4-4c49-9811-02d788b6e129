const chai = require('chai');
const sinon = require('sinon');
const sinonChai = require('sinon-chai');

chai.use(sinonChai);

const { expect } = chai;
const { restore, stub } = sinon;

const accountVerification = require('../../../../../../src/infra/middleware/accountVerification');

describe('Src :: App :: Infra :: Middleware :: AccountVerification', () => {
  beforeEach(() => {});
  afterEach(() => {
    restore();
  });

  describe('checkDuplicateUsernameOrEmail()', () => {
    context('When email is already duplicate', () => {
      it('Should return error', async () => {
        const email = 'duplicate email';
        const req = { email };
        const res = {};
        const expectedResponse = 'Failed email is already in use';
        const checkDuplicateEmail = stub(
          accountVerification,
          'checkDuplicateUsernameOrEmail',
        );

        try {
          checkDuplicateEmail.withArgs(req, res).returns(expectedResponse);
          expect.fail(expectedResponse);
        } catch (error) {
          expect(error.message).to.be.equal(expectedResponse);
        }
      });
    });
    context('When username is already duplicate', () => {
      it('Should return error', async () => {
        const username = 'duplicate username';
        const req = { username };
        const res = {};
        const expectedResponse = 'Failed username is already in use';
        const checkDuplicateEmail = stub(
          accountVerification,
          'checkDuplicateUsernameOrEmail',
        );

        try {
          checkDuplicateEmail.withArgs(req, res).returns(expectedResponse);
          expect.fail(expectedResponse);
        } catch (error) {
          expect(error.message).to.be.equal(expectedResponse);
        }
      });
    });
    context('When no record found', () => {
      it('Should return true', async () => {
        const username = 'new username';
        const email = 'new email';
        const req = { username, email };
        const res = {};
        const expectedResponse = true;
        const checkDuplicateEmail = stub(
          accountVerification,
          'checkDuplicateUsernameOrEmail',
        );

        const response = checkDuplicateEmail
          .withArgs(req, res)
          .callsFake(() => expectedResponse);
        expect(response()).to.be.equal(true);
      });
    });
  });
});
