const chai = require('chai');
const sinonChai = require('sinon-chai');
// const sinon = require('sinon');
// const jwt = require('jsonwebtoken');
// const config = require('../../../../src/infra/database/config/authConfig');

chai.use(sinonChai);

const { expect } = chai;
// const { restore, stub } = sinon;

// const { authJwt } = require('../../../../src/infra/middleware');
const container = require('../../../../src/container');
const fastify = container.resolve('fastify');

//let token;

describe('Src :: Interfaces :: Routes', () => {
  beforeEach(() => {
    //token = jwt.sign({ id: 123 }, config.secret, { expiresIn: 86400 });
  });
  afterEach(() => {});

  describe('GET / ', async () => {
    context('/ endpoint', () => {
      it('Should response 200', async () => {
        const response = await fastify.inject({
          method: 'GET',
          url: '/',
        });
        expect(response.statusCode).to.be.equal(200);
      });
      it('Should return 404', async () => {
        const response = await fastify.inject({
          method: 'GET',
          url: '/not-found',
        });
        expect(response.statusCode).to.be.equal(404);
      });
    });
  });

  describe('GET /users ', async () => {
    context('/users', () => {
      it('Should response 401 Unauthorized in /users/:id', async () => {
        const response = await fastify.inject({
          method: 'GET',
          url: '/users/:id',
          headers: {
            'x-access-token': 'not valid token',
          },
          payload: {},
          query: {},
        });

        expect(response.statusCode).to.be.equal(401);
        expect(response.statusMessage).to.be.equal('Unauthorized');
      });

      // it('Should response 200 Authorized in /users/:id', async() => {

      //   const response = await fastify.inject({
      //     method: 'GET',
      //     url: '/users/:id',
      //     headers: {
      //       'x-access-token': token
      //     },
      //     payload: {},
      //     query: {},
      //   });
      //   console.log('GET-USERS:',response);
      //   expect(response.statusCode).to.be.equal(200);
      // });
    });
  });
});
