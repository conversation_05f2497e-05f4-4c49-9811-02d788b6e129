const chai = require('chai');
const sinonChai = require('sinon-chai');
const sinon = require('sinon');

chai.use(sinonChai);

const { expect } = chai;
const { restore, stub } = sinon;

describe('Force Close API Routes Integration', () => {
  let fastify;
  let mockForceCloseController;

  beforeEach(async () => {
    // Mock the container and its dependencies
    const mockContainer = {
      resolve: stub(),
    };

    // Mock force close controller
    mockForceCloseController = {
      validateForceClose: stub(),
      forceCloseRequisition: stub(),
      getForceCloseHistory: stub(),
    };

    // Mock entities
    const mockEntities = {
      forceClose: {
        forceCloseParams: {
          type: 'object',
          properties: {
            requisitionId: { type: 'string', pattern: '^[0-9]+$' },
          },
          required: ['requisitionId'],
        },
        forceCloseRequestSchema: {
          type: 'object',
          properties: {
            notes: { type: 'string', minLength: 1, maxLength: 500 },
            confirmedScenario: {
              type: 'string',
              enum: [
                'ACTIVE_PO_PARTIAL_DELIVERY',
                'CLOSED_PO_WITH_REMAINING_CANVASS_QTY',
                'CLOSED_PO_PENDING_CS',
              ],
            },
            acknowledgedImpacts: { type: 'array', items: { type: 'string' } },
          },
          required: ['notes', 'confirmedScenario', 'acknowledgedImpacts'],
        },
      },
    };

    // Mock constants
    const mockConstants = {
      permission: {
        PERMISSIONS: {
          GET_DASHBOARD: 'GET_DASHBOARD',
          UPDATE_DASHBOARD: 'UPDATE_DASHBOARD',
        },
      },
    };

    // Setup container resolution
    mockContainer.resolve.withArgs('entities').returns(mockEntities);
    mockContainer.resolve
      .withArgs('forceCloseController')
      .returns(mockForceCloseController);
    mockContainer.resolve.withArgs('constants').returns(mockConstants);

    // Create fastify instance
    const Fastify = require('fastify');
    fastify = Fastify({ logger: false });

    // Mock authentication and authorization
    fastify.decorate('auth', (handlers) => async (request, reply) => {
      // Mock successful authentication
      request.userFromToken = { id: 1, username: 'testuser' };
    });

    fastify.decorate('authorize', (permission) => async (request, reply) => {
      // Mock successful authorization
      return true;
    });

    // Mock diScope
    fastify.decorate('diScope', mockContainer);

    // Register force close routes
    const forceCloseRoutes = require('../../../../src/interfaces/router/private/forceCloseRoutes');
    await fastify.register(forceCloseRoutes, { prefix: '/api/requisitions' });
  });

  afterEach(async () => {
    restore();
    await fastify.close();
  });

  describe('POST /api/requisitions/:requisitionId/validate-force-close', () => {
    it('should validate force close eligibility successfully', async () => {
      mockForceCloseController.validateForceClose.callsFake(
        async (request, reply) => {
          reply.code(200).send({
            eligible: true,
            scenario: 'ACTIVE_PO_PARTIAL_DELIVERY',
            validationPath: 'ACTIVE_PO_PATH',
            requiresPaymentCheck: true,
            pendingValidations: [],
            impactSummary: {
              poAdjustments: 1,
              documentsAffected: 2,
            },
          });
        },
      );

      const response = await fastify.inject({
        method: 'POST',
        url: '/api/requisitions/123/validate-force-close',
        headers: {
          'x-access-token': 'valid-token',
        },
      });

      expect(response.statusCode).to.equal(200);
      const payload = JSON.parse(response.payload);
      expect(payload.eligible).to.be.true;
      expect(payload.scenario).to.equal('ACTIVE_PO_PARTIAL_DELIVERY');
      expect(payload.validationPath).to.equal('ACTIVE_PO_PATH');
    });

    it('should return ineligible for unauthorized user', async () => {
      mockForceCloseController.validateForceClose.callsFake(
        async (request, reply) => {
          reply.code(200).send({
            eligible: false,
            scenario: null,
            validationPath: 'AUTHORIZATION_FAILED',
            requiresPaymentCheck: false,
            pendingValidations: ['User authorization required'],
            impactSummary: {},
          });
        },
      );

      const response = await fastify.inject({
        method: 'POST',
        url: '/api/requisitions/123/validate-force-close',
        headers: {
          'x-access-token': 'valid-token',
        },
      });

      expect(response.statusCode).to.equal(200);
      const payload = JSON.parse(response.payload);
      expect(payload.eligible).to.be.false;
      expect(payload.validationPath).to.equal('AUTHORIZATION_FAILED');
    });

    it('should validate requisition ID parameter', async () => {
      const response = await fastify.inject({
        method: 'POST',
        url: '/api/requisitions/invalid-id/validate-force-close',
        headers: {
          'x-access-token': 'valid-token',
        },
      });

      expect(response.statusCode).to.equal(400);
    });
  });

  describe('POST /api/requisitions/:requisitionId/force-close', () => {
    const validRequestBody = {
      notes: 'Test force close notes for business reasons',
      confirmedScenario: 'ACTIVE_PO_PARTIAL_DELIVERY',
      acknowledgedImpacts: [
        'PO amounts will be updated to reflect delivered quantities only',
        'Unfulfilled quantities will be returned to GFQ',
      ],
    };

    it('should execute force close successfully', async () => {
      mockForceCloseController.forceCloseRequisition.callsFake(
        async (request, reply) => {
          reply.code(200).send({
            success: true,
            requisitionStatus: 'CLOSED',
            documentsAffected: [
              { type: 'requisition', id: 123, status: 'CLOSED' },
              { type: 'purchase_order', id: 1, status: 'CLOSED' },
            ],
            quantitiesReturned: {
              1: { itemName: 'Test Item', returnedQty: 5, type: 'OFM' },
            },
            poAdjustments: [
              {
                poId: 1,
                originalAmount: 1000,
                newAmount: 500,
                originalQuantity: 10,
                newQuantity: 5,
                systemNotes:
                  'Force Close: PO updated to reflect delivered quantities only',
              },
            ],
          });
        },
      );

      const response = await fastify.inject({
        method: 'POST',
        url: '/api/requisitions/123/force-close',
        headers: {
          'x-access-token': 'valid-token',
          'content-type': 'application/json',
        },
        payload: validRequestBody,
      });

      expect(response.statusCode).to.equal(200);
      const payload = JSON.parse(response.payload);
      expect(payload.success).to.be.true;
      expect(payload.requisitionStatus).to.equal('CLOSED');
      expect(payload.documentsAffected).to.have.length(2);
      expect(payload.poAdjustments).to.have.length(1);
    });

    it('should validate request body schema', async () => {
      const invalidRequestBody = {
        notes: '', // Empty notes should fail
        confirmedScenario: 'INVALID_SCENARIO',
        acknowledgedImpacts: [],
      };

      const response = await fastify.inject({
        method: 'POST',
        url: '/api/requisitions/123/force-close',
        headers: {
          'x-access-token': 'valid-token',
          'content-type': 'application/json',
        },
        payload: invalidRequestBody,
      });

      expect(response.statusCode).to.equal(400);
    });

    it('should require all mandatory fields', async () => {
      const incompleteRequestBody = {
        notes: 'Test notes',
        // Missing confirmedScenario and acknowledgedImpacts
      };

      const response = await fastify.inject({
        method: 'POST',
        url: '/api/requisitions/123/force-close',
        headers: {
          'x-access-token': 'valid-token',
          'content-type': 'application/json',
        },
        payload: incompleteRequestBody,
      });

      expect(response.statusCode).to.equal(400);
    });

    it('should validate scenario enum values', async () => {
      const invalidScenarioBody = {
        ...validRequestBody,
        confirmedScenario: 'INVALID_SCENARIO_TYPE',
      };

      const response = await fastify.inject({
        method: 'POST',
        url: '/api/requisitions/123/force-close',
        headers: {
          'x-access-token': 'valid-token',
          'content-type': 'application/json',
        },
        payload: invalidScenarioBody,
      });

      expect(response.statusCode).to.equal(400);
    });
  });

  describe('GET /api/requisitions/:requisitionId/force-close-history', () => {
    it('should retrieve force close history successfully', async () => {
      mockForceCloseController.getForceCloseHistory.callsFake(
        async (request, reply) => {
          reply.code(200).send({
            forceCloseLog: {
              id: 1,
              requisitionId: 123,
              userId: 1,
              scenarioType: 'ACTIVE_PO_PARTIAL_DELIVERY',
              validationPath: 'ACTIVE_PO_PATH',
              forceCloseNotes: 'Test force close notes',
              createdAt: '2024-01-01T00:00:00Z',
            },
            systemChanges: [
              {
                type: 'PO_AMOUNT_UPDATE',
                description: 'Updated PO amount from 1000 to 500',
                timestamp: '2024-01-01T00:00:00Z',
              },
            ],
            auditTrail: [
              {
                action: 'FORCE_CLOSE',
                details: { scenario: 'ACTIVE_PO_PARTIAL_DELIVERY' },
                timestamp: '2024-01-01T00:00:00Z',
              },
            ],
            impactedDocuments: [
              {
                type: 'purchase_order',
                id: 1,
                status: 'CLOSED',
                changes: { amount: { from: 1000, to: 500 } },
              },
            ],
          });
        },
      );

      const response = await fastify.inject({
        method: 'GET',
        url: '/api/requisitions/123/force-close-history',
        headers: {
          'x-access-token': 'valid-token',
        },
      });

      expect(response.statusCode).to.equal(200);
      const payload = JSON.parse(response.payload);
      expect(payload.forceCloseLog).to.exist;
      expect(payload.forceCloseLog.scenarioType).to.equal(
        'ACTIVE_PO_PARTIAL_DELIVERY',
      );
      expect(payload.systemChanges).to.have.length(1);
      expect(payload.auditTrail).to.have.length(1);
      expect(payload.impactedDocuments).to.have.length(1);
    });

    it('should return 404 for non-existent history', async () => {
      mockForceCloseController.getForceCloseHistory.callsFake(
        async (request, reply) => {
          reply.code(404).send({
            error: 'Force close history not found for requisition 123',
          });
        },
      );

      const response = await fastify.inject({
        method: 'GET',
        url: '/api/requisitions/123/force-close-history',
        headers: {
          'x-access-token': 'valid-token',
        },
      });

      expect(response.statusCode).to.equal(404);
      const payload = JSON.parse(response.payload);
      expect(payload.error).to.include('not found');
    });
  });
});
