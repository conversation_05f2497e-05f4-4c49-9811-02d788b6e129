#!/usr/bin/env node

/**
 * API Test script for force close functionality
 * This script tests the force close implementation by calling the API directly
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3001/api/v1';
const REQUISITION_ID = 1009;

// Test credentials
const credentials = {
  username: 'ronald',
  password: '4842#O2Kv',
};

async function login() {
  try {
    console.log('🔐 Logging in...');
    const response = await axios.post(`${BASE_URL}/auth/login`, credentials);
    console.log('✅ Login successful');
    return response.data.token;
  } catch (error) {
    console.error('❌ Login failed:', error.response?.data || error.message);
    throw error;
  }
}

async function validateForceClose(token) {
  try {
    console.log('🔍 Validating force close eligibility...');
    const response = await axios.post(
      `${BASE_URL}/requisitions/${REQUISITION_ID}/validate-force-close`,
      {},
      {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      },
    );
    console.log(
      '✅ Validation successful:',
      JSON.stringify(response.data, null, 2),
    );
    return response.data;
  } catch (error) {
    console.error(
      '❌ Validation failed:',
      error.response?.data || error.message,
    );
    throw error;
  }
}

async function executeForceClose(token, scenario) {
  try {
    console.log('🚀 Executing force close...');
    const forceCloseData = {
      notes:
        'Test force close - verifying implementation of RS notes, PO system notes, and document cancellation',
      confirmedScenario: scenario,
      acknowledgedImpacts: [],
    };

    const response = await axios.post(
      `${BASE_URL}/requisitions/${REQUISITION_ID}/force-close`,
      forceCloseData,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      },
    );
    console.log(
      '✅ Force close successful:',
      JSON.stringify(response.data, null, 2),
    );
    return response.data;
  } catch (error) {
    console.error(
      '❌ Force close failed:',
      error.response?.data || error.message,
    );
    throw error;
  }
}

async function main() {
  try {
    console.log('🧪 Starting Force Close API Test');
    console.log('================================');

    // Step 1: Login
    const token = await login();

    // Step 2: Validate force close eligibility
    const validation = await validateForceClose(token);

    if (!validation.isEligible) {
      console.log('❌ Requisition is not eligible for force close');
      console.log('Reason:', validation.reason);
      return;
    }

    // Step 3: Execute force close
    const result = await executeForceClose(token, validation.scenario);

    console.log('🎉 Force close API test completed successfully!');
    console.log('================================');
  } catch (error) {
    console.error('💥 Test failed:', error.message);
    process.exit(1);
  }
}

// Run the test
main();
